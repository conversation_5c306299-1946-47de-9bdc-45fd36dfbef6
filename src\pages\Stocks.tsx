import React, { useState, useMemo } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { formatCurrency } from '@/lib/currency';
import { 
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Search, Package, AlertTriangle, TrendingUp, BarChart3, Plus, Minus, History, RefreshCw, Bell } from 'lucide-react';
import { Label } from '@/components/ui/label';
import { Skeleton } from '@/components/ui/skeleton';
import { useNotifications } from '@/lib/notificationService';
import SyncTestButton from '@/components/SyncTestButton';
// import { ApiDebugger } from '@/components/debug/ApiDebugger'; // Temporairement désactivé
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { useToast } from '@/hooks/use-toast';
import { usePermissions, ProtectedComponent } from '@/contexts/PermissionsContext';
import {
  useInventory,
  useStockMovements,
  useCreateStockMovement,
  useLowStockAlerts,
  useProducts
} from '@/hooks/useApi';
import { useStockSync } from '@/hooks/useStockSync';
import StockSyncManager from '@/components/StockSyncManager';

// Interfaces pour les stocks
interface StockMovement {
  id: string;
  product_id: string;
  product_name: string;
  movement_type: 'in' | 'out' | 'adjustment';
  quantity: number;
  reason: string;
  created_at: string;
  created_by: string;
}

interface InventoryItem {
  id: string;
  product_id: string;
  product_name: string;
  category: string;
  current_stock: number;
  minimum_stock: number;
  purchase_price: number;
  selling_price: number;
  unit: string;
  last_updated: string;
}

const Stocks = () => {
  // Hooks pour les données API
  const { data: inventory, isLoading: inventoryLoading, error: inventoryError } = useInventory();
  const { data: stockMovements, isLoading: movementsLoading } = useStockMovements({ limit: 20 });
  const { data: lowStockAlerts, isLoading: alertsLoading } = useLowStockAlerts();
  const { data: products } = useProducts();
  const createStockMovement = useCreateStockMovement();

  // Hook pour la synchronisation
  const { autoSync } = useStockSync();

  // Hook pour les notifications
  const notifications = useNotifications();

  // Synchronisation automatique au chargement
  React.useEffect(() => {
    if (!inventoryLoading && !products) {
      // Si l'inventaire est chargé mais vide, et qu'on a des produits, synchroniser
      autoSync();
    }
  }, [inventoryLoading, inventory, products, autoSync]);

  // Vérification des alertes de stock faible et envoi de notifications
  React.useEffect(() => {
    if (inventory && inventory.length > 0) {
      const lowStockItems = inventory.filter((item: InventoryItem) =>
        item.current_stock <= item.minimum_stock && item.current_stock > 0
      );

      // Envoyer des notifications pour les articles en stock faible
      lowStockItems.forEach((item: InventoryItem) => {
        // Éviter de spammer les notifications - vérifier si on a déjà notifié récemment
        const lastNotified = localStorage.getItem(`last_notified_${item.product_id}`);
        const now = Date.now();
        const oneHour = 60 * 60 * 1000; // 1 heure en millisecondes

        if (!lastNotified || (now - parseInt(lastNotified)) > oneHour) {
          notifications.notifyLowStock(
            item.product_name,
            item.current_stock,
            item.minimum_stock
          );

          // Marquer comme notifié
          localStorage.setItem(`last_notified_${item.product_id}`, now.toString());
        }
      });
    }
  }, [inventory, notifications]);

  // État local
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('Toutes');
  const [stockFilter, setStockFilter] = useState('Tous');
  const [isMovementDialogOpen, setIsMovementDialogOpen] = useState(false);
  const [selectedProduct, setSelectedProduct] = useState<string>('');
  const [movementType, setMovementType] = useState<'in' | 'out' | 'adjustment'>('in');
  const [quantity, setQuantity] = useState('');
  const [reason, setReason] = useState('');
  const [showSyncManager, setShowSyncManager] = useState(false);

  const { canManageStocks } = usePermissions();
  const { toast } = useToast();

  // Catégories dynamiques
  const categories = useMemo(() => {
    const cats: string[] = ['Toutes'];
    if (inventory && Array.isArray(inventory)) {
      const categorySet = new Set<string>();
      inventory.forEach((item: any) => {
        if (item.category && typeof item.category === 'string') {
          categorySet.add(item.category);
        }
      });
      cats.push(...Array.from(categorySet));
    }
    return cats;
  }, [inventory]);

  // Combiner les données d'inventaire et de produits
  const combinedStockData = useMemo(() => {
    let inventoryList: InventoryItem[] = [];

    // Essayer d'abord l'API inventory
    if (inventory) {
      if (Array.isArray(inventory)) {
        inventoryList = inventory;
      } else if (inventory.results && Array.isArray(inventory.results)) {
        inventoryList = inventory.results;
      } else if (inventory.inventory && Array.isArray(inventory.inventory)) {
        inventoryList = inventory.inventory;
      }
    }

    // Si l'inventaire est vide, utiliser les données des produits
    if (inventoryList.length === 0 && products) {
      const productsList = Array.isArray(products) ? products : products.results || [];
      inventoryList = productsList.map((product: any) => ({
        id: product.id,
        product_id: product.id,
        product_name: product.name,
        category: product.category_name || product.category || 'Non classé',
        current_stock: product.current_stock || 0,
        minimum_stock: product.minimum_stock || 10,
        purchase_price: product.purchase_price || product.cost_price || 0,
        selling_price: product.selling_price || product.price || 0,
        unit: product.unit || 'unité',
        last_updated: product.updated_at || product.created_at || new Date().toISOString()
      }));
    }

    return inventoryList;
  }, [inventory, products]);

  // Filtrage des stocks
  const filteredStocks = useMemo(() => {
    return combinedStockData.filter((stock: InventoryItem) => {
      const matchesSearch = stock.product_name.toLowerCase().includes(searchTerm.toLowerCase());
      const matchesCategory = selectedCategory === 'Toutes' || stock.category === selectedCategory;

      let matchesStockFilter = true;
      if (stockFilter === 'Rupture') {
        matchesStockFilter = stock.current_stock <= 0;
      } else if (stockFilter === 'Stock bas') {
        matchesStockFilter = stock.current_stock > 0 && stock.current_stock <= stock.minimum_stock;
      } else if (stockFilter === 'Stock normal') {
        matchesStockFilter = stock.current_stock > stock.minimum_stock;
      }

      return matchesSearch && matchesCategory && matchesStockFilter;
    });
  }, [combinedStockData, searchTerm, selectedCategory, stockFilter]);

  const getStockStatus = (currentStock: number, minimumStock: number) => {
    if (currentStock <= 0) return { variant: 'destructive' as const, text: 'Rupture', icon: AlertTriangle };
    if (currentStock <= minimumStock) return { variant: 'secondary' as const, text: 'Stock bas', icon: AlertTriangle };
    return { variant: 'default' as const, text: 'En stock', icon: Package };
  };

  const getStockStats = () => {
    const total = combinedStockData.length;
    const rupture = combinedStockData.filter((s: InventoryItem) => s.current_stock <= 0).length;
    const stockBas = combinedStockData.filter((s: InventoryItem) => s.current_stock > 0 && s.current_stock <= s.minimum_stock).length;
    const stockNormal = combinedStockData.filter((s: InventoryItem) => s.current_stock > s.minimum_stock).length;

    return { total, rupture, stockBas, stockNormal };
  };

  const stats = getStockStats();

  const getTotalValue = () => {
    return combinedStockData.reduce((sum: number, stock: InventoryItem) => sum + (stock.current_stock * stock.purchase_price), 0);
  };

  const stockFilters = ['Tous', 'Rupture', 'Stock bas', 'Stock normal'];

  // Fonction pour créer un mouvement de stock
  const handleCreateMovement = async () => {
    if (!selectedProduct || !quantity || !reason) {
      toast({
        title: "Erreur",
        description: "Veuillez remplir tous les champs.",
        variant: "destructive",
      });
      return;
    }

    try {
      await createStockMovement.mutateAsync({
        product_id: selectedProduct,
        movement_type: movementType,
        quantity: Number(quantity),
        reason: reason
      });

      // Reset form
      setSelectedProduct('');
      setQuantity('');
      setReason('');
      setIsMovementDialogOpen(false);
    } catch (error) {
      console.error('Erreur lors de la création du mouvement:', error);
    }
  };

  // Composant de loading
  const LoadingTable = () => (
    <div className="space-y-3">
      {[...Array(5)].map((_, i) => (
        <div key={i} className="flex items-center space-x-4">
          <Skeleton className="h-4 w-[200px]" />
          <Skeleton className="h-4 w-[100px]" />
          <Skeleton className="h-4 w-[80px]" />
          <Skeleton className="h-4 w-[80px]" />
          <Skeleton className="h-4 w-[100px]" />
          <Skeleton className="h-4 w-[80px]" />
          <Skeleton className="h-4 w-[120px]" />
          <Skeleton className="h-4 w-[60px]" />
        </div>
      ))}
    </div>
  );

  return (
    <div className="space-y-6">
      {/* Debug Info - Temporairement désactivé */}
      {/* <ApiDebugger /> */}

      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Gestion des Stocks</h1>
          <p className="text-muted-foreground">
            Suivez les niveaux de stock et les alertes
          </p>
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={() => setShowSyncManager(!showSyncManager)}
          >
            <RefreshCw className="w-4 h-4 mr-2" />
            {showSyncManager ? 'Masquer Sync' : 'Synchronisation'}
          </Button>
          <Button
            variant="outline"
            onClick={() => {
              // Tester une notification d'alerte de stock
              notifications.notifyLowStock('Produit Test', 3, 10);
              toast({
                title: "🔔 Notification testée",
                description: "Alerte de stock faible envoyée",
              });
            }}
          >
            <Bell className="w-4 h-4 mr-2" />
            Test Notification
          </Button>
          <SyncTestButton />
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-5">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Produits</CardTitle>
            <Package className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.total}</div>
            <p className="text-xs text-muted-foreground">produits suivis</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">En Rupture</CardTitle>
            <AlertTriangle className="h-4 w-4 text-destructive" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-destructive">{stats.rupture}</div>
            <p className="text-xs text-muted-foreground">urgent</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Stock Bas</CardTitle>
            <AlertTriangle className="h-4 w-4 text-warning" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-warning">{stats.stockBas}</div>
            <p className="text-xs text-muted-foreground">à surveiller</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Stock Normal</CardTitle>
            <TrendingUp className="h-4 w-4 text-success" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-success">{stats.stockNormal}</div>
            <p className="text-xs text-muted-foreground">en forme</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Valeur Stock</CardTitle>
            <BarChart3 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {getTotalValue().toLocaleString()} BIF
            </div>
            <p className="text-xs text-muted-foreground">prix d'achat</p>
          </CardContent>
        </Card>
      </div>

      {/* Gestionnaire de synchronisation */}
      {showSyncManager && (
        <StockSyncManager />
      )}

      {/* Message informatif sur la source des données */}
      {combinedStockData.length > 0 && !inventory?.results?.length && (
        <Card className="border-blue-200 bg-blue-50">
          <CardContent className="pt-4">
            <div className="flex items-center gap-2 text-blue-700">
              <Package className="h-4 w-4" />
              <span className="text-sm font-medium">
                Données affichées depuis la table Produits
              </span>
            </div>
            <p className="text-xs text-blue-600 mt-1">
              Les données d'inventaire spécialisées ne sont pas encore disponibles.
              Les stocks affichés proviennent directement de la table des produits.
            </p>
          </CardContent>
        </Card>
      )}

      {/* Filters */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Rechercher un produit..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <Select value={selectedCategory} onValueChange={setSelectedCategory}>
              <SelectTrigger className="w-full sm:w-[200px]">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {categories.map((category) => (
                  <SelectItem key={category} value={category}>
                    {category}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Select value={stockFilter} onValueChange={setStockFilter}>
              <SelectTrigger className="w-full sm:w-[200px]">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {stockFilters.map((filter) => (
                  <SelectItem key={filter} value={filter}>
                    {filter}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Stocks Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Package className="w-5 h-5" />
            État des Stocks
          </CardTitle>
          <CardDescription>
            {filteredStocks.length} produit(s) trouvé(s)
          </CardDescription>
        </CardHeader>
        <CardContent>
          {inventoryLoading ? (
            <LoadingTable />
          ) : filteredStocks.length === 0 ? (
            <div className="text-center py-8">
              <Package className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">Aucun produit trouvé</h3>
              <p className="text-muted-foreground">
                {searchTerm || selectedCategory !== 'Toutes' || stockFilter !== 'Tous'
                  ? 'Aucun produit ne correspond à vos critères de recherche.'
                  : 'Aucun produit en stock pour le moment.'
                }
              </p>
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Produit</TableHead>
                  <TableHead>Catégorie</TableHead>
                  <TableHead>Stock Actuel</TableHead>
                  <TableHead>Stock Minimum</TableHead>
                  <TableHead>Prix d'achat</TableHead>
                  <TableHead>Statut</TableHead>
                  <TableHead>Valeur Stock</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredStocks.map((stock: InventoryItem) => {
                  const status = getStockStatus(stock.current_stock, stock.minimum_stock);
                  const StatusIcon = status.icon;
                  return (
                    <TableRow key={stock.id}>
                      <TableCell className="font-medium">{stock.product_name}</TableCell>
                      <TableCell>{stock.category}</TableCell>
                      <TableCell>
                        <span className={`font-bold ${
                          stock.current_stock <= 0 ? 'text-destructive' :
                          stock.current_stock <= stock.minimum_stock ? 'text-warning' : 'text-success'
                        }`}>
                          {stock.current_stock} {stock.unit}
                        </span>
                    </TableCell>
                    <TableCell className="text-muted-foreground">
                      {stock.minimum_stock} {stock.unit}
                    </TableCell>
                    <TableCell>
                      {formatCurrency(stock.purchase_price)}
                    </TableCell>
                    <TableCell>
                      <Badge variant={status.variant} className="flex items-center gap-1 w-fit">
                        <StatusIcon className="w-3 h-3" />
                        {status.text}
                      </Badge>
                    </TableCell>
                    <TableCell className="font-medium">
                      {stock.current_stock > 0
                        ? formatCurrency(stock.current_stock * stock.purchase_price)
                        : '-'
                      }
                    </TableCell>
                    <TableCell>
                      <ProtectedComponent permission="stocks.update">
                        <div className="flex gap-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => {
                              setSelectedProduct(stock.product_id);
                              setMovementType('in');
                              setIsMovementDialogOpen(true);
                            }}
                          >
                            <Plus className="w-4 h-4" />
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => {
                              setSelectedProduct(stock.product_id);
                              setMovementType('out');
                              setIsMovementDialogOpen(true);
                            }}
                          >
                            <Minus className="w-4 h-4" />
                          </Button>
                        </div>
                      </ProtectedComponent>
                    </TableCell>
                  </TableRow>
                );
              })}
            </TableBody>
          </Table>
          )}
        </CardContent>
      </Card>

      {/* Dialog pour les mouvements de stock */}
      <Dialog open={isMovementDialogOpen} onOpenChange={setIsMovementDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>
              {movementType === 'in' ? 'Entrée de stock' :
               movementType === 'out' ? 'Sortie de stock' : 'Ajustement de stock'}
            </DialogTitle>
            <DialogDescription>
              Enregistrer un mouvement de stock pour le produit sélectionné.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            <div>
              <Label htmlFor="product">Produit</Label>
              <Select value={selectedProduct} onValueChange={setSelectedProduct}>
                <SelectTrigger>
                  <SelectValue placeholder="Sélectionnez un produit" />
                </SelectTrigger>
                <SelectContent>
                  {products && Array.isArray(products) ? products.map((product: any) => (
                    <SelectItem key={product.id} value={product.id}>
                      {product.name}
                    </SelectItem>
                  )) : products?.results && Array.isArray(products.results) ? products.results.map((product: any) => (
                    <SelectItem key={product.id} value={product.id}>
                      {product.name}
                    </SelectItem>
                  )) : null}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="quantity">Quantité</Label>
              <Input
                id="quantity"
                type="number"
                value={quantity}
                onChange={(e) => setQuantity(e.target.value)}
                placeholder="Entrez la quantité"
              />
            </div>

            <div>
              <Label htmlFor="reason">Motif</Label>
              <Input
                id="reason"
                value={reason}
                onChange={(e) => setReason(e.target.value)}
                placeholder="Motif du mouvement"
              />
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsMovementDialogOpen(false)}>
              Annuler
            </Button>
            <Button
              onClick={handleCreateMovement}
              disabled={createStockMovement.isPending}
            >
              {createStockMovement.isPending ? 'Enregistrement...' : 'Enregistrer'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default Stocks;