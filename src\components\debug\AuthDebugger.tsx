import React from 'react';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useAuth } from '@/contexts/AuthContextBackend';
import { usePermissions } from '@/contexts/PermissionsContext';

export const AuthDebugger = () => {
  const { user, isAuthenticated } = useAuth();
  const { userRole, canManageUsers, permissions } = usePermissions();

  return (
    <Card className="mb-4 border-orange-200 bg-orange-50">
      <CardHeader>
        <CardTitle className="text-orange-800">🔍 Debug Authentification</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid gap-4 md:grid-cols-2">
          <div>
            <h4 className="font-medium mb-2">AuthContext</h4>
            <div className="space-y-1 text-sm">
              <p><strong>isAuthenticated:</strong> {isAuthenticated ? 'Oui' : 'Non'}</p>
              <p><strong>user:</strong> {user ? JSON.stringify(user, null, 2) : 'null'}</p>
            </div>
          </div>
          
          <div>
            <h4 className="font-medium mb-2">PermissionsContext</h4>
            <div className="space-y-1 text-sm">
              <p><strong>userRole:</strong> {userRole || 'undefined'}</p>
              <p><strong>canManageUsers:</strong> {canManageUsers ? 'Oui' : 'Non'}</p>
              <p><strong>permissions:</strong> {permissions ? permissions.length : 0} permissions</p>
            </div>
          </div>
        </div>
        
        <div>
          <h4 className="font-medium mb-2">Permissions détaillées</h4>
          <div className="flex flex-wrap gap-1">
            {permissions && permissions.length > 0 ? (
              permissions.map((perm, index) => (
                <Badge key={index} variant="outline" className="text-xs">
                  {perm}
                </Badge>
              ))
            ) : (
              <Badge variant="secondary">Aucune permission</Badge>
            )}
          </div>
        </div>
        
        <div className="p-3 bg-white rounded border">
          <h4 className="font-medium mb-2">Condition d'accès Users</h4>
          <p className="text-sm">
            <strong>userRole !== 'Admin':</strong> {userRole !== 'Admin' ? 'true' : 'false'}
          </p>
          <p className="text-sm">
            <strong>!canManageUsers:</strong> {!canManageUsers ? 'true' : 'false'}
          </p>
          <p className="text-sm">
            <strong>Accès bloqué:</strong> {(userRole !== 'Admin' && !canManageUsers) ? 'OUI' : 'NON'}
          </p>
        </div>
      </CardContent>
    </Card>
  );
};
