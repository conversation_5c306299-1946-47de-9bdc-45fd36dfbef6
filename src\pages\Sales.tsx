import React, { useState, useCallback, useMemo } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { 
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { 
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import CustomSelect from '@/components/ui/custom-select';
import PaymentMethodSelect from '@/components/ui/payment-method-select';
import { Plus, ShoppingCart, Users, Receipt, Minus, X, AlertTriangle } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { formatCurrency } from '@/lib/currency';
import { Skeleton } from '@/components/ui/skeleton';
import { useProducts, useCreateSale, useSales, useTables } from '@/hooks/useApi';

// Interface pour les éléments du panier
interface CartItem {
  id: string;
  name: string;
  price: number;
  quantity: number;
  stock: number;
}

// Interface pour une vente
interface Sale {
  id: string;
  table_number: string;
  items: CartItem[];
  total_amount: number;
  created_at: string;
  served_by: string;
}

const Sales = () => {
  // Hooks pour les données API
  const { data: productsResponse, isLoading: productsLoading, error: productsError } = useProducts();
  const { data: tablesData, isLoading: tablesLoading } = useTables();
  const { data: recentSales, isLoading: salesLoading } = useSales({ limit: 20 });
  const createSale = useCreateSale();

  // Extraire les données de la réponse paginée
  const products = useMemo(() => {
    if (!productsResponse) return [];
    return productsResponse?.results || (Array.isArray(productsResponse) ? productsResponse : []);
  }, [productsResponse]);

  // État local
  const [currentSale, setCurrentSale] = useState<CartItem[]>([]);
  const [selectedTable, setSelectedTable] = useState('');
  const [customerName, setCustomerName] = useState('');
  const [paymentMethod, setPaymentMethod] = useState('cash');
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const { toast } = useToast();

  // Tables dynamiques depuis l'API - avec gestion d'erreur améliorée
  const tables = useMemo(() => {
    if (!tablesData || tablesLoading) return [];

    // Gérer différents formats de réponse de l'API
    let tablesList = [];
    try {
      if (Array.isArray(tablesData)) {
        tablesList = tablesData;
      } else if (tablesData.results && Array.isArray(tablesData.results)) {
        tablesList = tablesData.results;
      }

      return tablesList
        .filter(table => table && table.is_active)
        .map(table => `Table ${table.number}`)
        .sort();
    } catch (error) {
      console.error('Error processing tables data:', error);
      return [];
    }
  }, [tablesData, tablesLoading]);

  const addToCart = useCallback((product: any) => {
    if (!product || product.current_stock <= 0) {
      toast({
        title: "Stock insuffisant",
        description: `${product?.name || 'Produit'} n'est plus en stock.`,
        variant: "destructive",
      });
      return;
    }

    setCurrentSale(prev => {
      const existingItem = prev.find(item => item.id === product.id);
      if (existingItem) {
        if (existingItem.quantity >= product.current_stock) {
          toast({
            title: "Stock insuffisant",
            description: `Quantité maximale atteinte pour ${product.name}.`,
            variant: "destructive",
          });
          return prev;
        }
        return prev.map(item =>
          item.id === product.id
            ? { ...item, quantity: item.quantity + 1 }
            : item
        );
      } else {
        return [...prev, {
          id: product.id,
          name: product.name,
          price: product.selling_price,
          quantity: 1,
          stock: product.current_stock
        }];
      }
    });
  }, [toast]);

  const removeFromCart = useCallback((productId: string) => {
    setCurrentSale(prev => prev.filter(item => item.id !== productId));
  }, []);

  const clearCart = useCallback(() => setCurrentSale([]), []);

  const getCartTotal = useCallback(() => 
    currentSale.reduce((sum, item) => sum + (item.price * item.quantity), 0), 
    [currentSale]
  );

  const completeSale = useCallback(async () => {
    if (!selectedTable || currentSale.length === 0) {
      toast({
        title: "Erreur",
        description: "Veuillez sélectionner une table et ajouter des articles.",
        variant: "destructive",
      });
      return;
    }

    try {
      // Extraire l'ID de la table depuis le string "Table X"
      const tableNumber = selectedTable.replace('Table ', '');
      
      // Trouver l'objet table correspondant
      const tableObject = tablesData?.results?.find(table => table.number === tableNumber) ||
                         (Array.isArray(tablesData) ? tablesData.find(table => table.number === tableNumber) : null);
      
      if (!tableObject) {
        toast({
          title: "Erreur",
          description: "Table introuvable",
          variant: "destructive",
        });
        return;
      }

      const saleData = {
        table: tableObject.id, // ID de la table
        items: currentSale.map(item => ({
          product: item.id, // ID du produit
          quantity: item.quantity
        })),
        payment_method: paymentMethod,
        notes: customerName ? `Client: ${customerName}` : undefined
      };

      await createSale.mutateAsync(saleData);
      
      // Réinitialiser le formulaire après succès
      setCurrentSale([]);
      setSelectedTable('');
      setCustomerName('');
      setPaymentMethod('cash');
      
      toast({
        title: "Succès",
        description: "Vente enregistrée avec succès",
      });
    } catch (error) {
      console.error('Error completing sale:', error);
      toast({
        title: "Erreur",
        description: "Erreur lors de l'enregistrement de la vente",
        variant: "destructive",
      });
    }
  }, [selectedTable, currentSale, customerName, paymentMethod, tablesData, createSale, toast]);

  const printReceipt = useCallback(() => {
    if (currentSale.length === 0) {
      toast({
        title: "Erreur",
        description: "Aucun article à imprimer",
        variant: "destructive",
      });
      return;
    }

    // Créer le contenu HTML du reçu
    const receiptHTML = `
      <!DOCTYPE html>
      <html>
        <head>
          <title>Reçu de Vente</title>
          <style>
            body {
              font-family: monospace;
              max-width: 300px;
              margin: 0 auto;
              padding: 20px;
              font-size: 12px;
            }
            .header { text-align: center; margin-bottom: 20px; }
            .divider { border-top: 1px dashed #000; margin: 10px 0; }
            .item { display: flex; justify-content: space-between; margin: 5px 0; }
            .total { font-weight: bold; font-size: 14px; text-align: center; margin-top: 15px; }
            .footer { text-align: center; margin-top: 20px; font-size: 10px; }
          </style>
        </head>
        <body>
          <div class="header">
            <h2>BarStock Wise</h2>
            <p>Bujumbura, Burundi</p>
          </div>
          <div class="divider"></div>
          <p><strong>Table:</strong> ${selectedTable || 'Emporter'}</p>
          <p><strong>Date:</strong> ${new Date().toLocaleString('fr-FR')}</p>
          <div class="divider"></div>
          <h3>Articles:</h3>
          ${currentSale.map(item => `
            <div class="item">
              <span>${item.name} x${item.quantity}</span>
              <span>${formatCurrency(item.price * item.quantity)}</span>
            </div>
          `).join('')}
          <div class="divider"></div>
          <div class="total">
            TOTAL: ${formatCurrency(getCartTotal())}
          </div>
          <div class="footer">
            <p>Merci de votre visite !</p>
            <p>Reçu généré le ${new Date().toLocaleString('fr-FR')}</p>
          </div>
        </body>
      </html>
    `;

    // Ouvrir dans une nouvelle fenêtre et imprimer
    const printWindow = window.open('', '_blank', 'width=400,height=600');
    if (printWindow) {
      printWindow.document.write(receiptHTML);
      printWindow.document.close();

      // Attendre que le contenu soit chargé puis imprimer
      setTimeout(() => {
        printWindow.print();
        printWindow.close();
      }, 500);
    }

    toast({
      title: "Impression en cours",
      description: "Le reçu est en cours d'impression.",
    });
  }, [currentSale, selectedTable, getCartTotal, toast]);

  const LoadingProducts = () => (
    <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
      {[...Array(8)].map((_, i) => (
        <Card key={`loading-${i}`}>
          <CardContent className="p-4">
            <Skeleton className="h-4 w-24 mb-2" />
            <Skeleton className="h-6 w-16 mb-2" />
            <Skeleton className="h-3 w-20" />
          </CardContent>
        </Card>
      ))}
    </div>
  );

  // Gestion des erreurs
  if (productsError) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <AlertTriangle className="h-12 w-12 text-destructive mx-auto mb-4" />
          <h3 className="text-lg font-semibold mb-2">Erreur de chargement</h3>
          <p className="text-muted-foreground">
            Impossible de charger les produits. Vérifiez votre connexion.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Point de Vente</h1>
          <p className="text-muted-foreground">
            Enregistrez les commandes et générez les factures
          </p>
        </div>
        
        {currentSale.length > 0 && (
          <div className="flex gap-2">
            <Button variant="outline" onClick={printReceipt}>
              <Receipt className="w-4 h-4 mr-2" />
              Imprimer Facture
            </Button>
            <Button
              onClick={completeSale}
              className="bg-gradient-to-r from-primary to-accent"
            >
              Finaliser ({formatCurrency(getCartTotal())})
            </Button>
          </div>
        )}
      </div>

      <div className="grid gap-6 lg:grid-cols-3">
        {/* Products Grid */}
        <div className="lg:col-span-2">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <ShoppingCart className="w-5 h-5" />
                Produits Disponibles
              </CardTitle>
              <CardDescription>
                Cliquez sur un produit pour l'ajouter à la vente
              </CardDescription>
            </CardHeader>
            <CardContent>
              {productsLoading ? (
                <div className="grid gap-3 sm:grid-cols-2 lg:grid-cols-3">
                  {[...Array(6)].map((_, i) => (
                    <Card key={`product-loading-${i}`} className="animate-pulse">
                      <CardContent className="p-4">
                        <div className="text-center">
                          <Skeleton className="h-4 w-20 mx-auto mb-1" />
                          <Skeleton className="h-6 w-16 mx-auto mb-2" />
                          <Skeleton className="h-4 w-12 mx-auto" />
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              ) : products && products.length > 0 ? (
                <div className="grid gap-3 sm:grid-cols-2 lg:grid-cols-3">
                  {products.map((product: any) => (
                    <Card 
                      key={`product-${product.id}`}
                      className={`cursor-pointer transition-all hover:shadow-md ${
                        product.current_stock <= 0 ? 'opacity-50' : 'hover:scale-105'
                      }`}
                      onClick={() => product.current_stock > 0 && addToCart(product)}
                    >
                      <CardContent className="p-4">
                        <div className="text-center">
                          <h3 className="font-medium text-sm mb-1">{product.name}</h3>
                          <p className="text-lg font-bold text-primary">
                            {formatCurrency(product.selling_price)}
                          </p>
                          <Badge 
                            variant={product.current_stock <= 0 ? "destructive" : product.current_stock < 10 ? "secondary" : "default"}
                            className="mt-2 text-xs"
                          >
                            Stock: {product.current_stock}
                          </Badge>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <ShoppingCart className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <h3 className="text-lg font-semibold mb-2">Aucun produit disponible</h3>
                  <p className="text-sm text-muted-foreground">
                    {productsError ? "Erreur lors du chargement des produits" : "Ajoutez des produits pour commencer"}
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Current Sale */}
        <div>
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Receipt className="w-5 h-5" />
                Commande en cours
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Table/Customer Selection */}
              <div className="space-y-3">
                <div>
                  <Label htmlFor="table">Table</Label>
                  <CustomSelect
                    value={selectedTable}
                    onValueChange={setSelectedTable}
                    placeholder="Sélectionnez une table"
                    options={tables.map(table => ({ value: table, label: table }))}
                    disabled={tablesLoading}
                  />
                </div>
                
                <div>
                  <Label htmlFor="payment">Mode de paiement</Label>
                  <PaymentMethodSelect
                    value={paymentMethod}
                    onValueChange={setPaymentMethod}
                    disabled={currentSale.length === 0}
                  />
                </div>
                
              </div>

              {/* Cart Items */}
              {currentSale.length > 0 ? (
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <h4 className="font-medium text-sm">Articles:</h4>
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={clearCart}
                      className="h-6 text-xs text-muted-foreground hover:text-destructive"
                    >
                      <X className="w-3 h-3 mr-1" />
                      Vider
                    </Button>
                  </div>
                  {currentSale.map((item) => (
                    <div key={`cart-item-${item.id}`} className="flex items-center justify-between p-2 bg-muted/50 rounded">
                      <div className="flex-1">
                        <p className="font-medium text-sm">{item.name}</p>
                        <p className="text-xs text-muted-foreground">
                          {formatCurrency(item.price)} × {item.quantity}
                        </p>
                      </div>
                      <div className="flex items-center gap-1">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => removeFromCart(item.id)}
                          className="h-6 w-6 p-0"
                        >
                          <Minus className="w-3 h-3" />
                        </Button>
                        <span className="mx-2 text-sm font-medium">{item.quantity}</span>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => addToCart({
                            id: item.id,
                            name: item.name,
                            selling_price: item.price,
                            current_stock: item.stock
                          })}
                          className="h-6 w-6 p-0"
                          disabled={item.quantity >= item.stock}
                        >
                          <Plus className="w-3 h-3" />
                        </Button>
                      </div>
                      <div className="ml-2 text-right">
                        <p className="font-bold text-sm">
                          {formatCurrency(item.price * item.quantity)}
                        </p>
                      </div>
                    </div>
                  ))}

                  <div className="border-t pt-2 mt-3">
                    <div className="flex justify-between items-center">
                      <span className="font-bold">TOTAL:</span>
                      <span className="font-bold text-lg text-primary">
                        {formatCurrency(getCartTotal())}
                      </span>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  <ShoppingCart className="w-8 h-8 mx-auto mb-2 opacity-50" />
                  <p className="text-sm">Aucun article sélectionné</p>
                  <p className="text-xs">Cliquez sur un produit pour commencer</p>
                </div>
              )}

              {/* Action Buttons */}
              {currentSale.length > 0 && (
                <div className="space-y-2 pt-4 border-t">
                  <Button
                    onClick={completeSale}
                    className="w-full"
                    disabled={!selectedTable || createSale.isPending}
                  >
                    {createSale.isPending ? 'Enregistrement...' : 'Finaliser la vente'}
                  </Button>
                  <Button
                    onClick={printReceipt}
                    variant="outline"
                    className="w-full"
                  >
                    Imprimer le reçu
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default Sales;