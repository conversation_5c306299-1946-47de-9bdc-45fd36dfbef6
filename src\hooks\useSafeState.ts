import { useState, useEffect, useRef } from 'react';

export function useSafeState<T>(initialValue: T) {
  const [state, setState] = useState<T>(initialValue);
  const isMountedRef = useRef(true);

  useEffect(() => {
    isMountedRef.current = true;
    return () => {
      isMountedRef.current = false;
    };
  }, []);

  const safeSetState = (value: T | ((prev: T) => T)) => {
    if (isMountedRef.current) {
      setState(value);
    }
  };

  return [state, safeSetState] as const;
}

export function useSafeAsyncState<T>(initialValue: T) {
  const [state, setState] = useSafeState<T>(initialValue);
  const [isLoading, setIsLoading] = useSafeState(false);
  const [error, setError] = useSafeState<Error | null>(null);

  const executeAsync = async (asyncFn: () => Promise<T>) => {
    try {
      setIsLoading(true);
      setError(null);
      const result = await asyncFn();
      setState(result);
      return result;
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Unknown error');
      setError(error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  return {
    state,
    setState,
    isLoading,
    error,
    executeAsync
  };
} 