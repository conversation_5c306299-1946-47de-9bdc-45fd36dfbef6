/**
 * Utilitaires pour la gestion des valeurs numériques
 * Spécialement conçu pour gérer les DecimalField de Django qui sont sérialisés comme des chaînes
 */

/**
 * Convertit une valeur (string, number, null, undefined) en nombre sûr
 * @param value - La valeur à convertir
 * @returns Un nombre valide (0 si la conversion échoue)
 */
export const parseNumericValue = (value: any): number => {
  if (value === null || value === undefined) return 0;
  const parsed = parseFloat(value.toString());
  return isNaN(parsed) ? 0 : parsed;
};

/**
 * Convertit une valeur en nombre entier sûr
 * @param value - La valeur à convertir
 * @returns Un entier valide (0 si la conversion échoue)
 */
export const parseIntegerValue = (value: any): number => {
  if (value === null || value === undefined) return 0;
  const parsed = parseInt(value.toString(), 10);
  return isNaN(parsed) ? 0 : parsed;
};

/**
 * Calcule la somme d'un tableau de valeurs numériques
 * @param values - Tableau de valeurs à additionner
 * @returns La somme totale
 */
export const sumNumericValues = (values: any[]): number => {
  return values.reduce((sum, value) => sum + parseNumericValue(value), 0);
};

/**
 * Calcule la moyenne d'un tableau de valeurs numériques
 * @param values - Tableau de valeurs
 * @returns La moyenne (0 si le tableau est vide)
 */
export const averageNumericValues = (values: any[]): number => {
  if (values.length === 0) return 0;
  return sumNumericValues(values) / values.length;
};

/**
 * Formate un nombre pour l'affichage avec gestion des valeurs nulles
 * @param value - La valeur à formater
 * @param decimals - Nombre de décimales (défaut: 2)
 * @returns Le nombre formaté
 */
export const formatNumericValue = (value: any, decimals: number = 2): string => {
  const num = parseNumericValue(value);
  return num.toFixed(decimals);
};

/**
 * Vérifie si une valeur numérique est positive
 * @param value - La valeur à vérifier
 * @returns true si la valeur est positive
 */
export const isPositiveValue = (value: any): boolean => {
  return parseNumericValue(value) > 0;
};

/**
 * Convertit un pourcentage en nombre décimal
 * @param percentage - Le pourcentage (ex: 15 pour 15%)
 * @returns Le nombre décimal (ex: 0.15)
 */
export const percentageToDecimal = (percentage: any): number => {
  return parseNumericValue(percentage) / 100;
};

/**
 * Convertit un nombre décimal en pourcentage
 * @param decimal - Le nombre décimal (ex: 0.15)
 * @returns Le pourcentage (ex: 15)
 */
export const decimalToPercentage = (decimal: any): number => {
  return parseNumericValue(decimal) * 100;
};
