import React, { useState } from 'react';

const LoginTest = () => {
  console.log('LoginTest component rendering');
  console.log('React object:', React);
  console.log('useState function:', useState);
  
  const [test, setTest] = useState('test');
  
  return (
    <div>
      <h1>Login Test Component</h1>
      <p>Test state: {test}</p>
      <button onClick={() => setTest('updated')}>Update Test</button>
    </div>
  );
};

export default LoginTest;
