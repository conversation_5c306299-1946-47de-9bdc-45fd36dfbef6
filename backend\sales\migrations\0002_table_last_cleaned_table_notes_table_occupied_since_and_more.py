# Generated by Django 5.2.4 on 2025-07-31 15:15

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('sales', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name='table',
            name='last_cleaned',
            field=models.DateTimeField(blank=True, null=True, verbose_name='Dernier nettoyage'),
        ),
        migrations.AddField(
            model_name='table',
            name='notes',
            field=models.TextField(blank=True, null=True, verbose_name='Notes'),
        ),
        migrations.AddField(
            model_name='table',
            name='occupied_since',
            field=models.DateTimeField(blank=True, null=True, verbose_name='Occupée depuis'),
        ),
        migrations.AddField(
            model_name='table',
            name='updated_at',
            field=models.DateTimeField(auto_now=True, verbose_name='Date de modification'),
        ),
        migrations.CreateModel(
            name='TableReservation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('customer_name', models.CharField(max_length=100, verbose_name='Nom du client')),
                ('customer_phone', models.CharField(blank=True, max_length=20, null=True, verbose_name='Téléphone')),
                ('customer_email', models.EmailField(blank=True, max_length=254, null=True, verbose_name='Email')),
                ('party_size', models.PositiveIntegerField(verbose_name='Nombre de personnes')),
                ('reservation_date', models.DateField(verbose_name='Date de réservation')),
                ('reservation_time', models.TimeField(verbose_name='Heure de réservation')),
                ('duration_minutes', models.PositiveIntegerField(default=120, verbose_name='Durée prévue (minutes)')),
                ('status', models.CharField(choices=[('pending', 'En attente'), ('confirmed', 'Confirmée'), ('seated', 'Installée'), ('completed', 'Terminée'), ('cancelled', 'Annulée'), ('no_show', 'Absent')], default='pending', max_length=20, verbose_name='Statut')),
                ('special_requests', models.TextField(blank=True, null=True, verbose_name='Demandes spéciales')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='Notes internes')),
                ('seated_at', models.DateTimeField(blank=True, null=True, verbose_name='Installé à')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Date de création')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Date de modification')),
                ('confirmed_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='confirmed_reservations', to=settings.AUTH_USER_MODEL, verbose_name='Confirmé par')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='created_reservations', to=settings.AUTH_USER_MODEL, verbose_name='Créé par')),
                ('table', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='reservations', to='sales.table', verbose_name='Table')),
            ],
            options={
                'verbose_name': 'Réservation',
                'verbose_name_plural': 'Réservations',
                'ordering': ['reservation_date', 'reservation_time'],
                'unique_together': {('table', 'reservation_date', 'reservation_time')},
            },
        ),
    ]
