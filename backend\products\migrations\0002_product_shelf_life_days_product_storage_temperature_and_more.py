# Generated by Django 5.2.4 on 2025-07-31 13:28

from decimal import Decimal
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('products', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='product',
            name='shelf_life_days',
            field=models.PositiveIntegerField(blank=True, help_text='Durée de conservation en jours', null=True, verbose_name='Durée de conservation (jours)'),
        ),
        migrations.AddField(
            model_name='product',
            name='storage_temperature',
            field=models.CharField(blank=True, help_text='Température de stockage recommandée', max_length=20, null=True, verbose_name='Température de stockage'),
        ),
        migrations.AddField(
            model_name='product',
            name='waste_percentage',
            field=models.DecimalField(decimal_places=2, default=Decimal('5.00'), help_text='Pourcentage de perte moyen pour ce produit', max_digits=5, verbose_name='% de perte moyen'),
        ),
    ]
