import { useState, useMemo, FormEvent } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { Plus, Search, Edit, Trash2, AlertTriangle, Package } from 'lucide-react';
import { useProducts, useProductCategories, useCreateProduct, useUpdateProduct, useDeleteProduct } from '@/hooks/useApi';
import { useToast } from '@/components/ui/use-toast';
import { usePermissions, ProtectedComponent } from '@/contexts/PermissionsContext';
import { Product } from '@/types';
import { formatCurrency } from '@/lib/currency';

const Products = () => {
  // Hooks pour les données API
  const { data: productsResponse, isLoading: productsLoading, error: productsError } = useProducts();

  console.log('Products page state:', {
    productsResponse,
    productsLoading,
    productsError,
  });
  const { data: categoriesResponse, isLoading: categoriesLoading } = useProductCategories();
  const createProduct = useCreateProduct();
  const updateProduct = useUpdateProduct();
  const deleteProduct = useDeleteProduct();

  // État local
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('Toutes');
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editingProduct, setEditingProduct] = useState<Product | null>(null);
  const { toast } = useToast();
  const { canManageProducts, canDelete, hasPermission } = usePermissions();

  const [formData, setFormData] = useState({
    name: '',
    category: '',
    purchase_price: '',
    selling_price: '',
    current_stock: '',
    minimum_stock: '',
    unit: 'unité',
    description: ''
  });

  // Extraire les données de la réponse paginée
  const products = productsResponse?.results || (Array.isArray(productsResponse) ? productsResponse : []);
  const categories = categoriesResponse?.results || (Array.isArray(categoriesResponse) ? categoriesResponse : []);

  // Filtrage des produits
  const filteredProducts = useMemo(() => {
    if (!products) return [];
    return products.filter((product: Product) => {
      const matchesSearch = product.name.toLowerCase().includes(searchTerm.toLowerCase());
      const matchesCategory = selectedCategory === 'Toutes' || product.category === selectedCategory;
      return matchesSearch && matchesCategory;
    });
  }, [products, searchTerm, selectedCategory]);

  // Liste des catégories avec "Toutes"
  const categoryOptions = useMemo(() => {
    const cats = ['Toutes'];
    if (categories) {
      cats.push(...categories.map((cat: any) => cat.name || cat));
    }
    return cats;
  }, [categories]);

  const resetForm = () => {
    setFormData({
      name: '', category: '', purchase_price: '', selling_price: '',
      current_stock: '', minimum_stock: '', unit: 'unité', description: ''
    });
    setEditingProduct(null);
  };

  const handleSubmit = async (e: FormEvent) => {
    e.preventDefault();
    const productData = {
      name: formData.name,
      category: formData.category,
      purchase_price: Number(formData.purchase_price),
      selling_price: Number(formData.selling_price),
      current_stock: Number(formData.current_stock),
      minimum_stock: Number(formData.minimum_stock),
      unit: formData.unit,
      description: formData.description,
      is_active: true
    };
    try {
      if (editingProduct) {
        await updateProduct.mutateAsync({ id: editingProduct.id, data: productData });
        toast({ title: "Produit mis à jour", description: "Le produit a été modifié avec succès." });
      } else {
        await createProduct.mutateAsync(productData);
        toast({ title: "Produit créé", description: "Le nouveau produit a été ajouté avec succès." });
      }
      resetForm();
      setIsDialogOpen(false);
    } catch (error) {
      console.error('Erreur lors de la sauvegarde:', error);
      toast({ variant: "destructive", title: "Erreur", description: "La sauvegarde a échoué." });
    }
  };

  const handleEdit = (product: Product) => {
    setEditingProduct(product);
    setFormData({
      name: product.name,
      category: product.category,
      purchase_price: product.purchase_price.toString(),
      selling_price: product.selling_price.toString(),
      current_stock: product.current_stock.toString(),
      minimum_stock: product.minimum_stock.toString(),
      unit: product.unit,
      description: product.description || ''
    });
    setIsDialogOpen(true);
  };

  const handleDelete = async (productId: string) => {
    try {
      await deleteProduct.mutateAsync(productId);
      toast({ title: "Produit supprimé", description: "Le produit a été supprimé avec succès." });
    } catch (error) {
      console.error('Erreur lors de la suppression:', error);
      toast({ variant: "destructive", title: "Erreur", description: "La suppression a échoué." });
    }
  };

  const getStockStatus = (currentStock: number, minimumStock: number) => {
    if (currentStock <= 0) return { variant: 'destructive' as const, text: 'Rupture' };
    if (currentStock <= minimumStock) return { variant: 'secondary' as const, text: 'Stock bas' };
    return { variant: 'default' as const, text: 'En stock' };
  };

  if (productsError) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <AlertTriangle className="h-12 w-12 text-destructive mx-auto mb-4" />
          <h3 className="text-lg font-semibold mb-2">Erreur de chargement</h3>
          <p className="text-muted-foreground">Impossible de charger les produits.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Gestion des Produits</h1>
          <p className="text-muted-foreground">Gérez votre inventaire de produits.</p>
        </div>
        <ProtectedComponent permission="products.create">
          <Dialog open={isDialogOpen} onOpenChange={(isOpen) => {
            if (!isOpen) {
              resetForm();
            }
            setIsDialogOpen(isOpen);
          }}>
            <DialogTrigger asChild>
              <Button onClick={() => setIsDialogOpen(true)}>
                <Plus className="w-4 h-4 mr-2" />
                Ajouter un produit
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[600px]">
              <DialogHeader>
                <DialogTitle>{editingProduct ? 'Modifier le produit' : 'Ajouter un produit'}</DialogTitle>
                <DialogDescription>{editingProduct ? 'Modifiez les informations.' : 'Remplissez les informations.'}</DialogDescription>
              </DialogHeader>
              <form onSubmit={handleSubmit} className="space-y-4 pt-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="name">Nom</Label>
                    <Input id="name" value={formData.name} onChange={(e) => setFormData({...formData, name: e.target.value})} required />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="category">Catégorie</Label>
                    <Select value={formData.category} onValueChange={(value) => setFormData({...formData, category: value})}>
                      <SelectTrigger><SelectValue placeholder="Choisir..." /></SelectTrigger>
                      <SelectContent>
                        {categoriesLoading ? (
                          <SelectItem value="loading" disabled>Chargement...</SelectItem>
                        ) : (
                          categories.map((cat: any) => <SelectItem key={cat.name} value={cat.name}>{cat.name}</SelectItem>)
                        )}
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="purchase_price">Prix d'achat</Label>
                    <Input id="purchase_price" type="number" value={formData.purchase_price} onChange={(e) => setFormData({...formData, purchase_price: e.target.value})} required />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="selling_price">Prix de vente</Label>
                    <Input id="selling_price" type="number" value={formData.selling_price} onChange={(e) => setFormData({...formData, selling_price: e.target.value})} required />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="current_stock">Stock actuel</Label>
                    <Input id="current_stock" type="number" value={formData.current_stock} onChange={(e) => setFormData({...formData, current_stock: e.target.value})} required />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="minimum_stock">Stock minimum</Label>
                    <Input id="minimum_stock" type="number" value={formData.minimum_stock} onChange={(e) => setFormData({...formData, minimum_stock: e.target.value})} required />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="unit">Unité</Label>
                    <Input id="unit" value={formData.unit} onChange={(e) => setFormData({...formData, unit: e.target.value})} required />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="description">Description</Label>
                    <Input id="description" value={formData.description} onChange={(e) => setFormData({...formData, description: e.target.value})} />
                  </div>
                </div>
                <div className="flex justify-end gap-2">
                  <Button type="button" variant="outline" onClick={() => setIsDialogOpen(false)}>Annuler</Button>
                  <Button type="submit" disabled={createProduct.isPending || updateProduct.isPending}>
                    {createProduct.isPending || updateProduct.isPending ? 'Sauvegarde...' : 'Sauvegarder'}
                  </Button>
                </div>
              </form>
            </DialogContent>
          </Dialog>
        </ProtectedComponent>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Liste des Produits</CardTitle>
          <CardDescription>Recherchez, filtrez et gérez vos produits.</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-4 mb-4">
            <div className="relative flex-1">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input type="search" placeholder="Rechercher..." className="pl-8" value={searchTerm} onChange={(e) => setSearchTerm(e.target.value)} />
            </div>
            <Select value={selectedCategory} onValueChange={setSelectedCategory}>
              <SelectTrigger className="w-[180px]"><SelectValue placeholder="Filtrer..." /></SelectTrigger>
              <SelectContent>
                {categoryOptions.map(c => <SelectItem key={c} value={c}>{c}</SelectItem>)}
              </SelectContent>
            </Select>
          </div>

          {productsLoading ? (
            <div className="space-y-2 py-4">
              {[...Array(5)].map((_, i) => <Skeleton key={i} className="h-12 w-full" />)}
            </div>
          ) : filteredProducts.length === 0 ? (
            <div className="text-center py-10">
              <Package className="h-12 w-12 text-muted-foreground mx-auto" />
              <h3 className="mt-4 text-lg font-semibold">Aucun produit trouvé</h3>
              <p className="text-sm text-muted-foreground">
                {searchTerm || selectedCategory !== 'Toutes' ? "Essayez d'ajuster votre recherche." : 'Ajoutez votre premier produit pour commencer.'}
              </p>
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Produit</TableHead>
                  <TableHead>Catégorie</TableHead>
                  <TableHead className="text-right">Prix Vente</TableHead>
                  <TableHead className="text-right">Stock</TableHead>
                  <TableHead>Statut</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredProducts.map((product: Product) => {
                  const stockStatus = getStockStatus(product.current_stock, product.minimum_stock);
                  return (
                    <TableRow key={product.id}>
                      <TableCell className="font-medium">{product.name}</TableCell>
                      <TableCell>{product.category}</TableCell>
                      <TableCell className="text-right">{formatCurrency(product.selling_price)}</TableCell>
                      <TableCell className="text-right">{product.current_stock} {product.unit}</TableCell>
                      <TableCell><Badge variant={stockStatus.variant}>{stockStatus.text}</Badge></TableCell>
                      <TableCell className="text-right">
                        <div className="flex items-center justify-end gap-1">
                          <ProtectedComponent permission="products.update">
                            <Button variant="ghost" size="icon" onClick={() => handleEdit(product)}><Edit className="h-4 w-4" /></Button>
                          </ProtectedComponent>
                          <ProtectedComponent permission="products.delete">
                            <Button variant="ghost" size="icon" onClick={() => handleDelete(product.id)} disabled={!hasPermission('products.delete')}> 
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </ProtectedComponent>
                        </div>
                      </TableCell>
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default Products;
