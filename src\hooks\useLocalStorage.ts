import { useState, useEffect, useCallback } from 'react';

/**
 * Hook personnalisé pour gérer le localStorage avec synchronisation automatique
 */
export function useLocalStorage<T>(key: string, initialValue: T) {
  // Fonction pour lire la valeur depuis localStorage
  const readValue = useCallback((): T => {
    if (typeof window === 'undefined') {
      return initialValue;
    }

    try {
      const item = window.localStorage.getItem(key);
      return item ? JSON.parse(item) : initialValue;
    } catch (error) {
      console.warn(`Erreur lors de la lecture de localStorage pour la clé "${key}":`, error);
      return initialValue;
    }
  }, [initialValue, key]);

  // État avec lazy initial state
  const [storedValue, setStoredValue] = useState<T>(readValue);

  // Fonction pour sauvegarder dans localStorage
  const setValue = useCallback(
    (value: T | ((val: T) => T)) => {
      try {
        // Permettre la fonction de mise à jour comme useState
        const valueToStore = value instanceof Function ? value(storedValue) : value;
        
        // Sauvegarder dans l'état
        setStoredValue(valueToStore);
        
        // Sauvegarder dans localStorage
        if (typeof window !== 'undefined') {
          window.localStorage.setItem(key, JSON.stringify(valueToStore));
        }
      } catch (error) {
        console.warn(`Erreur lors de l'écriture dans localStorage pour la clé "${key}":`, error);
      }
    },
    [key, storedValue]
  );

  // Écouter les changements dans localStorage (pour la synchronisation entre onglets)
  useEffect(() => {
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === key && e.newValue !== null) {
        try {
          setStoredValue(JSON.parse(e.newValue));
        } catch (error) {
          console.warn(`Erreur lors de la synchronisation localStorage pour la clé "${key}":`, error);
        }
      }
    };

    window.addEventListener('storage', handleStorageChange);
    return () => window.removeEventListener('storage', handleStorageChange);
  }, [key]);

  return [storedValue, setValue] as const;
}

/**
 * Hook pour gérer les préférences utilisateur avec localStorage
 */
export function useUserPreferences() {
  const [preferences, setPreferences] = useLocalStorage('userPreferences', {
    theme: 'light',
    language: 'fr',
    sidebarCollapsed: false,
    tablePageSize: 10,
    notifications: true,
    autoSave: true
  });

  const updatePreference = useCallback(
    (key: string, value: any) => {
      setPreferences(prev => ({ ...prev, [key]: value }));
    },
    [setPreferences]
  );

  return { preferences, updatePreference };
}

/**
 * Hook pour gérer l'historique de navigation récente
 */
export function useRecentPages() {
  const [recentPages, setRecentPages] = useLocalStorage<Array<{
    path: string;
    title: string;
    timestamp: number;
  }>>('recentPages', []);

  const addRecentPage = useCallback(
    (path: string, title: string) => {
      setRecentPages(prev => {
        const filtered = prev.filter(page => page.path !== path);
        const newPage = { path, title, timestamp: Date.now() };
        return [newPage, ...filtered].slice(0, 10); // Garder seulement les 10 dernières
      });
    },
    [setRecentPages]
  );

  return { recentPages, addRecentPage };
}
