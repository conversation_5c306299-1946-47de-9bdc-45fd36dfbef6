import React from 'react';
import { useAuth } from '@/contexts/AuthContextBackend';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { formatCurrency } from '@/lib/currency';
import { parseNumericValue } from '@/lib/utils/numeric';
import {
  TrendingUp,
  Package,
  ShoppingCart,
  AlertTriangle,
  DollarSign,
  Users,
  Wine,
  ChefHat
} from 'lucide-react';
import {
  useProducts,
  useDailySummary,
  useLowStockAlerts,
  useSales
} from '@/hooks/useApi';

const Dashboard = () => {
  const { user } = useAuth();

  // Hooks pour récupérer les données dynamiques
  const { data: products, isLoading: productsLoading } = useProducts();
  const { data: dailySummary, isLoading: summaryLoading } = useDailySummary();
  const { data: lowStockAlerts, isLoading: alertsLoading } = useLowStockAlerts();
  const { data: recentSales, isLoading: salesLoading } = useSales({ limit: 10 });

  // Calculer les statistiques à partir des données réelles
  const stats = React.useMemo(() => {
    if (!products || !dailySummary) return null;

    const totalProducts = products?.length || 0;
    const totalSales = dailySummary?.total_sales || 0;
    const totalBenefits = dailySummary?.total_benefits || 0;
    // lowStockAlerts retourne un objet avec une propriété 'products'
    const lowStockCount = lowStockAlerts?.products?.length || lowStockAlerts?.count || 0;

    return {
      totalProducts,
      totalSales,
      totalBenefits,
      lowStockCount,
      salesByCategory: dailySummary?.sales_by_category || {},
      topProducts: dailySummary?.top_products || []
    };
  }, [products, dailySummary, lowStockAlerts]);

  const DashboardCard = ({ title, value, description, icon: Icon, trend, variant = "default" }: {
    title: string;
    value: string | number;
    description: string;
    icon: any;
    trend?: string;
    variant?: "default" | "success" | "warning" | "destructive";
  }) => (
    <Card className="relative overflow-hidden">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">{title}</CardTitle>
        <Icon className={`h-4 w-4 ${
          variant === "success" ? "text-success" :
          variant === "warning" ? "text-warning" :
          variant === "destructive" ? "text-destructive" :
          "text-muted-foreground"
        }`} />
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold">{typeof value === 'number' ? value.toLocaleString() : value}</div>
        <p className="text-xs text-muted-foreground">{description}</p>
        {trend && (
          <Badge variant="secondary" className="mt-2 text-xs">
            {trend}
          </Badge>
        )}
      </CardContent>
    </Card>
  );

  const StockAlert = ({ item }: { item: any }) => {
    // Déterminer si c'est critique (rupture de stock ou très faible)
    const isCritical = item.is_out_of_stock || item.current_stock === 0;
    const isLowStock = item.is_low_stock || item.current_stock <= item.minimum_stock;

    return (
      <div className={`flex items-center justify-between p-3 rounded-lg border ${
        isCritical ? 'border-destructive/20 bg-destructive/5' : 'border-warning/20 bg-warning/5'
      }`}>
        <div className="flex items-center gap-3">
          <AlertTriangle className={`w-4 h-4 ${isCritical ? 'text-destructive' : 'text-warning'}`} />
          <span className="font-medium">{item.product_name || item.name}</span>
        </div>
        <Badge variant={isCritical ? "destructive" : "secondary"}>
          {item.current_stock || 0} {isCritical ? 'rupture' : 'restant'}
        </Badge>
      </div>
    );
  };

  // Composant de loading
  const LoadingCard = () => (
    <Card>
      <CardHeader>
        <Skeleton className="h-4 w-24" />
      </CardHeader>
      <CardContent>
        <Skeleton className="h-8 w-16 mb-2" />
        <Skeleton className="h-3 w-32" />
      </CardContent>
    </Card>
  );

  return (
    <div className="space-y-6">
      {/* Welcome Section */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">
            Bonjour, {user?.name} 👋
          </h1>
          <p className="text-muted-foreground">
            Voici un aperçu de votre activité aujourd'hui
          </p>
        </div>
        <div className="text-right">
          <p className="text-sm text-muted-foreground">Rôle actuel</p>
          <Badge variant="outline" className="mt-1">
            {user?.role}
          </Badge>
        </div>
      </div>

      {/* Stats Cards */}
      {(user?.role === 'admin' || user?.role === 'gerant') && (
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          {summaryLoading ? (
            <>
              <LoadingCard />
              <LoadingCard />
              <LoadingCard />
              <LoadingCard />
            </>
          ) : (
            <>
              <DashboardCard
                title="Ventes Totales"
                value={formatCurrency(stats?.totalSales || 0)}
                description="Ventes d'aujourd'hui"
                icon={ShoppingCart}
                variant="success"
              />
              <DashboardCard
                title="Bénéfices"
                value={formatCurrency(stats?.totalBenefits || 0)}
                description="Bénéfices cumulés"
                icon={DollarSign}
                variant="success"
              />
              <DashboardCard
                title="Produits en Stock"
                value={stats?.totalProducts || 0}
                description="Produits référencés"
                icon={Package}
                variant="default"
              />
              <DashboardCard
                title="Alertes Stock"
                value={stats?.lowStockCount || 0}
                description="Produits en rupture"
                icon={AlertTriangle}
                variant="destructive"
              />
            </>
          )}
        </div>
      )}

      {/* Serveur Dashboard */}
      {user?.role === 'serveur' && (
        <div className="grid gap-4 md:grid-cols-3">
          <DashboardCard
            title="Commandes du Jour"
            value="24"
            description="Commandes traitées"
            icon={ShoppingCart}
            variant="success"
          />
          <DashboardCard
            title="Tables Actives"
            value="8"
            description="Tables occupées"
            icon={Users}
            variant="default"
          />
          <DashboardCard
            title="Ventes Personnelles"
            value="45,000 BIF"
            description="Vos ventes aujourd'hui"
            icon={DollarSign}
            variant="success"
          />
        </div>
      )}

      <div className="grid gap-6 md:grid-cols-2">
        {/* Ventes par Catégorie */}
        {(user?.role === 'admin' || user?.role === 'gerant') && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Wine className="w-5 h-5" />
                Ventes par Catégorie
              </CardTitle>
              <CardDescription>Répartition des ventes d'aujourd'hui</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium">Boissons</span>
                <div className="text-right">
                  <span className="font-bold">{formatCurrency(stats?.salesByCategory?.boissons || 0)}</span>
                  <div className="w-32 bg-muted rounded-full h-2 ml-2">
                    <div className="bg-primary h-2 rounded-full" style={{ width: '68%' }} />
                  </div>
                </div>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium">Plats</span>
                <div className="text-right">
                  <span className="font-bold">{formatCurrency(stats?.salesByCategory?.plats || 0)}</span>
                  <div className="w-32 bg-muted rounded-full h-2 ml-2">
                    <div className="bg-accent h-2 rounded-full" style={{ width: '22%' }} />
                  </div>
                </div>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium">Liqueurs</span>
                <div className="text-right">
                  <span className="font-bold">{formatCurrency(stats?.salesByCategory?.liqueurs || 0)}</span>
                  <div className="w-32 bg-muted rounded-full h-2 ml-2">
                    <div className="bg-warning h-2 rounded-full" style={{ width: '10%' }} />
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Produits Populaires / Alertes Stock */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              {user?.role === 'Serveur' ? (
                <>
                  <ChefHat className="w-5 h-5" />
                  Produits Populaires
                </>
              ) : (
                <>
                  <AlertTriangle className="w-5 h-5" />
                  Alertes Stock
                </>
              )}
            </CardTitle>
            <CardDescription>
              {user?.role === 'Serveur' 
                ? 'Les plus commandés aujourd\'hui'
                : 'Produits nécessitant une attention'
              }
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-3">
            {user?.role === 'Serveur' ? (
              stats?.topProducts?.map((product, index) => (
                <div key={index} className="flex items-center justify-between p-3 rounded-lg bg-muted/50">
                  <div>
                    <span className="font-medium">{product.name}</span>
                    <p className="text-xs text-muted-foreground">
                      {formatCurrency(parseNumericValue(product.revenue))} vendus
                    </p>
                  </div>
                  <Badge variant="secondary">
                    Stock: {product.current_stock || 0}
                  </Badge>
                </div>
              )) || []
            ) : (
              lowStockAlerts?.products?.map((item, index) => (
                <StockAlert key={index} item={item} />
              )) || []
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default Dashboard;