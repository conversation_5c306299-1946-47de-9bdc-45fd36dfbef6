import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { 
  Users, Clock, MapPin, Search, Filter, 
  Plus, BarChart3, Calendar, AlertCircle,
  Loader2, RefreshCw
} from 'lucide-react';
import { TableCard } from './TableCard';
import { useTablesQueries, useReservationsQueries } from '@/api/tables';
import { useTablesWebSocket, useReservationsWebSocket } from '@/hooks/useWebSocket';
import { toast } from 'sonner';

export function TablesDashboardConnected() {
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [locationFilter, setLocationFilter] = useState<string>('all');
  const [customerName, setCustomerName] = useState('');
  const [showOccupyDialog, setShowOccupyDialog] = useState(false);
  const [selectedTableId, setSelectedTableId] = useState<number | null>(null);

  // Hooks pour les données
  const { useTablesList, useTableStatusSummary, useOccupyTable, useFreeTable } = useTablesQueries();
  const { useTodaysReservations } = useReservationsQueries();

  // WebSocket pour les mises à jour temps réel
  const {
    isConnected: tablesWsConnected,
    lastUpdate,
    requestTablesUpdate,
    reconnectAttempts
  } = useTablesWebSocket();

  const { isConnected: reservationsWsConnected } = useReservationsWebSocket();

  const { 
    data: tables = [], 
    isLoading: tablesLoading, 
    error: tablesError,
    refetch: refetchTables 
  } = useTablesList();
  
  const { 
    data: statusSummary, 
    isLoading: summaryLoading 
  } = useTableStatusSummary();
  
  const { 
    data: todaysReservations = [], 
    isLoading: reservationsLoading 
  } = useTodaysReservations();
  
  const occupyTableMutation = useOccupyTable();
  const freeTableMutation = useFreeTable();

  // Filtrage des tables
  const filteredTables = tables.filter(table => {
    const matchesSearch = table.number.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         table.location.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'all' || table.status === statusFilter;
    const matchesLocation = locationFilter === 'all' || table.location === locationFilter;
    
    return matchesSearch && matchesStatus && matchesLocation;
  });

  // Locations uniques
  const locations = [...new Set(tables.map(t => t.location))];

  // Réservations urgentes (dans les 2 prochaines heures)
  const urgentReservations = todaysReservations.filter(r => 
    r.is_upcoming && r.status === 'confirmed'
  );

  const handleOccupyTable = async (tableId: number) => {
    setSelectedTableId(tableId);
    setShowOccupyDialog(true);
  };

  const confirmOccupyTable = async () => {
    if (!selectedTableId) return;

    try {
      await occupyTableMutation.mutateAsync({ 
        id: selectedTableId, 
        customerName: customerName || undefined 
      });
      
      toast.success('Table occupée avec succès');
      setShowOccupyDialog(false);
      setCustomerName('');
      setSelectedTableId(null);
    } catch (error: any) {
      toast.error(error.message || 'Erreur lors de l\'occupation de la table');
    }
  };

  const handleFreeTable = async (tableId: number) => {
    const table = tables.find(t => t.id === tableId);
    if (!table) return;

    // Vérifier s'il y a une vente en cours
    if (table.current_sale && table.current_sale.status !== 'paid') {
      toast.error('Impossible de libérer la table : commande non payée');
      return;
    }

    try {
      await freeTableMutation.mutateAsync(tableId);
      toast.success('Table libérée avec succès');
    } catch (error: any) {
      toast.error(error.message || 'Erreur lors de la libération de la table');
    }
  };

  const handleRefresh = () => {
    refetchTables();
    toast.success('Données actualisées');
  };

  if (tablesLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="w-8 h-8 animate-spin" />
        <span className="ml-2">Chargement des tables...</span>
      </div>
    );
  }

  if (tablesError) {
    return (
      <div className="flex flex-col items-center justify-center h-64 space-y-4">
        <AlertCircle className="w-12 h-12 text-red-500" />
        <div className="text-center">
          <h3 className="text-lg font-medium">Erreur de chargement</h3>
          <p className="text-muted-foreground">Impossible de charger les données des tables</p>
        </div>
        <Button onClick={handleRefresh} variant="outline">
          <RefreshCw className="w-4 h-4 mr-2" />
          Réessayer
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* En-tête avec statistiques */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">Gestion des Tables</h1>
          <p className="text-muted-foreground">
            Gérez l'occupation et les réservations de vos tables en temps réel
          </p>
        </div>
        
        <div className="flex gap-2">
          <div className="flex items-center gap-2">
            <div className={`w-2 h-2 rounded-full ${tablesWsConnected ? 'bg-green-500' : 'bg-red-500'}`} />
            <span className="text-sm text-muted-foreground">
              {tablesWsConnected ? 'Temps réel' : 'Hors ligne'}
              {reconnectAttempts > 0 && ` (${reconnectAttempts}/5)`}
            </span>
            {lastUpdate && (
              <span className="text-xs text-muted-foreground">
                Mis à jour: {lastUpdate.toLocaleTimeString()}
              </span>
            )}
          </div>

          <Button variant="outline" onClick={handleRefresh}>
            <RefreshCw className="w-4 h-4 mr-2" />
            Actualiser
          </Button>
          <Button variant="outline">
            <Calendar className="w-4 h-4 mr-2" />
            Réservations
          </Button>
          <Button variant="outline">
            <BarChart3 className="w-4 h-4 mr-2" />
            Analyses
          </Button>
          <Button>
            <Plus className="w-4 h-4 mr-2" />
            Nouvelle table
          </Button>
        </div>
      </div>

      {/* Statistiques rapides */}
      {statusSummary && !summaryLoading && (
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-5">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Tables</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{statusSummary.total}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Disponibles</CardTitle>
              <div className="h-3 w-3 rounded-full bg-green-500"></div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">{statusSummary.available}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Occupées</CardTitle>
              <div className="h-3 w-3 rounded-full bg-red-500"></div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-red-600">{statusSummary.occupied}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Réservées</CardTitle>
              <div className="h-3 w-3 rounded-full bg-blue-500"></div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-blue-600">{statusSummary.reserved}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Taux d'occupation</CardTitle>
              <BarChart3 className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{statusSummary.occupation_rate}%</div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Alertes réservations urgentes */}
      {urgentReservations.length > 0 && (
        <Card className="border-orange-200 bg-orange-50">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-orange-800">
              <AlertCircle className="w-5 h-5" />
              Réservations urgentes ({urgentReservations.length})
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {urgentReservations.slice(0, 3).map(reservation => (
                <div key={reservation.id} className="flex justify-between items-center p-2 bg-white rounded">
                  <div>
                    <span className="font-medium">{reservation.customer_name}</span>
                    <span className="text-sm text-muted-foreground ml-2">
                      Table {reservation.table_number} - {reservation.reservation_time}
                    </span>
                  </div>
                  <Badge variant="outline">
                    {reservation.party_size} pers.
                  </Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Filtres */}
      <div className="flex gap-4 items-center">
        <div className="relative flex-1 max-w-sm">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
          <Input
            placeholder="Rechercher une table..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>

        <Select value={statusFilter} onValueChange={setStatusFilter}>
          <SelectTrigger className="w-40">
            <SelectValue placeholder="Statut" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">Tous les statuts</SelectItem>
            <SelectItem value="available">Disponible</SelectItem>
            <SelectItem value="occupied">Occupée</SelectItem>
            <SelectItem value="reserved">Réservée</SelectItem>
            <SelectItem value="cleaning">Nettoyage</SelectItem>
          </SelectContent>
        </Select>

        <Select value={locationFilter} onValueChange={setLocationFilter}>
          <SelectTrigger className="w-40">
            <SelectValue placeholder="Emplacement" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">Tous les emplacements</SelectItem>
            {locations.map(location => (
              <SelectItem key={location} value={location}>
                {location}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        <Button variant="outline" size="icon">
          <Filter className="w-4 h-4" />
        </Button>
      </div>

      {/* Grille des tables */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
        {filteredTables.map((table) => (
          <TableCard
            key={table.id}
            table={table}
            onOccupy={handleOccupyTable}
            onFree={handleFreeTable}
            onEdit={(table) => console.log('Edit table:', table)}
            onViewSale={(saleId) => console.log('View sale:', saleId)}
          />
        ))}
      </div>

      {/* Message si aucune table */}
      {filteredTables.length === 0 && (
        <div className="text-center py-12">
          <Users className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-lg font-medium mb-2">Aucune table trouvée</h3>
          <p className="text-muted-foreground mb-4">
            Aucune table ne correspond à vos critères de recherche.
          </p>
          <Button variant="outline" onClick={() => {
            setSearchTerm('');
            setStatusFilter('all');
            setLocationFilter('all');
          }}>
            Réinitialiser les filtres
          </Button>
        </div>
      )}

      {/* Dialog pour occuper une table */}
      <Dialog open={showOccupyDialog} onOpenChange={setShowOccupyDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Occuper la table</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium">Nom du client (optionnel)</label>
              <Input
                value={customerName}
                onChange={(e) => setCustomerName(e.target.value)}
                placeholder="Entrez le nom du client..."
                className="mt-1"
              />
            </div>
            <div className="flex justify-end gap-2">
              <Button variant="outline" onClick={() => setShowOccupyDialog(false)}>
                Annuler
              </Button>
              <Button 
                onClick={confirmOccupyTable}
                disabled={occupyTableMutation.isPending}
              >
                {occupyTableMutation.isPending && (
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                )}
                Occuper
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}
