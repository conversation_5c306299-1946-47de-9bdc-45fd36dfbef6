import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import { StockSyncService } from '@/lib/syncService';
import { RefreshCw, CheckCircle, AlertTriangle, XCircle } from 'lucide-react';

const SyncDebug = () => {
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [lastResult, setLastResult] = useState<any>(null);

  const testCheckInventoryAPI = async () => {
    setIsLoading(true);
    try {
      const result = await StockSyncService.checkInventoryAPI();
      setLastResult({ type: 'checkAPI', result, timestamp: new Date() });
      
      toast({
        title: result ? "✅ API Disponible" : "⚠️ API Lecture Seule",
        description: result 
          ? "L'API Inventory supporte l'écriture" 
          : "L'API Inventory est en lecture seule",
        variant: result ? "default" : "destructive",
      });
    } catch (error) {
      toast({
        title: "❌ Erreur",
        description: `Erreur lors de la vérification: ${error instanceof Error ? error.message : 'Erreur inconnue'}`,
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const testSyncProductsToInventory = async () => {
    setIsLoading(true);
    try {
      const result = await StockSyncService.syncProductsToInventory();
      setLastResult({ type: 'syncProducts', result, timestamp: new Date() });
      
      toast({
        title: result.success ? "✅ Sync Réussie" : "❌ Sync Échouée",
        description: result.message || `${result.syncedItems} éléments synchronisés`,
        variant: result.success ? "default" : "destructive",
      });
    } catch (error) {
      toast({
        title: "❌ Erreur Sync",
        description: `Erreur: ${error instanceof Error ? error.message : 'Erreur inconnue'}`,
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const testCheckConsistency = async () => {
    setIsLoading(true);
    try {
      const result = await StockSyncService.checkConsistency();
      setLastResult({ type: 'checkConsistency', result, timestamp: new Date() });
      
      toast({
        title: result.consistent ? "✅ Données Cohérentes" : "⚠️ Incohérences Détectées",
        description: `${result.issues.length} problème(s) trouvé(s)`,
        variant: result.consistent ? "default" : "destructive",
      });
    } catch (error) {
      toast({
        title: "❌ Erreur Vérification",
        description: `Erreur: ${error instanceof Error ? error.message : 'Erreur inconnue'}`,
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const testFullSync = async () => {
    setIsLoading(true);
    try {
      const result = await StockSyncService.fullSync();
      setLastResult({ type: 'fullSync', result, timestamp: new Date() });
      
      toast({
        title: result.success ? "✅ Sync Complète Réussie" : "❌ Sync Complète Échouée",
        description: result.message || `${result.syncedItems} éléments synchronisés`,
        variant: result.success ? "default" : "destructive",
      });
    } catch (error) {
      toast({
        title: "❌ Erreur Sync Complète",
        description: `Erreur: ${error instanceof Error ? error.message : 'Erreur inconnue'}`,
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const getStatusIcon = (type: string) => {
    if (!lastResult || lastResult.type !== type) return null;
    
    if (type === 'checkAPI') {
      return lastResult.result ? <CheckCircle className="w-4 h-4 text-green-500" /> : <AlertTriangle className="w-4 h-4 text-yellow-500" />;
    }
    
    if (lastResult.result?.success) {
      return <CheckCircle className="w-4 h-4 text-green-500" />;
    } else {
      return <XCircle className="w-4 h-4 text-red-500" />;
    }
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <RefreshCw className="w-5 h-5" />
          Debug Service de Synchronisation
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid gap-3">
          <Button 
            onClick={testCheckInventoryAPI} 
            disabled={isLoading}
            variant="outline"
            className="flex items-center justify-between"
          >
            <span>🔍 Vérifier API Inventory</span>
            {getStatusIcon('checkAPI')}
          </Button>
          
          <Button 
            onClick={testSyncProductsToInventory} 
            disabled={isLoading}
            variant="outline"
            className="flex items-center justify-between"
          >
            <span>🔄 Sync Products → Inventory</span>
            {getStatusIcon('syncProducts')}
          </Button>
          
          <Button 
            onClick={testCheckConsistency} 
            disabled={isLoading}
            variant="outline"
            className="flex items-center justify-between"
          >
            <span>✅ Vérifier Cohérence</span>
            {getStatusIcon('checkConsistency')}
          </Button>
          
          <Button 
            onClick={testFullSync} 
            disabled={isLoading}
            variant="outline"
            className="flex items-center justify-between"
          >
            <span>🚀 Synchronisation Complète</span>
            {getStatusIcon('fullSync')}
          </Button>
        </div>

        {lastResult && (
          <div className="mt-4 p-4 bg-gray-50 rounded-lg">
            <h4 className="font-medium mb-2">Dernier Résultat :</h4>
            <div className="text-sm space-y-1">
              <div><strong>Type :</strong> {lastResult.type}</div>
              <div><strong>Timestamp :</strong> {lastResult.timestamp.toLocaleTimeString()}</div>
              {lastResult.result && (
                <div className="mt-2">
                  <strong>Détails :</strong>
                  <pre className="text-xs bg-white p-2 rounded mt-1 overflow-auto">
                    {JSON.stringify(lastResult.result, null, 2)}
                  </pre>
                </div>
              )}
            </div>
          </div>
        )}

        {isLoading && (
          <div className="flex items-center justify-center py-4">
            <RefreshCw className="w-6 h-6 animate-spin mr-2" />
            <span>Test en cours...</span>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default SyncDebug;
