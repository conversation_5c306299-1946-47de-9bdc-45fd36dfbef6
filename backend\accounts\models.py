from django.contrib.auth.models import AbstractUser
from django.db import models
from django.core.validators import RegexValidator

class User(AbstractUser):
    """
    Modèle utilisateur personnalisé avec système de rôles
    basé sur le cahier des charges du bar-restaurant
    """

    ROLE_CHOICES = [
        ('admin', 'Admin'),
        ('gerant', 'Gérant'),
        ('serveur', 'Serveur'),
    ]

    role = models.CharField(
        max_length=20,
        choices=ROLE_CHOICES,
        default='serveur',
        verbose_name='Rôle'
    )

    phone = models.CharField(
        max_length=15,
        validators=[RegexValidator(r'^\+?1?\d{9,15}$')],
        blank=True,
        null=True,
        verbose_name='Téléphone'
    )

    address = models.TextField(
        blank=True,
        null=True,
        verbose_name='Adresse'
    )

    is_active_session = models.BooleanField(
        default=False,
        verbose_name='Session active'
    )

    last_activity = models.DateTimeField(
        auto_now=True,
        verbose_name='Dernière activité'
    )

    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name='Date de création'
    )

    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name='Date de modification'
    )

    class Meta:
        verbose_name = 'Utilisateur'
        verbose_name_plural = 'Utilisateurs'
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.username} ({self.get_role_display()})"

    @property
    def is_admin(self):
        """Vérifie si l'utilisateur est admin"""
        return self.role == 'admin'

    @property
    def is_gerant(self):
        """Vérifie si l'utilisateur est gérant"""
        return self.role == 'gerant'

    @property
    def is_serveur(self):
        """Vérifie si l'utilisateur est serveur"""
        return self.role == 'serveur'

    def can_manage_users(self):
        """Peut créer/modifier des utilisateurs"""
        return self.role == 'admin'

    def can_manage_products(self):
        """Peut ajouter/modifier des produits"""
        return self.role in ['admin', 'gerant']

    def can_make_sales(self):
        """Peut effectuer des ventes"""
        return self.role in ['admin', 'gerant', 'serveur']

    def can_view_sales_history(self):
        """Peut voir l'historique des ventes"""
        return self.role in ['admin', 'gerant']

    def can_manage_inventory(self):
        """Peut gérer les approvisionnements"""
        return self.role in ['admin', 'gerant']

    def can_view_stock_alerts(self):
        """Peut voir les stocks et alertes"""
        return self.role in ['admin', 'gerant']

    def can_generate_reports(self):
        """Peut générer des rapports"""
        return self.role in ['admin', 'gerant']

    def can_manage_expenses(self):
        """Peut enregistrer des dépenses"""
        return self.role in ['admin', 'gerant']

    def can_delete_records(self):
        """Peut supprimer des enregistrements"""
        return self.role == 'admin'

    def can_manage_database(self):
        """Peut gérer la base de données"""
        return self.role == 'admin'


class UserActivity(models.Model):
    """
    Modèle pour tracer les activités des utilisateurs
    """

    ACTION_CHOICES = [
        ('login', 'Connexion'),
        ('logout', 'Déconnexion'),
        ('create', 'Création'),
        ('update', 'Modification'),
        ('delete', 'Suppression'),
        ('view', 'Consultation'),
        ('sale', 'Vente'),
        ('inventory', 'Inventaire'),
        ('report', 'Rapport'),
    ]

    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='activities',
        verbose_name='Utilisateur'
    )

    action = models.CharField(
        max_length=20,
        choices=ACTION_CHOICES,
        verbose_name='Action'
    )

    description = models.TextField(
        verbose_name='Description'
    )

    ip_address = models.GenericIPAddressField(
        blank=True,
        null=True,
        verbose_name='Adresse IP'
    )

    user_agent = models.TextField(
        blank=True,
        null=True,
        verbose_name='User Agent'
    )

    timestamp = models.DateTimeField(
        auto_now_add=True,
        verbose_name='Horodatage'
    )

    class Meta:
        verbose_name = 'Activité utilisateur'
        verbose_name_plural = 'Activités utilisateurs'
        ordering = ['-timestamp']

    def __str__(self):
        return f"{self.user.username} - {self.get_action_display()} - {self.timestamp}"
