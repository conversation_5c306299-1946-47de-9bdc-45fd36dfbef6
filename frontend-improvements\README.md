# BarStockWise - Améliorations Frontend

Ce dossier contient les améliorations et nouvelles fonctionnalités pour le frontend de BarStockWise.

## 🚀 Structure du projet

```
frontend-improvements/
├── api/                    # Couche API améliorée
│   ├── base.ts            # Configuration de base Axios
│   ├── tables.ts          # API des tables et réservations
│   ├── products.ts        # API des produits
│   ├── sales.ts           # API des ventes
│   └── auth.ts            # API d'authentification
├── src/                   # Code source React
│   ├── components/        # Composants réutilisables
│   ├── hooks/            # Hooks personnalisés
│   ├── lib/              # Utilitaires
│   └── pages/            # Pages de l'application
├── types/                # Types TypeScript globaux
└── config/               # Configuration
```

## 🔧 Configuration

### Installation des dépendances

```bash
cd frontend-improvements
npm install
```

### Variables d'environnement

Créer un fichier `.env` :

```env
VITE_API_URL=http://localhost:8000/api
VITE_APP_NAME=BarStockWise
VITE_APP_VERSION=2.0.0
```

### Démarrage du serveur de développement

```bash
npm run dev
```

## 📚 Fonctionnalités

### API Layer

- **Configuration Axios centralisée** avec intercepteurs
- **Gestion automatique des tokens** JWT
- **Refresh automatique** des tokens expirés
- **Gestion d'erreurs** unifiée
- **Types TypeScript** complets

### Hooks React Query

- **Mise en cache intelligente** des données
- **Synchronisation automatique** avec le backend
- **Optimistic updates** pour une meilleure UX
- **Gestion des états** de chargement et d'erreur

### Types TypeScript

- **Types complets** pour toutes les entités
- **Interfaces API** bien définies
- **Types utilitaires** pour la manipulation de données
- **Configuration stricte** TypeScript

## 🛠️ Développement

### Scripts disponibles

```bash
npm run dev          # Serveur de développement
npm run build        # Build de production
npm run preview      # Aperçu du build
npm run type-check   # Vérification des types
```

### Standards de code

- **ESLint** pour la qualité du code
- **Prettier** pour le formatage
- **TypeScript strict** mode
- **Conventions de nommage** cohérentes

## 🔗 Intégration

Ces améliorations peuvent être intégrées progressivement dans le projet principal :

1. **Remplacer la couche API** existante
2. **Migrer les hooks** vers React Query
3. **Ajouter les types** TypeScript
4. **Mettre à jour** les composants

## 📝 Notes

- Compatible avec **Vite** et **Next.js**
- Support **SSR/SSG** ready
- **Tree-shaking** optimisé
- **Bundle splitting** intelligent

## 🐛 Résolution des erreurs

### Erreurs TypeScript communes

1. **Module non trouvé** : Vérifier les alias dans `tsconfig.json`
2. **Types manquants** : Installer `@types/node`
3. **Variables d'environnement** : Utiliser le bon préfixe (`VITE_` ou `NEXT_PUBLIC_`)

### Erreurs d'API

1. **CORS** : Configurer le backend Django
2. **Authentification** : Vérifier les tokens JWT
3. **Endpoints** : Vérifier les URLs d'API
