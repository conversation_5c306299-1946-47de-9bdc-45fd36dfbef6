// Types globaux pour l'application

// Variables d'environnement Vite
interface ImportMetaEnv {
  readonly VITE_API_URL: string;
  readonly VITE_APP_NAME: string;
  readonly VITE_APP_VERSION: string;
}

interface ImportMeta {
  readonly env: ImportMetaEnv;
}

// Variables d'environnement Node.js (pour compatibilité)
declare namespace NodeJS {
  interface ProcessEnv {
    readonly NEXT_PUBLIC_API_URL: string;
    readonly NODE_ENV: 'development' | 'production' | 'test';
  }
}

// Extensions pour les modules CSS
declare module '*.module.css' {
  const classes: { [key: string]: string };
  export default classes;
}

declare module '*.module.scss' {
  const classes: { [key: string]: string };
  export default classes;
}

// Extensions pour les assets
declare module '*.svg' {
  const content: React.FunctionComponent<React.SVGAttributes<SVGElement>>;
  export default content;
}

declare module '*.png' {
  const content: string;
  export default content;
}

declare module '*.jpg' {
  const content: string;
  export default content;
}

declare module '*.jpeg' {
  const content: string;
  export default content;
}

declare module '*.gif' {
  const content: string;
  export default content;
}

declare module '*.webp' {
  const content: string;
  export default content;
}

// Types pour les composants React
export interface BaseComponentProps {
  className?: string;
  children?: React.ReactNode;
}

// Types pour les erreurs API
export interface ApiErrorResponse {
  message: string;
  errors?: Record<string, string[]>;
  status?: number;
}

// Types pour l'authentification
export interface User {
  id: number;
  username: string;
  email: string;
  first_name: string;
  last_name: string;
  role: 'admin' | 'gerant' | 'serveur';
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface AuthTokens {
  access: string;
  refresh: string;
}

export interface LoginResponse extends AuthTokens {
  user: User;
}

// Types pour les réponses paginées
export interface PaginatedResponse<T> {
  count: number;
  next: string | null;
  previous: string | null;
  results: T[];
}

// Types pour les filtres de recherche
export interface SearchFilters {
  search?: string;
  ordering?: string;
  page?: number;
  page_size?: number;
}

// Types pour les statistiques
export interface StatsCard {
  title: string;
  value: string | number;
  change?: {
    value: number;
    type: 'increase' | 'decrease';
  };
  icon?: React.ComponentType;
}

// Types pour les notifications
export interface Notification {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message: string;
  timestamp: string;
  read: boolean;
}

// Types pour les permissions
export type Permission = 
  | 'users.create' | 'users.read' | 'users.update' | 'users.delete'
  | 'products.create' | 'products.read' | 'products.update' | 'products.delete'
  | 'sales.create' | 'sales.read' | 'sales.update' | 'sales.delete'
  | 'tables.create' | 'tables.read' | 'tables.update' | 'tables.delete'
  | 'reports.read' | 'reports.export'
  | 'settings.read' | 'settings.update';

export type UserRole = 'admin' | 'gerant' | 'serveur';

// Utilitaires TypeScript
export type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;
export type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>;
export type PartialExcept<T, K extends keyof T> = Partial<T> & Pick<T, K>;
