import React, { useState, useEffect, useMemo } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  TrendingUp, 
  TrendingDown, 
  DollarSign, 
  Package, 
  AlertTriangle,
  Calendar,
  BarChart3,
  PieChart,
  Target,
  Award,
  Clock,
  Zap
} from 'lucide-react';
import { formatCurrency } from '@/lib/currency';
import { usePermissions } from '@/contexts/PermissionsContext';
import { parseNumericValue } from '@/lib/utils/numeric';
import {
  useSales,
  useProducts,
  useDailySummary,
  useLowStockAlerts,
  useSalesAnalytics,
  useProductAnalytics,
  useCategoryAnalytics,
  useDashboardStats
} from '@/hooks/useApi';

// Types pour les analytics
interface DailyAnalytics {
  date: string;
  totalRevenue: number;
  totalProfit: number;
  totalSales: number;
  averageMargin: number;
  topProduct: string;
  alertCount: number;
}

interface ProductAnalytics {
  name: string;
  category: string;
  totalRevenue: number;
  totalProfit: number;
  totalSold: number;
  averagePrice: number;
  margin: number;
  trend: 'up' | 'down' | 'stable';
  stockTurnover: number;
}

interface CategoryAnalytics {
  category: string;
  revenue: number;
  profit: number;
  margin: number;
  percentage: number;
  trend: 'up' | 'down' | 'stable';
}

const Analytics = () => {
  const { canViewReports } = usePermissions();
  const [selectedPeriod, setSelectedPeriod] = useState('7d');
  const [selectedMetric, setSelectedMetric] = useState('revenue');

  // Hooks pour les données API d'analytics
  const { data: salesAnalytics, isLoading: salesAnalyticsLoading, error: salesAnalyticsError } = useSalesAnalytics({ period: selectedPeriod });
  const { data: productAnalytics, isLoading: productAnalyticsLoading, error: productAnalyticsError } = useProductAnalytics({ period: selectedPeriod });
  const { data: categoryAnalytics, isLoading: categoryAnalyticsLoading, error: categoryAnalyticsError } = useCategoryAnalytics({ period: selectedPeriod });
  const { data: dashboardStats, isLoading: dashboardStatsLoading, error: dashboardStatsError } = useDashboardStats({ period: selectedPeriod });

  // Hooks de fallback pour les données de base
  const { data: salesResponse, isLoading: salesLoading } = useSales();
  const { data: productsResponse, isLoading: productsLoading } = useProducts();
  const { data: lowStockAlertsResponse, isLoading: alertsLoading } = useLowStockAlerts();

  // Les APIs d'analytics sont temporairement désactivées - utiliser uniquement les calculs de fallback
  const hasAnalyticsAPI = false;

  // Fonctions de calcul de fallback (déclarées avant leur utilisation)
  const calculateDailyAnalyticsFromSales = (sales: any[], products: any[]): DailyAnalytics[] => {
    if (!sales || sales.length === 0) return [];

    const salesByDate = sales.reduce((acc: any, sale: any) => {
      const date = new Date(sale.created_at).toISOString().split('T')[0];
      if (!acc[date]) {
        acc[date] = {
          totalRevenue: 0,
          totalProfit: 0,
          totalSales: 0,
          products: {}
        };
      }

      acc[date].totalRevenue += parseNumericValue(sale.total_amount);
      acc[date].totalSales += 1;

      sale.items?.forEach((item: any) => {
        const product = products.find((p: any) => p.id === item.product_id);
        if (product) {
          const profit = (parseNumericValue(item.unit_price) - parseNumericValue(product.purchase_price)) * item.quantity;
          acc[date].totalProfit += profit;
        }
      });

      return acc;
    }, {});

    return Object.entries(salesByDate).map(([date, data]: [string, any]) => {
      // Trouver le produit le plus vendu du jour
      const topProduct = Object.entries(data.products).reduce((top: any, [productName, productData]: [string, any]) =>
        productData.quantity > (top?.quantity || 0) ? { name: productName, quantity: productData.quantity } : top,
        { name: 'Aucun', quantity: 0 }
      );

      return {
        date,
        totalRevenue: data.totalRevenue,
        totalProfit: data.totalProfit,
        totalSales: data.totalSales,
        averageMargin: data.totalRevenue > 0 ? (data.totalProfit / data.totalRevenue) * 100 : 0,
        topProduct: topProduct.name,
        alertCount: 0
      };
    }).sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());
  };

  const calculateProductAnalyticsFromSales = (sales: any[], products: any[]): ProductAnalytics[] => {
    if (!products || !sales) return [];

    return products.map((product: any) => {
      const productSales = sales.filter((sale: any) =>
        sale.items?.some((item: any) => item.product_id === product.id)
      );

      const totalQuantitySold = productSales.reduce((sum, sale) => {
        const item = sale.items?.find((item: any) => item.product_id === product.id);
        return sum + (item?.quantity || 0);
      }, 0);

      const totalRevenue = productSales.reduce((sum, sale) => {
        const item = sale.items?.find((item: any) => item.product_id === product.id);
        return sum + (parseNumericValue(item?.total_price) || 0);
      }, 0);

      const totalProfit = totalQuantitySold * (parseNumericValue(product.selling_price) - parseNumericValue(product.purchase_price));
      const profitMargin = totalRevenue > 0 ? (totalProfit / totalRevenue) * 100 : 0;
      const stockTurnover = product.initial_stock > 0 ? totalQuantitySold / product.initial_stock : 0;

      return {
        productId: product.id,
        productName: product.name,
        name: product.name,
        category: product.category?.name || 'Non définie',
        totalRevenue,
        totalProfit,
        quantitySold: totalQuantitySold,
        totalSold: totalQuantitySold,
        profitMargin,
        stockTurnover,
        averagePrice: totalQuantitySold > 0 ? totalRevenue / totalQuantitySold : parseNumericValue(product.selling_price),
        margin: profitMargin,
        trend: (totalQuantitySold > 10 ? 'up' : totalQuantitySold > 5 ? 'stable' : 'down') as 'up' | 'stable' | 'down'
      };
    }).sort((a, b) => b.totalRevenue - a.totalRevenue);
  };

  const calculateCategoryAnalyticsFromSales = (sales: any[], products: any[]): CategoryAnalytics[] => {
    const productAnalytics = calculateProductAnalyticsFromSales(sales, products);
    if (!productAnalytics.length) return [];

    const categoryData = productAnalytics.reduce((acc: any, product) => {
      if (!acc[product.category]) {
        acc[product.category] = {
          revenue: 0,
          profit: 0,
          totalSold: 0
        };
      }

      acc[product.category].revenue += product.totalRevenue;
      acc[product.category].profit += product.totalProfit;
      acc[product.category].totalSold += product.totalSold;

      return acc;
    }, {});

    const totalRevenueNum = Object.values(categoryData).reduce((sum: number, cat: any) => sum + cat.revenue, 0);

    return Object.entries(categoryData).map(([category, data]: [string, any]) => {
      const revenue = Number(data.revenue) || 0;
      const profit = Number(data.profit) || 0;
      const totalRevenueNumber = Number(totalRevenueNum) || 0;

      return {
        category,
        revenue,
        profit,
        totalSold: data.totalSold,
        percentage: totalRevenueNumber > 0 ? (revenue / totalRevenueNumber) * 100 : 0,
        margin: revenue > 0 ? (profit / revenue) * 100 : 0,
        trend: (revenue > 10000 ? 'up' : revenue > 5000 ? 'stable' : 'down') as 'up' | 'stable' | 'down'
      };
    }).sort((a, b) => b.revenue - a.revenue);
  };

  const calculateDashboardStatsFromSales = (sales: any[], products: any[]) => {
    const dailyAnalytics = calculateDailyAnalyticsFromSales(sales, products);
    const totalRevenue = dailyAnalytics.reduce((sum, day) => sum + day.totalRevenue, 0);
    const totalProfit = dailyAnalytics.reduce((sum, day) => sum + day.totalProfit, 0);

    return {
      totalRevenue,
      totalProfit,
      averageDaily: {
        revenue: dailyAnalytics.length > 0 ? totalRevenue / dailyAnalytics.length : 0,
        profit: dailyAnalytics.length > 0 ? totalProfit / dailyAnalytics.length : 0,
        sales: dailyAnalytics.reduce((sum, day) => sum + day.totalSales, 0) / (dailyAnalytics.length || 1)
      }
    };
  };

  // Utiliser les données d'analytics si disponibles, sinon calculer à partir des données de base
  const analytics = useMemo(() => {
    if (hasAnalyticsAPI && salesAnalytics && productAnalytics && categoryAnalytics) {
      return {
        dailyAnalytics: salesAnalytics?.daily_analytics || [],
        productAnalytics: productAnalytics?.product_analytics || [],
        categoryAnalytics: categoryAnalytics?.category_analytics || [],
        dashboardStats: dashboardStats || {}
      };
    }

    // Fallback: calculer à partir des données de base
    const sales = salesResponse?.results || (Array.isArray(salesResponse) ? salesResponse : []);
    const products = productsResponse?.results || (Array.isArray(productsResponse) ? productsResponse : []);

    return {
      dailyAnalytics: calculateDailyAnalyticsFromSales(sales, products),
      productAnalytics: calculateProductAnalyticsFromSales(sales, products),
      categoryAnalytics: calculateCategoryAnalyticsFromSales(sales, products),
      dashboardStats: calculateDashboardStatsFromSales(sales, products)
    };
  }, [hasAnalyticsAPI, salesAnalytics, productAnalytics, categoryAnalytics, dashboardStats, salesResponse, productsResponse]);

  // Calculs des métriques principales à partir des analytics
  const totalRevenue = useMemo(() =>
    analytics.dailyAnalytics.reduce((sum: number, day: any) => sum + day.totalRevenue, 0),
    [analytics.dailyAnalytics]
  );

  const totalProfit = useMemo(() =>
    analytics.dailyAnalytics.reduce((sum: number, day: any) => sum + day.totalProfit, 0),
    [analytics.dailyAnalytics]
  );

  const averageDaily = useMemo(() => ({
    revenue: analytics.dailyAnalytics.length > 0 ? totalRevenue / analytics.dailyAnalytics.length : 0,
    profit: analytics.dailyAnalytics.length > 0 ? totalProfit / analytics.dailyAnalytics.length : 0,
    sales: analytics.dailyAnalytics.reduce((sum: number, day: any) => sum + day.totalSales, 0) / (analytics.dailyAnalytics.length || 1)
  }), [analytics.dailyAnalytics, totalRevenue, totalProfit]);

  const revenueGrowth = useMemo(() => {
    if (analytics.dailyAnalytics.length < 2) return 0;
    const latest = analytics.dailyAnalytics[analytics.dailyAnalytics.length - 1]?.totalRevenue || 0;
    const previous = analytics.dailyAnalytics[analytics.dailyAnalytics.length - 2]?.totalRevenue || 0;
    return previous > 0 ? ((latest - previous) / previous) * 100 : 0;
  }, [analytics.dailyAnalytics]);

  // Fonctions utilitaires supprimées car non utilisées dans cette version simplifiée

  // Vérification des permissions
  if (!canViewReports) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <AlertTriangle className="w-12 h-12 text-yellow-500 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">Accès restreint</h3>
          <p className="text-gray-500">Vous n'avez pas les permissions nécessaires pour voir les analytics.</p>
        </div>
      </div>
    );
  }

  // État de chargement global
  if (salesLoading || productsLoading || dashboardStatsLoading) {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          {[...Array(4)].map((_, i) => (
            <Card key={i}>
              <CardHeader className="pb-2">
                <Skeleton className="h-4 w-20" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-8 w-24 mb-2" />
                <Skeleton className="h-3 w-16" />
              </CardContent>
            </Card>
          ))}
        </div>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <Skeleton className="h-6 w-32" />
            </CardHeader>
            <CardContent>
              <Skeleton className="h-64 w-full" />
            </CardContent>
          </Card>
          <Card>
            <CardHeader>
              <Skeleton className="h-6 w-32" />
            </CardHeader>
            <CardContent>
              <Skeleton className="h-64 w-full" />
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Métriques principales */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Revenus Totaux</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(totalRevenue)}</div>
            <p className="text-xs text-muted-foreground">
              +{revenueGrowth.toFixed(1)}% par rapport à hier
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Bénéfices</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(totalProfit)}</div>
            <p className="text-xs text-muted-foreground">
              Marge: {totalRevenue > 0 ? ((totalProfit / totalRevenue) * 100).toFixed(1) : 0}%
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Ventes Journalières</CardTitle>
            <BarChart3 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{analytics.dailyAnalytics.length}</div>
            <p className="text-xs text-muted-foreground">
              Moyenne: {formatCurrency(averageDaily.revenue)}/jour
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Produits Actifs</CardTitle>
            <Package className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{analytics.productAnalytics.length}</div>
            <p className="text-xs text-muted-foreground">
              En stock et disponibles
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Graphiques et tableaux */}
      <Tabs defaultValue="daily" className="space-y-4">
        <TabsList>
          <TabsTrigger value="daily">Analyse Journalière</TabsTrigger>
          <TabsTrigger value="products">Produits</TabsTrigger>
          <TabsTrigger value="categories">Catégories</TabsTrigger>
        </TabsList>

        <TabsContent value="daily" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Évolution des Ventes</CardTitle>
              <CardDescription>
                Revenus et bénéfices par jour
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-64 flex items-center justify-center text-muted-foreground">
                Graphique des ventes journalières (à implémenter avec Recharts)
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="products" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Performance des Produits</CardTitle>
              <CardDescription>
                Analyse détaillée par produit
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {analytics.productAnalytics.slice(0, 10).map((product, index) => (
                  <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
                    <div>
                      <h4 className="font-medium">{product.name}</h4>
                      <p className="text-sm text-muted-foreground">{product.category}</p>
                    </div>
                    <div className="text-right">
                      <div className="font-medium">{formatCurrency(product.totalRevenue)}</div>
                      <div className="text-sm text-muted-foreground">
                        {product.totalSold} vendus
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="categories" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Performance par Catégorie</CardTitle>
              <CardDescription>
                Répartition des ventes par catégorie
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {analytics.categoryAnalytics.map((category, index) => (
                  <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex items-center space-x-3">
                      <Package className="w-8 h-8 text-blue-600" />
                      <div>
                        <h4 className="font-medium">{category.category}</h4>
                        <p className="text-sm text-muted-foreground">
                          {category.percentage.toFixed(1)}% du total
                        </p>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="font-medium">{formatCurrency(category.revenue)}</div>
                      <Badge variant={category.trend === 'up' ? 'default' : category.trend === 'down' ? 'destructive' : 'secondary'}>
                        {category.trend === 'up' ? '↗' : category.trend === 'down' ? '↘' : '→'} {category.trend}
                      </Badge>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default Analytics;

