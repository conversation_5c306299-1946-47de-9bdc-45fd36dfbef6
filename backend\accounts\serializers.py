from rest_framework import serializers
from django.contrib.auth import authenticate
from .models import User, UserActivity

class UserSerializer(serializers.ModelSerializer):
    """
    Serializer pour le modèle User
    """
    
    password = serializers.CharField(write_only=True)
    
    class Meta:
        model = User
        fields = [
            'id', 'username', 'email', 'first_name', 'last_name',
            'role', 'phone', 'address', 'is_active', 'is_active_session',
            'last_activity', 'created_at', 'password'
        ]
        extra_kwargs = {
            'password': {'write_only': True},
            'last_activity': {'read_only': True},
            'created_at': {'read_only': True},
        }
    
    def create(self, validated_data):
        password = validated_data.pop('password')
        user = User.objects.create_user(**validated_data)
        user.set_password(password)
        user.save()
        return user
    
    def update(self, instance, validated_data):
        password = validated_data.pop('password', None)
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        
        if password:
            instance.set_password(password)
        
        instance.save()
        return instance


class UserLoginSerializer(serializers.Serializer):
    """
    Serializer pour l'authentification
    """
    
    username = serializers.CharField()
    password = serializers.CharField()
    
    def validate(self, data):
        username = data.get('username')
        password = data.get('password')
        
        if username and password:
            user = authenticate(username=username, password=password)
            if user:
                if user.is_active:
                    data['user'] = user
                else:
                    raise serializers.ValidationError('Compte utilisateur désactivé.')
            else:
                raise serializers.ValidationError('Nom d\'utilisateur ou mot de passe incorrect.')
        else:
            raise serializers.ValidationError('Nom d\'utilisateur et mot de passe requis.')
        
        return data


class UserActivitySerializer(serializers.ModelSerializer):
    """
    Serializer pour les activités utilisateur
    """
    
    user_username = serializers.CharField(source='user.username', read_only=True)
    action_display = serializers.CharField(source='get_action_display', read_only=True)
    
    class Meta:
        model = UserActivity
        fields = [
            'id', 'user', 'user_username', 'action', 'action_display',
            'description', 'ip_address', 'user_agent', 'timestamp'
        ]
        extra_kwargs = {
            'user': {'read_only': True},
            'timestamp': {'read_only': True},
        }


class ChangePasswordSerializer(serializers.Serializer):
    """
    Serializer pour changer le mot de passe
    """
    
    old_password = serializers.CharField(required=True)
    new_password = serializers.CharField(required=True)
    confirm_password = serializers.CharField(required=True)
    
    def validate(self, data):
        if data['new_password'] != data['confirm_password']:
            raise serializers.ValidationError("Les nouveaux mots de passe ne correspondent pas.")
        return data
    
    def validate_old_password(self, value):
        user = self.context['request'].user
        if not user.check_password(value):
            raise serializers.ValidationError("Ancien mot de passe incorrect.")
        return value


class UserProfileSerializer(serializers.ModelSerializer):
    """
    Serializer pour le profil utilisateur (lecture seule pour certains champs)
    """
    
    role_display = serializers.CharField(source='get_role_display', read_only=True)
    
    class Meta:
        model = User
        fields = [
            'id', 'username', 'email', 'first_name', 'last_name',
            'role', 'role_display', 'phone', 'address', 'is_active_session',
            'last_activity', 'created_at'
        ]
        read_only_fields = ['username', 'role', 'last_activity', 'created_at']
