# Guide d'Intégration Backend-Frontend BarStockWise

## 🎯 Vue d'ensemble

Ce guide explique comment l'intégration entre le frontend React et le backend Django a été mise en place pour BarStockWise.

## 📋 Modifications Apportées

### 1. **Configuration API (src/lib/api.ts)**
- ✅ Client Axios configuré avec intercepteurs
- ✅ Gestion automatique des tokens JWT
- ✅ Refresh automatique des tokens expirés
- ✅ APIs complètes pour tous les modules

### 2. **Nouveau Contexte d'Authentification (src/contexts/AuthContextBackend.tsx)**
- ✅ Authentification via API Django
- ✅ Gestion des tokens JWT
- ✅ Vérification automatique de l'activité utilisateur
- ✅ Gestion des erreurs et timeouts

### 3. **Hooks React Query (src/hooks/useApi.ts)**
- ✅ Hooks pour tous les modules (produits, ventes, stocks, etc.)
- ✅ Mutations avec gestion d'erreurs
- ✅ Cache intelligent et invalidation automatique
- ✅ Notifications toast intégrées

### 4. **WebSockets Temps Réel (src/hooks/useWebSocket.ts)**
- ✅ Connexions WebSocket automatiques
- ✅ Notifications en temps réel
- ✅ Alertes de stock automatiques
- ✅ Dashboard temps réel
- ✅ Reconnexion automatique

### 5. **Scripts de Démarrage**
- ✅ `start-backend.ps1` - Démarrage du serveur Django
- ✅ `start-frontend.ps1` - Démarrage du serveur React
- ✅ `start-dev.ps1` - Démarrage de l'environnement complet

## 🚀 Démarrage Rapide

### Option 1: Démarrage Automatique (Recommandé)
```powershell
# Dans le dossier bar-stock-wise
.\start-dev.ps1
```

### Option 2: Démarrage Manuel

**Backend Django:**
```powershell
.\start-backend.ps1
```

**Frontend React:**
```powershell
.\start-frontend.ps1
```

## 🔧 Configuration

### Variables d'Environnement (.env)
```env
# API Backend
VITE_API_URL=http://localhost:8000/api

# WebSocket
VITE_WS_URL=ws://localhost:8000/ws

# Développement
VITE_DEV_MODE=true
VITE_TOKEN_REFRESH_THRESHOLD=300000

# Notifications
VITE_ENABLE_NOTIFICATIONS=true
VITE_NOTIFICATION_TIMEOUT=5000
```

## 📡 APIs Disponibles

### Authentification
- `POST /api/auth/login/` - Connexion
- `POST /api/auth/logout/` - Déconnexion
- `GET /api/auth/profile/` - Profil utilisateur
- `POST /api/auth/token/refresh/` - Refresh token

### Produits
- `GET /api/products/` - Liste des produits
- `POST /api/products/` - Créer un produit
- `PUT /api/products/{id}/` - Modifier un produit
- `DELETE /api/products/{id}/` - Supprimer un produit

### Ventes
- `GET /api/sales/` - Liste des ventes
- `POST /api/sales/` - Créer une vente
- `GET /api/sales/daily-summary/` - Résumé journalier

### Inventaire
- `GET /api/inventory/` - État des stocks
- `POST /api/inventory/movements/` - Mouvement de stock
- `GET /api/inventory/low-stock-alerts/` - Alertes stock faible

### Rapports
- `GET /api/reports/daily/` - Rapports journaliers
- `POST /api/reports/daily/` - Créer un rapport
- `GET /api/reports/daily/{id}/export-pdf/` - Export PDF

## 🔌 WebSockets

### Notifications Utilisateur
```
ws://localhost:8000/ws/notifications/{user_id}/
```

### Alertes de Stock
```
ws://localhost:8000/ws/alerts/
```

### Dashboard Temps Réel
```
ws://localhost:8000/ws/dashboard/
```

## 🎨 Utilisation dans les Composants

### Authentification
```tsx
import { useAuth } from '@/contexts/AuthContextBackend';

const MyComponent = () => {
  const { user, login, logout, isAuthenticated } = useAuth();
  
  // Utilisation...
};
```

### API Calls avec React Query
```tsx
import { useProducts, useCreateProduct } from '@/hooks/useApi';

const ProductsPage = () => {
  const { data: products, isLoading } = useProducts();
  const createProduct = useCreateProduct();
  
  const handleCreate = (productData) => {
    createProduct.mutate(productData);
  };
  
  // Rendu...
};
```

### WebSockets
```tsx
import { useNotificationWebSocket } from '@/hooks/useWebSocket';

const App = () => {
  const { isConnected } = useNotificationWebSocket();
  
  // Les notifications apparaîtront automatiquement
};
```

## 🔐 Authentification et Sécurité

### Tokens JWT
- **Access Token**: Valide 24h, stocké dans localStorage
- **Refresh Token**: Valide 7 jours, rotation automatique
- **Auto-refresh**: Avant expiration du token

### Permissions
- **Admin**: Accès complet
- **Gérant**: Gestion opérationnelle
- **Serveur**: Ventes et consultation

### Sécurité
- Tokens automatiquement supprimés en cas d'erreur 401
- Vérification d'activité utilisateur
- Déconnexion automatique après inactivité

## 🐛 Débogage

### Logs WebSocket
```javascript
// Dans la console du navigateur
localStorage.setItem('debug', 'websocket');
```

### Logs API
```javascript
// Activer les logs détaillés
localStorage.setItem('debug', 'api');
```

### Mode Développement
```env
VITE_DEV_MODE=true
```

## 📊 Comptes de Test

### Administrateur
- **Username**: admin
- **Password**: admin123
- **Permissions**: Toutes

### Gérant
- **Username**: gerant
- **Password**: gerant123
- **Permissions**: Gestion opérationnelle

### Serveur
- **Username**: serveur1
- **Password**: serveur123
- **Permissions**: Ventes et consultation

## 🔄 Migration depuis Mock Data

### Avant (Mock)
```tsx
// Données statiques dans le code
const mockUsers = [...];
```

### Après (API)
```tsx
// Données dynamiques depuis l'API
const { data: users } = useUsers();
```

## 🎉 Fonctionnalités Temps Réel

### Notifications Automatiques
- Nouvelles ventes
- Alertes de stock
- Erreurs système
- Confirmations d'actions

### Dashboard Live
- Ventes en temps réel
- Stock mis à jour automatiquement
- Alertes instantanées

### Synchronisation Multi-Utilisateurs
- Modifications visibles immédiatement
- Conflits de données évités
- État cohérent entre utilisateurs

## 📈 Performance

### Optimisations
- Cache React Query intelligent
- Lazy loading des composants
- WebSocket avec reconnexion automatique
- Pagination automatique des APIs

### Métriques
- Temps de réponse API < 200ms
- Reconnexion WebSocket < 5s
- Cache invalidation intelligente

---

**BarStockWise** - Intégration Frontend-Backend Complète
*De la gestion mock à l'API temps réel !*
