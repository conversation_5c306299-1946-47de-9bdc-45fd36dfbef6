{"name": "bar-stock-wise-frontend-improvements", "version": "1.0.0", "description": "Améliorations frontend pour BarStockWise", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "type-check": "tsc --noEmit"}, "dependencies": {"@tanstack/react-query": "^5.0.0", "axios": "^1.6.0", "react": "^18.2.0", "react-dom": "^18.2.0", "sonner": "^1.4.0"}, "devDependencies": {"@types/node": "^20.0.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@vitejs/plugin-react": "^4.2.0", "typescript": "^5.0.0", "vite": "^5.0.0"}}