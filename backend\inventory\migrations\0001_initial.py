# Generated by Django 5.2.4 on 2025-07-30 14:12

import django.core.validators
import django.db.models.deletion
from decimal import Decimal
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('products', '0001_initial'),
        ('suppliers', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Purchase',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('reference', models.CharField(max_length=100, unique=True, verbose_name="Référence d'achat")),
                ('status', models.CharField(choices=[('pending', 'En attente'), ('received', 'Reçu'), ('partial', 'Partiel'), ('cancelled', 'Annulé')], default='pending', max_length=20, verbose_name='Statut')),
                ('order_date', models.DateTimeField(auto_now_add=True, verbose_name='Date de commande')),
                ('delivery_date', models.DateTimeField(blank=True, null=True, verbose_name='Date de livraison')),
                ('total_amount', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=12, validators=[django.core.validators.MinValueValidator(Decimal('0.00'))], verbose_name='Montant total (BIF)')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='Notes')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Date de création')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Date de modification')),
                ('supplier', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='purchases', to='suppliers.supplier', verbose_name='Fournisseur')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='Utilisateur')),
            ],
            options={
                'verbose_name': 'Achat',
                'verbose_name_plural': 'Achats',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='StockMovement',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('movement_type', models.CharField(choices=[('in', 'Entrée'), ('out', 'Sortie'), ('adjustment', 'Ajustement'), ('loss', 'Perte'), ('return', 'Retour')], max_length=20, verbose_name='Type de mouvement')),
                ('reason', models.CharField(choices=[('purchase', 'Achat'), ('sale', 'Vente'), ('inventory', 'Inventaire'), ('damage', 'Dommage'), ('expiry', 'Expiration'), ('theft', 'Vol'), ('correction', 'Correction')], max_length=20, verbose_name='Raison')),
                ('quantity', models.PositiveIntegerField(verbose_name='Quantité')),
                ('unit_price', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, validators=[django.core.validators.MinValueValidator(Decimal('0.00'))], verbose_name='Prix unitaire (BIF)')),
                ('total_amount', models.DecimalField(blank=True, decimal_places=2, max_digits=12, null=True, validators=[django.core.validators.MinValueValidator(Decimal('0.00'))], verbose_name='Montant total (BIF)')),
                ('stock_before', models.PositiveIntegerField(verbose_name='Stock avant')),
                ('stock_after', models.PositiveIntegerField(verbose_name='Stock après')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='Notes')),
                ('reference', models.CharField(blank=True, max_length=100, null=True, verbose_name='Référence')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Date de création')),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='stock_movements', to='products.product', verbose_name='Produit')),
                ('supplier', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='suppliers.supplier', verbose_name='Fournisseur')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='Utilisateur')),
            ],
            options={
                'verbose_name': 'Mouvement de stock',
                'verbose_name_plural': 'Mouvements de stock',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='PurchaseItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('quantity_ordered', models.PositiveIntegerField(verbose_name='Quantité commandée')),
                ('quantity_received', models.PositiveIntegerField(default=0, verbose_name='Quantité reçue')),
                ('unit_price', models.DecimalField(decimal_places=2, max_digits=10, validators=[django.core.validators.MinValueValidator(Decimal('0.00'))], verbose_name='Prix unitaire (BIF)')),
                ('total_price', models.DecimalField(decimal_places=2, max_digits=12, validators=[django.core.validators.MinValueValidator(Decimal('0.00'))], verbose_name='Prix total (BIF)')),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='products.product', verbose_name='Produit')),
                ('purchase', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='items', to='inventory.purchase', verbose_name='Achat')),
            ],
            options={
                'verbose_name': "Article d'achat",
                'verbose_name_plural': "Articles d'achat",
                'unique_together': {('purchase', 'product')},
            },
        ),
    ]
