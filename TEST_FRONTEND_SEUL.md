# 🧪 Test du Frontend Seul - BarStockWise

## 🎯 Objectif
Tester le frontend React avec l'intégration backend préparée, mais sans démarrer le serveur Django.

## 🔧 Configuration pour Test Frontend Seul

### 1. **Variables d'Environnement**
Le fichier `.env` est configuré pour fonctionner sans backend :
```env
# API Backend (sera en erreur mais géré gracieusement)
VITE_API_URL=http://localhost:8000/api

# WebSocket (désactivé)
VITE_WS_URL=ws://localhost:8000/ws

# Mode développement
VITE_DEV_MODE=true

# Notifications (désactivées)
VITE_ENABLE_NOTIFICATIONS=false
```

### 2. **Fonctionnalités Testables**

#### ✅ **Interface Utilisateur**
- Navigation entre les pages
- Composants UI (boutons, formulaires, tableaux)
- Responsive design
- Thème sombre/clair

#### ✅ **Authentification Mock**
- Page de connexion fonctionnelle
- Gestion des sessions
- Redirection après connexion
- Déconnexion

#### ✅ **Permissions et Rôles**
- Affichage conditionnel selon les rôles
- Menu adapté aux permissions
- Composants protégés

#### ❌ **Fonctionnalités Nécessitant le Backend**
- Appels API réels
- Sauvegarde des données
- WebSockets temps réel
- Export PDF avec données réelles

## 🚀 **Démarrage du Test**

### 1. **Démarrer le Frontend**
```powershell
# Dans le dossier bar-stock-wise
npm run dev
```

### 2. **Accéder à l'Application**
- Ouvrez http://localhost:5173
- Vous verrez la page de connexion

### 3. **Comptes de Test Mock**
Utilisez les comptes mock intégrés :

#### Administrateur
- **Username** : `admin`
- **Password** : `admin123`
- **Accès** : Tous les modules

#### Gérant
- **Username** : `gerant`
- **Password** : `gerant123`
- **Accès** : Gestion opérationnelle

#### Serveur
- **Username** : `serveur1`
- **Password** : `serveur123`
- **Accès** : Ventes et consultation

## 🧪 **Scénarios de Test**

### Test 1 : Authentification
1. ✅ Connectez-vous avec `admin` / `admin123`
2. ✅ Vérifiez la redirection vers le dashboard
3. ✅ Vérifiez l'affichage du nom d'utilisateur
4. ✅ Testez la déconnexion

### Test 2 : Navigation
1. ✅ Testez tous les liens du menu
2. ✅ Vérifiez que les pages se chargent
3. ✅ Testez le breadcrumb
4. ✅ Vérifiez la navigation mobile

### Test 3 : Permissions
1. ✅ Connectez-vous en tant que serveur
2. ✅ Vérifiez que certains menus sont cachés
3. ✅ Connectez-vous en tant qu'admin
4. ✅ Vérifiez l'accès complet

### Test 4 : Interface Utilisateur
1. ✅ Testez le thème sombre/clair
2. ✅ Testez la responsivité mobile
3. ✅ Vérifiez les formulaires
4. ✅ Testez les modales et dialogs

### Test 5 : Gestion d'Erreurs API
1. ⚠️ Tentez de créer un produit (erreur attendue)
2. ✅ Vérifiez que l'erreur est affichée proprement
3. ✅ Vérifiez que l'application reste stable
4. ✅ Testez la gestion des timeouts

## 📊 **Résultats Attendus**

### ✅ **Fonctionnel**
- Interface utilisateur complète
- Navigation fluide
- Authentification mock
- Gestion des permissions
- Responsive design
- Thèmes

### ⚠️ **Erreurs Attendues**
- Erreurs de connexion API (normales)
- WebSockets désactivés (normal)
- Données mock au lieu de données réelles
- Exports PDF avec données de test

### ❌ **Erreurs Critiques**
Si vous voyez ces erreurs, il faut les corriger :
- Erreurs de compilation TypeScript
- Composants qui ne se chargent pas
- Erreurs de contexte React
- Navigation cassée

## 🔄 **Passage au Backend Complet**

Une fois le frontend testé, pour activer le backend :

### 1. **Démarrer le Backend Django**
```powershell
.\start-backend.ps1
```

### 2. **Activer les WebSockets**
```env
# Dans .env
VITE_ENABLE_NOTIFICATIONS=true
```

### 3. **Redémarrer le Frontend**
```powershell
# Ctrl+C puis
npm run dev
```

### 4. **Tester l'Intégration Complète**
- Connexion avec comptes Django
- APIs fonctionnelles
- WebSockets actifs
- Données persistantes

## 🎯 **Objectifs du Test**

1. ✅ **Valider l'Interface** : S'assurer que l'UI est complète et fonctionnelle
2. ✅ **Tester la Navigation** : Vérifier que toutes les pages sont accessibles
3. ✅ **Valider les Permissions** : S'assurer que les rôles fonctionnent
4. ✅ **Préparer l'Intégration** : Identifier les points d'intégration avec le backend

## 📝 **Notes de Développement**

- Les erreurs API sont normales en mode frontend seul
- Les WebSockets sont désactivés pour éviter les erreurs
- L'authentification utilise les données mock
- Les données affichées sont des exemples statiques

---

**🎉 Résultat** : Frontend React fonctionnel avec intégration backend préparée !
**🚀 Prochaine étape** : Démarrer le backend Django pour l'intégration complète.
