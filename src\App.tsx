import React, { lazy } from "react";
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route, Navigate } from "react-router-dom";
import { AuthProvider } from "@/contexts/AuthContextBackend";
import { PermissionsProvider } from "@/contexts/PermissionsContext";
import DashboardLayout from "./components/layout/DashboardLayout";
import PageLoader from "./components/layout/PageLoader";
import WebSocketProvider from "./components/WebSocketProvider";
import ErrorBoundary from "./components/ErrorBoundary";

// Lazy loading des pages
// Temporarily import Login directly to debug hook issue
import Login from "./pages/Login";
// const Login = lazy(() => import("./pages/Login"));
const Dashboard = lazy(() => import("./pages/Dashboard"));
const Products = lazy(() => import("./pages/Products"));
const Users = lazy(() => import("./pages/Users"));
const Sales = lazy(() => import("./pages/Sales"));
const SalesHistory = lazy(() => import("./pages/SalesHistory"));
const Stocks = lazy(() => import("./pages/Stocks"));
const Supplies = lazy(() => import("./pages/Supplies"));
const Expenses = lazy(() => import("./pages/Expenses"));
const Reports = lazy(() => import("./pages/Reports"));
const Settings = lazy(() => import("./pages/Settings"));
const Help = lazy(() => import("./pages/Help"));
const Profile = lazy(() => import("./pages/Profile"));
const Suppliers = lazy(() => import("./pages/Suppliers"));
const Tables = lazy(() => import("./pages/Tables"));
const Orders = lazy(() => import("./pages/Orders"));
const Receipts = lazy(() => import("./pages/Receipts"));
const Invoices = lazy(() => import("./pages/Invoices"));
const Alerts = lazy(() => import("./pages/Alerts"));
const DailyReport = lazy(() => import("./pages/DailyReport"));
const Analytics = lazy(() => import("./pages/Analytics"));
const Monitoring = lazy(() => import("./pages/Monitoring"));
const NotificationTest = lazy(() => import("./pages/NotificationTest"));
const ApiTest = lazy(() => import("./pages/ApiTest"));
const TestNavigation = lazy(() => import("./pages/TestNavigation"));
const NotFound = lazy(() => import("./pages/NotFound"));

const queryClient = new QueryClient();

const App = () => (
  <ErrorBoundary>
    <QueryClientProvider client={queryClient}>
      <AuthProvider>
        <PermissionsProvider>
          <WebSocketProvider>
            <TooltipProvider>
            <Toaster />
            <Sonner />
            <BrowserRouter>
          <Routes>
            <Route path="/login" element={<Login />} />
            <Route path="/" element={<Navigate to={localStorage.getItem('lastVisitedPage') || '/dashboard'} replace />} />
            <Route path="/dashboard" element={
              <DashboardLayout>
                <PageLoader>
                  <Dashboard />
                </PageLoader>
              </DashboardLayout>
            } />
            <Route path="/products" element={
              <DashboardLayout>
                <PageLoader>
                  <Products />
                </PageLoader>
              </DashboardLayout>
            } />
            <Route path="/users" element={
              <DashboardLayout>
                <PageLoader>
                  <Users />
                </PageLoader>
              </DashboardLayout>
            } />
            <Route path="/sales" element={
              <DashboardLayout>
                <PageLoader>
                  <Sales />
                </PageLoader>
              </DashboardLayout>
            } />
            <Route path="/sales-history" element={
              <DashboardLayout>
                <PageLoader>
                  <SalesHistory />
                </PageLoader>
              </DashboardLayout>
            } />
            <Route path="/stocks" element={
              <DashboardLayout>
                <PageLoader>
                  <Stocks />
                </PageLoader>
              </DashboardLayout>
            } />
            <Route path="/supplies" element={
              <DashboardLayout>
                <PageLoader>
                  <Supplies />
                </PageLoader>
              </DashboardLayout>
            } />
            <Route path="/expenses" element={
              <DashboardLayout>
                <PageLoader>
                  <Expenses />
                </PageLoader>
              </DashboardLayout>
            } />
            <Route path="/reports" element={
              <DashboardLayout>
                <PageLoader>
                  <Reports />
                </PageLoader>
              </DashboardLayout>
            } />
            <Route path="/settings" element={
              <DashboardLayout>
                <PageLoader>
                  <Settings />
                </PageLoader>
              </DashboardLayout>
            } />
            <Route path="/help" element={
              <DashboardLayout>
                <PageLoader>
                  <Help />
                </PageLoader>
              </DashboardLayout>
            } />
            <Route path="/profile" element={
              <DashboardLayout>
                <PageLoader>
                  <Profile />
                </PageLoader>
              </DashboardLayout>
            } />
            <Route path="/suppliers" element={
              <DashboardLayout>
                <PageLoader>
                  <Suppliers />
                </PageLoader>
              </DashboardLayout>
            } />
            <Route path="/tables" element={
              <DashboardLayout>
                <PageLoader>
                  <Tables />
                </PageLoader>
              </DashboardLayout>
            } />
            <Route path="/alerts" element={
              <DashboardLayout>
                <PageLoader>
                  <Alerts />
                </PageLoader>
              </DashboardLayout>
            } />
            <Route path="/invoices" element={
              <DashboardLayout>
                <PageLoader>
                  <Invoices />
                </PageLoader>
              </DashboardLayout>
            } />
            <Route path="/daily-report" element={
              <DashboardLayout>
                <PageLoader>
                  <DailyReport />
                </PageLoader>
              </DashboardLayout>
            } />
            <Route path="/analytics" element={
              <DashboardLayout>
                <PageLoader>
                  <Analytics />
                </PageLoader>
              </DashboardLayout>
            } />
            <Route path="/monitoring" element={
              <DashboardLayout>
                <PageLoader>
                  <Monitoring />
                </PageLoader>
              </DashboardLayout>
            } />
            <Route path="/notification-test" element={
              <DashboardLayout>
                <PageLoader>
                  <NotificationTest />
                </PageLoader>
              </DashboardLayout>
            } />
            {/* ADD ALL CUSTOM ROUTES ABOVE THE CATCH-ALL "*" ROUTE */}
            <Route path="*" element={
              <PageLoader>
                <NotFound />
              </PageLoader>
            } />
          </Routes>
        </BrowserRouter>
        </TooltipProvider>
        </WebSocketProvider>
      </PermissionsProvider>
    </AuthProvider>
  </QueryClientProvider>
  </ErrorBoundary>
);

export default App;
