import json
from channels.generic.websocket import AsyncWebsocketConsumer
from channels.db import database_sync_to_async
from django.contrib.auth import get_user_model
from django.utils import timezone
from datetime import datetime, timedelta

User = get_user_model()

class NotificationConsumer(AsyncWebsocketConsumer):
    """Consumer pour les notifications personnalisées par utilisateur"""
    
    async def connect(self):
        self.user_id = self.scope['url_route']['kwargs']['user_id']
        self.room_group_name = f'notifications_{self.user_id}'
        
        # Rejoindre le groupe de notifications de l'utilisateur
        await self.channel_layer.group_add(
            self.room_group_name,
            self.channel_name
        )
        
        await self.accept()
        
        # Envoyer un message de bienvenue
        await self.send(text_data=json.dumps({
            'type': 'connection_established',
            'message': 'Connexion aux notifications établie',
            'timestamp': timezone.now().isoformat()
        }))
    
    async def disconnect(self, close_code):
        # Quitter le groupe
        await self.channel_layer.group_discard(
            self.room_group_name,
            self.channel_name
        )
    
    async def receive(self, text_data):
        """Recevoir des messages du client"""
        try:
            text_data_json = json.loads(text_data)
            message_type = text_data_json.get('type', 'ping')
            
            if message_type == 'ping':
                await self.send(text_data=json.dumps({
                    'type': 'pong',
                    'timestamp': timezone.now().isoformat()
                }))
        except json.JSONDecodeError:
            await self.send(text_data=json.dumps({
                'type': 'error',
                'message': 'Format JSON invalide'
            }))
    
    # Handlers pour différents types de notifications
    async def stock_alert(self, event):
        """Envoyer une alerte de stock"""
        await self.send(text_data=json.dumps({
            'type': 'stock_alert',
            'alert': event['alert'],
            'timestamp': timezone.now().isoformat()
        }))
    
    async def sale_notification(self, event):
        """Envoyer une notification de vente"""
        await self.send(text_data=json.dumps({
            'type': 'sale_notification',
            'sale': event['sale'],
            'timestamp': timezone.now().isoformat()
        }))
    
    async def system_notification(self, event):
        """Envoyer une notification système"""
        await self.send(text_data=json.dumps({
            'type': 'system_notification',
            'message': event['message'],
            'level': event.get('level', 'info'),
            'timestamp': timezone.now().isoformat()
        }))

class AlertConsumer(AsyncWebsocketConsumer):
    """Consumer pour les alertes globales (stock, système)"""
    
    async def connect(self):
        self.room_group_name = 'global_alerts'
        
        # Rejoindre le groupe d'alertes globales
        await self.channel_layer.group_add(
            self.room_group_name,
            self.channel_name
        )
        
        await self.accept()
        
        # Envoyer les alertes actives au moment de la connexion
        await self.send_active_alerts()
    
    async def disconnect(self, close_code):
        await self.channel_layer.group_discard(
            self.room_group_name,
            self.channel_name
        )
    
    @database_sync_to_async
    def get_active_alerts(self):
        """Récupérer les alertes actives"""
        from .models import StockAlert
        from products.models import Product
        
        alerts = []
        
        # Alertes de stock
        stock_alerts = StockAlert.objects.filter(status='active').select_related('product')
        for alert in stock_alerts:
            alerts.append({
                'id': alert.id,
                'type': 'stock_alert',
                'product_name': alert.product.name,
                'current_stock': alert.product.current_stock,
                'minimum_stock': alert.product.minimum_stock,
                'alert_type': alert.alert_type,
                'created_at': alert.created_at.isoformat()
            })
        
        # Produits en rupture de stock
        out_of_stock = Product.objects.filter(current_stock=0, is_active=True)
        for product in out_of_stock:
            alerts.append({
                'type': 'out_of_stock',
                'product_name': product.name,
                'category': product.category.name,
                'current_stock': 0
            })
        
        return alerts
    
    async def send_active_alerts(self):
        """Envoyer les alertes actives"""
        alerts = await self.get_active_alerts()
        await self.send(text_data=json.dumps({
            'type': 'active_alerts',
            'alerts': alerts,
            'count': len(alerts),
            'timestamp': timezone.now().isoformat()
        }))
    
    # Handlers pour les alertes
    async def new_stock_alert(self, event):
        """Nouvelle alerte de stock"""
        await self.send(text_data=json.dumps({
            'type': 'new_stock_alert',
            'alert': event['alert'],
            'timestamp': timezone.now().isoformat()
        }))
    
    async def alert_resolved(self, event):
        """Alerte résolue"""
        await self.send(text_data=json.dumps({
            'type': 'alert_resolved',
            'alert_id': event['alert_id'],
            'timestamp': timezone.now().isoformat()
        }))

class DashboardConsumer(AsyncWebsocketConsumer):
    """Consumer pour les mises à jour du tableau de bord en temps réel"""
    
    async def connect(self):
        self.room_group_name = 'dashboard_updates'
        
        await self.channel_layer.group_add(
            self.room_group_name,
            self.channel_name
        )
        
        await self.accept()
        
        # Envoyer les statistiques actuelles
        await self.send_dashboard_stats()
    
    async def disconnect(self, close_code):
        await self.channel_layer.group_discard(
            self.room_group_name,
            self.channel_name
        )
    
    @database_sync_to_async
    def get_dashboard_stats(self):
        """Récupérer les statistiques du tableau de bord"""
        from .models import DailyReport
        from sales.models import Sale
        from products.models import Product
        
        today = timezone.now().date()
        
        # Statistiques du jour
        today_sales = Sale.objects.filter(created_at__date=today).count()
        today_revenue = Sale.objects.filter(
            created_at__date=today, 
            status='completed'
        ).aggregate(
            total=models.Sum('total_amount')
        )['total'] or 0
        
        # Alertes
        active_alerts = StockAlert.objects.filter(status='active').count()
        low_stock_products = Product.objects.filter(
            current_stock__lte=models.F('minimum_stock'),
            is_active=True
        ).count()
        
        return {
            'today_sales': today_sales,
            'today_revenue': float(today_revenue),
            'active_alerts': active_alerts,
            'low_stock_products': low_stock_products,
            'total_products': Product.objects.filter(is_active=True).count()
        }
    
    async def send_dashboard_stats(self):
        """Envoyer les statistiques du tableau de bord"""
        stats = await self.get_dashboard_stats()
        await self.send(text_data=json.dumps({
            'type': 'dashboard_stats',
            'stats': stats,
            'timestamp': timezone.now().isoformat()
        }))
    
    # Handlers pour les mises à jour
    async def stats_update(self, event):
        """Mise à jour des statistiques"""
        await self.send(text_data=json.dumps({
            'type': 'stats_update',
            'stats': event['stats'],
            'timestamp': timezone.now().isoformat()
        }))
    
    async def new_sale(self, event):
        """Nouvelle vente"""
        await self.send(text_data=json.dumps({
            'type': 'new_sale',
            'sale': event['sale'],
            'timestamp': timezone.now().isoformat()
        }))
