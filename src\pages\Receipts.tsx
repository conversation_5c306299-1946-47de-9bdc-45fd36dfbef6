import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { 
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { 
  Receipt, 
  Search, 
  Download, 
  Printer,
  Eye,
  Calendar,
  DollarSign,
  FileText,
  Filter
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { formatCurrency } from '@/lib/currency';

// Types pour les reçus
interface ReceiptItem {
  id: string;
  name: string;
  quantity: number;
  price: number;
  total: number;
}

interface ReceiptData {
  id: string;
  receiptNumber: string;
  customerName?: string;
  tableNumber: string;
  items: ReceiptItem[];
  subtotal: number;
  tax: number;
  total: number;
  paymentMethod: 'Espèces' | 'Carte' | 'Mobile Money' | 'Crédit';
  status: 'Payé' | 'En attente' | 'Remboursé' | 'Annulé';
  createdAt: string;
  cashier: string;
  notes?: string;
}

// Données mock
const mockReceipts: ReceiptData[] = [
  {
    id: '1',
    receiptNumber: 'REC-20240115-001',
    customerName: 'Jean Hakizimana',
    tableNumber: 'Table 5',
    items: [
      { id: '1', name: 'Primus', quantity: 2, price: 3500, total: 7000 },
      { id: '2', name: 'Poulet yassa', quantity: 1, price: 8000, total: 8000 }
    ],
    subtotal: 15000,
    tax: 0,
    total: 15000,
    paymentMethod: 'Espèces',
    status: 'Payé',
    createdAt: '2024-01-15T14:30:00',
    cashier: 'Marie Uwimana'
  },
  {
    id: '2',
    receiptNumber: 'REC-20240115-002',
    tableNumber: 'Table 2',
    items: [
      { id: '3', name: 'Chivas', quantity: 1, price: 25000, total: 25000 },
      { id: '4', name: 'Amstel', quantity: 3, price: 4000, total: 12000 }
    ],
    subtotal: 37000,
    tax: 0,
    total: 37000,
    paymentMethod: 'Carte',
    status: 'Payé',
    createdAt: '2024-01-15T14:45:00',
    cashier: 'Paul Ndayisenga'
  },
  {
    id: '3',
    receiptNumber: 'REC-20240115-003',
    customerName: 'Alice Niragire',
    tableNumber: 'Table 8',
    items: [
      { id: '5', name: 'Pizza Chicken', quantity: 2, price: 12000, total: 24000 },
      { id: '6', name: 'Fanta', quantity: 2, price: 2000, total: 4000 }
    ],
    subtotal: 28000,
    tax: 0,
    total: 28000,
    paymentMethod: 'Mobile Money',
    status: 'En attente',
    createdAt: '2024-01-15T13:20:00',
    cashier: 'Marie Uwimana'
  }
];

const Receipts = () => {
  const [receipts, setReceipts] = useState<ReceiptData[]>(mockReceipts);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('Tous');
  const [paymentFilter, setPaymentFilter] = useState('Tous');
  const [selectedReceipt, setSelectedReceipt] = useState<ReceiptData | null>(null);
  const { toast } = useToast();

  const filteredReceipts = receipts.filter(receipt => {
    const matchesSearch = receipt.receiptNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         receipt.tableNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         (receipt.customerName?.toLowerCase().includes(searchTerm.toLowerCase()) ?? false);
    const matchesStatus = statusFilter === 'Tous' || receipt.status === statusFilter;
    const matchesPayment = paymentFilter === 'Tous' || receipt.paymentMethod === paymentFilter;
    
    return matchesSearch && matchesStatus && matchesPayment;
  });

  const getStatusBadge = (status: string) => {
    const variants = {
      'Payé': 'success' as const,
      'En attente': 'warning' as const,
      'Remboursé': 'secondary' as const,
      'Annulé': 'destructive' as const
    };
    
    return (
      <Badge variant={variants[status as keyof typeof variants] || 'outline'}>
        {status}
      </Badge>
    );
  };

  const getPaymentMethodBadge = (method: string) => {
    const variants = {
      'Espèces': 'default' as const,
      'Carte': 'secondary' as const,
      'Mobile Money': 'outline' as const,
      'Crédit': 'warning' as const
    };
    
    return (
      <Badge variant={variants[method as keyof typeof variants] || 'outline'}>
        {method}
      </Badge>
    );
  };

  const printReceipt = (receipt: ReceiptData) => {
    // Créer le contenu du reçu
    const receiptContent = `
      <!DOCTYPE html>
      <html>
        <head>
          <title>Reçu ${receipt.receiptNumber}</title>
          <style>
            body { font-family: monospace; max-width: 300px; margin: 0 auto; padding: 20px; }
            .header { text-align: center; margin-bottom: 20px; }
            .divider { border-top: 1px dashed #000; margin: 10px 0; }
            .item { display: flex; justify-content: space-between; margin: 5px 0; }
            .total { font-weight: bold; font-size: 1.1em; }
          </style>
        </head>
        <body>
          <div class="header">
            <h2>BarStock Wise</h2>
            <p>Bujumbura, Burundi</p>
          </div>
          <div class="divider"></div>
          <p><strong>Reçu:</strong> ${receipt.receiptNumber}</p>
          <p><strong>Table:</strong> ${receipt.tableNumber}</p>
          ${receipt.customerName ? `<p><strong>Client:</strong> ${receipt.customerName}</p>` : ''}
          <p><strong>Date:</strong> ${new Date(receipt.createdAt).toLocaleString('fr-FR')}</p>
          <p><strong>Caissier:</strong> ${receipt.cashier}</p>
          <div class="divider"></div>
          ${receipt.items.map(item => `
            <div class="item">
              <span>${item.name} x${item.quantity}</span>
              <span>${formatCurrency(item.total)}</span>
            </div>
          `).join('')}
          <div class="divider"></div>
          <div class="item total">
            <span>TOTAL</span>
            <span>${formatCurrency(receipt.total)}</span>
          </div>
          <div class="divider"></div>
          <p><strong>Paiement:</strong> ${receipt.paymentMethod}</p>
          <div class="header">
            <p>Merci de votre visite!</p>
          </div>
        </body>
      </html>
    `;

    // Ouvrir dans une nouvelle fenêtre et imprimer
    const printWindow = window.open('', '_blank');
    if (printWindow) {
      printWindow.document.write(receiptContent);
      printWindow.document.close();
      printWindow.print();
    }

    toast({
      title: "Impression en cours",
      description: "Le reçu est en cours d'impression.",
    });
  };

  const exportReceipt = (receipt: ReceiptData) => {
    // Créer un export simple en texte
    const exportData = {
      receiptNumber: receipt.receiptNumber,
      customerName: receipt.customerName,
      tableNumber: receipt.tableNumber,
      items: receipt.items,
      total: receipt.total,
      paymentMethod: receipt.paymentMethod,
      status: receipt.status,
      createdAt: receipt.createdAt,
      cashier: receipt.cashier
    };

    const dataStr = JSON.stringify(exportData, null, 2);
    const dataUri = 'data:application/json;charset=utf-8,'+ encodeURIComponent(dataStr);
    
    const exportFileDefaultName = `receipt_${receipt.receiptNumber}.json`;
    
    const linkElement = document.createElement('a');
    linkElement.setAttribute('href', dataUri);
    linkElement.setAttribute('download', exportFileDefaultName);
    linkElement.click();

    toast({
      title: "Export réussi",
      description: "Le reçu a été exporté avec succès.",
    });
  };

  const getStats = () => {
    const paid = receipts.filter(r => r.status === 'Payé').length;
    const pending = receipts.filter(r => r.status === 'En attente').length;
    const totalRevenue = receipts
      .filter(r => r.status === 'Payé')
      .reduce((sum, receipt) => sum + receipt.total, 0);
    const averageTicket = totalRevenue / (paid || 1);
    
    return { paid, pending, totalRevenue, averageTicket };
  };

  const stats = getStats();

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Gestion des Reçus</h1>
          <p className="text-muted-foreground">
            Consultez, imprimez et exportez tous vos reçus de vente
          </p>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Reçus Payés</CardTitle>
            <Receipt className="h-4 w-4 text-success" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-success">{stats.paid}</div>
            <p className="text-xs text-muted-foreground">transactions réussies</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">En Attente</CardTitle>
            <Calendar className="h-4 w-4 text-warning" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-warning">{stats.pending}</div>
            <p className="text-xs text-muted-foreground">paiements en cours</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Chiffre d'Affaires</CardTitle>
            <DollarSign className="h-4 w-4 text-primary" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(stats.totalRevenue)}</div>
            <p className="text-xs text-muted-foreground">total encaissé</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Ticket Moyen</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(stats.averageTicket)}</div>
            <p className="text-xs text-muted-foreground">par transaction</p>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Rechercher un reçu..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-40">
                <SelectValue placeholder="Statut" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="Tous">Tous statuts</SelectItem>
                <SelectItem value="Payé">Payé</SelectItem>
                <SelectItem value="En attente">En attente</SelectItem>
                <SelectItem value="Remboursé">Remboursé</SelectItem>
                <SelectItem value="Annulé">Annulé</SelectItem>
              </SelectContent>
            </Select>
            
            <Select value={paymentFilter} onValueChange={setPaymentFilter}>
              <SelectTrigger className="w-40">
                <SelectValue placeholder="Paiement" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="Tous">Tous moyens</SelectItem>
                <SelectItem value="Espèces">Espèces</SelectItem>
                <SelectItem value="Carte">Carte</SelectItem>
                <SelectItem value="Mobile Money">Mobile Money</SelectItem>
                <SelectItem value="Crédit">Crédit</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Receipts Table */}
      <Card>
        <CardHeader>
          <CardTitle>Historique des Reçus</CardTitle>
          <CardDescription>
            {filteredReceipts.length} reçu(s) trouvé(s)
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>N° Reçu</TableHead>
                <TableHead>Table</TableHead>
                <TableHead>Client</TableHead>
                <TableHead>Total</TableHead>
                <TableHead>Paiement</TableHead>
                <TableHead>Statut</TableHead>
                <TableHead>Date</TableHead>
                <TableHead>Caissier</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredReceipts.map((receipt) => (
                <TableRow key={receipt.id}>
                  <TableCell className="font-medium">{receipt.receiptNumber}</TableCell>
                  <TableCell>{receipt.tableNumber}</TableCell>
                  <TableCell>{receipt.customerName || '-'}</TableCell>
                  <TableCell className="font-medium">{formatCurrency(receipt.total)}</TableCell>
                  <TableCell>{getPaymentMethodBadge(receipt.paymentMethod)}</TableCell>
                  <TableCell>{getStatusBadge(receipt.status)}</TableCell>
                  <TableCell>
                    {new Date(receipt.createdAt).toLocaleDateString('fr-FR')}
                  </TableCell>
                  <TableCell className="text-sm">{receipt.cashier}</TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <Button 
                        size="sm" 
                        variant="outline"
                        onClick={() => setSelectedReceipt(receipt)}
                      >
                        <Eye className="w-4 h-4" />
                      </Button>
                      
                      <Button 
                        size="sm" 
                        variant="outline"
                        onClick={() => printReceipt(receipt)}
                      >
                        <Printer className="w-4 h-4" />
                      </Button>
                      
                      <Button 
                        size="sm" 
                        variant="outline"
                        onClick={() => exportReceipt(receipt)}
                      >
                        <Download className="w-4 h-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
};

export default Receipts;