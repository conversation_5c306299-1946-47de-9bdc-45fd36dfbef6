import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, Ta<PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Activity,
  Bell,
  TrendingUp,
  Clock,
  AlertTriangle,
  Download,
  RefreshCw,
  Eye,
  Wifi,
  WifiOff,
  Server,
  Database,
  Zap,
  Shield,
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { useNotifications } from '@/lib/notificationService';

const Monitoring = () => {
  const { toast } = useToast();
  const notificationService = useNotifications();
  const [isOnline, setIsOnline] = useState(navigator.onLine);
  const [systemStatus, setSystemStatus] = useState({
    api: 'online',
    database: 'online',
    cache: 'active',
    notifications: 'enabled',
  });
  const [notifications, setNotifications] = useState({
    enabled: true,
    stockAlerts: true,
    salesNotifications: true,
    systemAlerts: true,
    soundEnabled: true,
  });

  useEffect(() => {
    // Écouter les changements de connexion
    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    // Vérifier le statut système périodiquement
    const interval = setInterval(checkSystemStatus, 30000);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
      clearInterval(interval);
    };
  }, []);

  const checkSystemStatus = async () => {
    try {
      // Simuler des vérifications de statut
      const apiStatus = await fetch('/api/health').then(() => 'online').catch(() => 'offline');
      
      setSystemStatus(prev => ({
        ...prev,
        api: apiStatus,
        database: apiStatus === 'online' ? 'online' : 'offline',
      }));
    } catch (error) {
      console.error('Erreur vérification statut:', error);
    }
  };

  const testNotification = async () => {
    try {
      const success = await notificationService.sendNotification({
        title: 'Test BarStock Wise',
        body: 'Notification de test réussie !',
        icon: '/icon-192x192.png',
      });

      if (success) {
        toast({
          title: "Test réussi",
          description: "Notification envoyée avec succès",
        });
      } else {
        toast({
          title: "Test échoué",
          description: "Impossible d'envoyer la notification",
          variant: "destructive",
        });
      }
    } catch (error) {
      toast({
        title: "Erreur",
        description: `Erreur lors du test: ${error instanceof Error ? error.message : 'Erreur inconnue'}`,
        variant: "destructive",
      });
    }
  };

  const clearCache = () => {
    if ('caches' in window) {
      caches.keys().then(names => {
        names.forEach(name => caches.delete(name));
      });
    }
    localStorage.clear();
    sessionStorage.clear();
    
    toast({
      title: "Cache vidé",
      description: "Tous les caches ont été supprimés",
    });
  };

  const exportLogs = () => {
    const logs = {
      timestamp: new Date().toISOString(),
      systemStatus,
      isOnline,
      notifications,
      userAgent: navigator.userAgent,
      performance: {
        memory: (performance as any).memory ? {
          used: (performance as any).memory.usedJSHeapSize,
          total: (performance as any).memory.totalJSHeapSize,
        } : null,
      },
    };

    const blob = new Blob([JSON.stringify(logs, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `monitoring-${new Date().toISOString().split('T')[0]}.json`;
    a.click();
    URL.revokeObjectURL(url);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Monitoring Système</h1>
          <p className="text-muted-foreground">
            Surveillance en temps réel et gestion des notifications
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" onClick={exportLogs}>
            <Download className="w-4 h-4 mr-2" />
            Export Logs
          </Button>
          <Button onClick={checkSystemStatus}>
            <RefreshCw className="w-4 h-4 mr-2" />
            Actualiser
          </Button>
        </div>
      </div>

      {/* Statut de connexion */}
      <Card className={`border-2 ${isOnline ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'}`}>
        <CardContent className="pt-4">
          <div className={`flex items-center gap-2 ${isOnline ? 'text-green-700' : 'text-red-700'}`}>
            {isOnline ? <Wifi className="h-5 w-5" /> : <WifiOff className="h-5 w-5" />}
            <span className="font-medium">
              {isOnline ? 'Connexion Active' : 'Mode Hors-ligne'}
            </span>
          </div>
          <p className={`text-sm mt-1 ${isOnline ? 'text-green-600' : 'text-red-600'}`}>
            {isOnline 
              ? 'Toutes les fonctionnalités sont disponibles'
              : 'Fonctionnalités limitées - Données en cache disponibles'
            }
          </p>
        </CardContent>
      </Card>

      {/* Métriques système */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">API Status</CardTitle>
            <Server className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <Badge variant={systemStatus.api === 'online' ? 'default' : 'destructive'}>
              {systemStatus.api}
            </Badge>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Base de Données</CardTitle>
            <Database className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <Badge variant={systemStatus.database === 'online' ? 'default' : 'destructive'}>
              {systemStatus.database}
            </Badge>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Cache</CardTitle>
            <Zap className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <Badge variant={systemStatus.cache === 'active' ? 'default' : 'secondary'}>
              {systemStatus.cache}
            </Badge>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Notifications</CardTitle>
            <Bell className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <Badge variant={notifications.enabled ? 'default' : 'secondary'}>
              {notifications.enabled ? 'enabled' : 'disabled'}
            </Badge>
          </CardContent>
        </Card>
      </div>

      {/* Onglets de configuration */}
      <Tabs defaultValue="notifications" className="space-y-4">
        <TabsList>
          <TabsTrigger value="notifications">Notifications</TabsTrigger>
          <TabsTrigger value="performance">Performance</TabsTrigger>
          <TabsTrigger value="security">Sécurité</TabsTrigger>
          <TabsTrigger value="maintenance">Maintenance</TabsTrigger>
        </TabsList>

        <TabsContent value="notifications" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Configuration des Notifications</CardTitle>
              <CardDescription>
                Gérez les alertes et notifications push
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <Label htmlFor="notifications-enabled">Notifications activées</Label>
                <Switch
                  id="notifications-enabled"
                  checked={notifications.enabled}
                  onCheckedChange={(checked) => 
                    setNotifications(prev => ({ ...prev, enabled: checked }))
                  }
                />
              </div>

              <div className="flex items-center justify-between">
                <Label htmlFor="stock-alerts">Alertes de stock</Label>
                <Switch
                  id="stock-alerts"
                  checked={notifications.stockAlerts}
                  onCheckedChange={(checked) => 
                    setNotifications(prev => ({ ...prev, stockAlerts: checked }))
                  }
                />
              </div>

              <div className="flex items-center justify-between">
                <Label htmlFor="sales-notifications">Notifications de ventes</Label>
                <Switch
                  id="sales-notifications"
                  checked={notifications.salesNotifications}
                  onCheckedChange={(checked) => 
                    setNotifications(prev => ({ ...prev, salesNotifications: checked }))
                  }
                />
              </div>

              <div className="flex items-center justify-between">
                <Label htmlFor="system-alerts">Alertes système</Label>
                <Switch
                  id="system-alerts"
                  checked={notifications.systemAlerts}
                  onCheckedChange={(checked) => 
                    setNotifications(prev => ({ ...prev, systemAlerts: checked }))
                  }
                />
              </div>

              <div className="flex items-center justify-between">
                <Label htmlFor="sound-enabled">Son activé</Label>
                <Switch
                  id="sound-enabled"
                  checked={notifications.soundEnabled}
                  onCheckedChange={(checked) => 
                    setNotifications(prev => ({ ...prev, soundEnabled: checked }))
                  }
                />
              </div>

              <div className="pt-4">
                <Button onClick={testNotification} variant="outline">
                  <Bell className="w-4 h-4 mr-2" />
                  Tester les Notifications
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="performance" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Optimisation des Performances</CardTitle>
              <CardDescription>
                Gestion du cache et optimisations
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid gap-4 md:grid-cols-2">
                <div className="space-y-2">
                  <h4 className="font-medium">Cache Navigateur</h4>
                  <p className="text-sm text-muted-foreground">
                    Vider le cache pour forcer le rechargement des données
                  </p>
                  <Button onClick={clearCache} variant="outline" size="sm">
                    Vider le Cache
                  </Button>
                </div>

                <div className="space-y-2">
                  <h4 className="font-medium">Service Worker</h4>
                  <p className="text-sm text-muted-foreground">
                    Statut du service worker pour le mode hors-ligne
                  </p>
                  <Badge variant="outline">
                    {'serviceWorker' in navigator ? 'Supporté' : 'Non supporté'}
                  </Badge>
                </div>
              </div>

              {(performance as any).memory && (
                <div className="space-y-2">
                  <h4 className="font-medium">Utilisation Mémoire</h4>
                  <div className="grid gap-2 md:grid-cols-2">
                    <div className="flex justify-between">
                      <span>Utilisée:</span>
                      <Badge variant="outline">
                        {Math.round((performance as any).memory.usedJSHeapSize / 1024 / 1024)}MB
                      </Badge>
                    </div>
                    <div className="flex justify-between">
                      <span>Totale:</span>
                      <Badge variant="outline">
                        {Math.round((performance as any).memory.totalJSHeapSize / 1024 / 1024)}MB
                      </Badge>
                    </div>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="security" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Sécurité et Permissions</CardTitle>
              <CardDescription>
                État des permissions et sécurité
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Permission</TableHead>
                    <TableHead>Statut</TableHead>
                    <TableHead>Description</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  <TableRow>
                    <TableCell>Notifications</TableCell>
                    <TableCell>
                      <Badge variant={Notification.permission === 'granted' ? 'default' : 'destructive'}>
                        {Notification.permission}
                      </Badge>
                    </TableCell>
                    <TableCell>Affichage des notifications push</TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell>Géolocalisation</TableCell>
                    <TableCell>
                      <Badge variant="secondary">Non utilisée</Badge>
                    </TableCell>
                    <TableCell>Localisation pour les livraisons</TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell>Stockage Local</TableCell>
                    <TableCell>
                      <Badge variant="default">Autorisé</Badge>
                    </TableCell>
                    <TableCell>Sauvegarde des données hors-ligne</TableCell>
                  </TableRow>
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="maintenance" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Outils de Maintenance</CardTitle>
              <CardDescription>
                Diagnostic et maintenance du système
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid gap-4 md:grid-cols-2">
                <Button onClick={checkSystemStatus} variant="outline">
                  <Activity className="w-4 h-4 mr-2" />
                  Vérifier le Système
                </Button>

                <Button onClick={exportLogs} variant="outline">
                  <Download className="w-4 h-4 mr-2" />
                  Exporter les Logs
                </Button>

                <Button onClick={clearCache} variant="outline">
                  <RefreshCw className="w-4 h-4 mr-2" />
                  Réinitialiser Cache
                </Button>

                <Button onClick={testNotification} variant="outline">
                  <Bell className="w-4 h-4 mr-2" />
                  Test Notifications
                </Button>
              </div>

              <div className="pt-4 border-t">
                <h4 className="font-medium mb-2">Informations Système</h4>
                <div className="grid gap-2 text-sm">
                  <div className="flex justify-between">
                    <span>Navigateur:</span>
                    <span>{navigator.userAgent.split(' ')[0]}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Plateforme:</span>
                    <span>{navigator.platform}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Langue:</span>
                    <span>{navigator.language}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Connexion:</span>
                    <Badge variant={isOnline ? 'default' : 'destructive'}>
                      {isOnline ? 'En ligne' : 'Hors ligne'}
                    </Badge>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default Monitoring;
