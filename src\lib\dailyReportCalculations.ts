import { DailyStockItem, StockAlert, AlertThresholds, DEFAULT_ALERT_THRESHOLDS } from '@/types/dailyReport';

export class DailyReportCalculator {
  private thresholds: AlertThresholds;

  constructor(thresholds: AlertThresholds = DEFAULT_ALERT_THRESHOLDS) {
    this.thresholds = thresholds;
  }

  // Calcul du stock total (stock initial + stock entrant)
  calculateStockTotal(stockInitial: number, stockEntrant: number): number {
    return stockInitial + stockEntrant;
  }

  // Calcul du stock restant (stock total - consommation - perte)
  calculateStockRestant(stockTotal: number, consommation: number, perte: number = 0): number {
    return Math.max(0, stockTotal - consommation - perte);
  }

  // Calcul de la marge unitaire
  calculateMarge(prixVente: number, prixAchat: number): number {
    return prixVente - prixAchat;
  }

  // Calcul du bénéfice total
  calculateBenefice(stockVendu: number, margeUnitaire: number): number {
    return stockVendu * margeUnitaire;
  }

  // Calcul du chiffre d'affaires
  calculateChiffredAffaires(stockVendu: number, prixVenteUnitaire: number): number {
    return stockVendu * prixVenteUnitaire;
  }

  // Validation et correction automatique des données
  validateAndCorrectItem(item: DailyStockItem): { correctedItem: DailyStockItem; alerts: StockAlert[] } {
    const alerts: StockAlert[] = [];
    const correctedItem = { ...item };

    // 1. Vérifier et corriger le stock total
    const expectedStockTotal = this.calculateStockTotal(item.stockInitial, item.stockEntrant);
    if (item.stockTotal !== expectedStockTotal) {
      alerts.push({
        id: `alert-${item.id}-stock-total`,
        productId: item.id,
        productName: item.produit,
        type: 'calculation_error',
        alertType: 'Incohérence',
        severity: 'Élevée',
        message: `Stock total incohérent: ${item.stockTotal} au lieu de ${expectedStockTotal}`,
        suggestedAction: `Corriger le stock total à ${expectedStockTotal}`
      });
      correctedItem.stockTotal = expectedStockTotal;
    }

    // 2. Vérifier et corriger le stock restant
    const expectedStockRestant = this.calculateStockRestant(
      correctedItem.stockTotal, 
      item.consommation, 
      item.perte
    );
    if (item.stockRestant !== expectedStockRestant) {
      alerts.push({
        id: `alert-${item.id}-stock-restant`,
        productId: item.id,
        productName: item.produit,
        type: 'calculation_error',
        alertType: 'Incohérence',
        severity: 'Élevée',
        message: `Stock restant incohérent: ${item.stockRestant} au lieu de ${expectedStockRestant}`,
        suggestedAction: `Corriger le stock restant à ${expectedStockRestant}`
      });
      correctedItem.stockRestant = expectedStockRestant;
    }

    // 3. Vérifier la cohérence consommation vs stock vendu
    if (item.consommation !== item.stockVendu && item.perte === 0) {
      const suggestedPerte = item.consommation - item.stockVendu;
      if (suggestedPerte > 0) {
        alerts.push({
          id: `alert-${item.id}-perte-manquante`,
          productId: item.id,
          productName: item.produit,
          type: 'calculation_error',
          alertType: 'Incohérence',
          severity: 'Moyenne',
          message: `Différence entre consommation (${item.consommation}) et vente (${item.stockVendu})`,
          suggestedAction: `Vérifier s'il y a ${suggestedPerte} unités de perte non déclarées`
        });
      }
    }

    // 4. Recalculer la marge si nécessaire
    if (item.prixAchatUnitaire > 0 && item.prixVenteUnitaire > 0) {
      const expectedMarge = this.calculateMarge(item.prixVenteUnitaire, item.prixAchatUnitaire);
      if (Math.abs(item.marge - expectedMarge) > 0.01) {
        correctedItem.marge = expectedMarge;
        alerts.push({
          id: `alert-${item.id}-marge`,
          productId: item.id,
          productName: item.produit,
          type: 'calculation_error',
          alertType: 'Incohérence',
          severity: 'Moyenne',
          message: `Marge recalculée: ${expectedMarge.toFixed(2)} BIF`,
          suggestedAction: 'Marge corrigée automatiquement'
        });
      }
    }

    // 5. Recalculer le bénéfice
    const expectedBenefice = this.calculateBenefice(item.stockVendu, correctedItem.marge);
    if (Math.abs(item.benefice - expectedBenefice) > 0.01) {
      correctedItem.benefice = expectedBenefice;
      alerts.push({
        id: `alert-${item.id}-benefice`,
        productId: item.id,
        productName: item.produit,
        type: 'calculation_error',
        alertType: 'Incohérence',
        severity: 'Moyenne',
        message: `Bénéfice recalculé: ${expectedBenefice.toFixed(2)} BIF`,
        suggestedAction: 'Bénéfice corrigé automatiquement'
      });
    }

    return { correctedItem, alerts };
  }

  // Génération d'alertes de stock
  generateStockAlerts(item: DailyStockItem): StockAlert[] {
    const alerts: StockAlert[] = [];

    // Alerte rupture de stock
    if (item.stockRestant === 0 && item.consommation > 0) {
      alerts.push({
        id: `alert-${item.id}-rupture`,
        productId: item.id,
        productName: item.produit,
        type: 'stock_out',
        alertType: 'Rupture',
        severity: 'Critique',
        message: `Rupture de stock détectée`,
        suggestedAction: 'Réapprovisionnement urgent nécessaire'
      });
    }

    // Alerte stock faible
    const seuilStockFaible = (item.stockInitial * this.thresholds.stockFaibleSeuil) / 100;
    if (item.stockRestant > 0 && item.stockRestant <= seuilStockFaible) {
      alerts.push({
        id: `alert-${item.id}-stock-faible`,
        productId: item.id,
        productName: item.produit,
        type: 'stock_low',
        alertType: 'Stock faible',
        severity: 'Élevée',
        message: `Stock faible: ${item.stockRestant} unités restantes`,
        suggestedAction: `Prévoir réapprovisionnement (seuil: ${seuilStockFaible})`
      });
    }

    // Alerte perte élevée
    if (item.perte > 0) {
      const tauxPerte = (item.perte / item.stockTotal) * 100;
      if (tauxPerte > this.thresholds.perteMaximale) {
        alerts.push({
          id: `alert-${item.id}-perte-elevee`,
          productId: item.id,
          productName: item.produit,
          type: 'high_loss',
          alertType: 'Perte élevée',
          severity: 'Élevée',
          message: `Taux de perte élevé: ${tauxPerte.toFixed(1)}%`,
          suggestedAction: 'Vérifier les conditions de stockage et manipulation'
        });
      }
    }

    return alerts;
  }

  // Calcul des totaux par catégorie
  calculateCategoryTotals(items: DailyStockItem[], category: string) {
    const categoryItems = items.filter(item => item.category === category);
    
    return {
      recette: categoryItems.reduce((sum, item) => sum + this.calculateChiffredAffaires(item.stockVendu, item.prixVenteUnitaire), 0),
      benefice: categoryItems.reduce((sum, item) => sum + item.benefice, 0),
      stockVenduTotal: categoryItems.reduce((sum, item) => sum + item.stockVendu, 0),
      perteTotal: categoryItems.reduce((sum, item) => sum + item.perte, 0)
    };
  }
}