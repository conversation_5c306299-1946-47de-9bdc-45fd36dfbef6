import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContextBackend';
import { Navigate, useLocation } from 'react-router-dom';
import Sidebar from './Sidebar';
import { Button } from '@/components/ui/button';
import { Menu } from 'lucide-react';

interface DashboardLayoutProps {
  children: React.ReactNode;
}

const DashboardLayout: React.FC<DashboardLayoutProps> = ({ children }) => {
  const { isAuthenticated, isLoading } = useAuth();
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const location = useLocation();

  // Sauvegarder la dernière page visitée
  useEffect(() => {
    if (isAuthenticated) {
      localStorage.setItem('lastVisitedPage', location.pathname);
      localStorage.setItem('lastActivity', Date.now().toString());
    }
  }, [location.pathname, isAuthenticated]);

  // Afficher un écran de chargement pendant l'initialisation
  if (isLoading) {
    return (
      <div className="h-screen flex items-center justify-center bg-background">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Chargement...</p>
        </div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return <Navigate to="/login" state={{ from: location }} replace />;
  }

  return (
    <div className="h-screen flex bg-background">
      {/* Sidebar */}
      <Sidebar 
        collapsed={sidebarCollapsed} 
        onCollapse={setSidebarCollapsed} 
      />
      
      {/* Main Content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Top Bar */}
        <header className="h-16 bg-card border-b border-border flex items-center px-4 gap-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setSidebarCollapsed(!sidebarCollapsed)}
            className="w-9 h-9 p-0"
          >
            <Menu className="w-5 h-5" />
          </Button>
          
          <div className="flex-1">
            <h1 className="text-xl font-semibold text-foreground">
              Gestion de Stock Bar-Restaurant
            </h1>
          </div>
        </header>
        
        {/* Page Content */}
        <main className="flex-1 overflow-auto bg-muted/20 p-6">
          {children}
        </main>
      </div>
    </div>
  );
};

export default DashboardLayout;