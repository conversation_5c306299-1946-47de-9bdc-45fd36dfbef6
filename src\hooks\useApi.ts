import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import {
  productsAPI,
  salesAPI,
  inventoryAPI,
  suppliersAPI,
  expensesAPI,
  reportsAPI,
  usersAPI,
  authAPI,
  settingsAPI,
  tablesAPI,
  invoicesAPI
} from '@/lib/api';
import { useToast } from '@/hooks/use-toast';

// Hook pour les produits
export const useProducts = (params?: any) => {
  return useQuery({
    queryKey: ['products', params],
    queryFn: async () => {
      try {
        return await productsAPI.getAll(params);
      } catch (error) {
        console.error('API Error in useProducts:', error);
        throw error; // Re-throw the error to be handled by React Query
      }
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

export const useProduct = (id: string) => {
  return useQuery({
    queryKey: ['products', id],
    queryFn: () => productsAPI.getById(id),
    enabled: !!id,
  });
};

export const useCreateProduct = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: productsAPI.create,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['products'] });
      toast({
        title: "Succès",
        description: "Produit créé avec succès",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Erreur",
        description: error.response?.data?.detail || "Erreur lors de la création du produit",
        variant: "destructive",
      });
    },
  });
};

export const useUpdateProduct = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: any }) => productsAPI.update(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['products'] });
      toast({
        title: "Succès",
        description: "Produit mis à jour avec succès",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Erreur",
        description: error.response?.data?.detail || "Erreur lors de la mise à jour du produit",
        variant: "destructive",
      });
    },
  });
};

export const useDeleteProduct = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: productsAPI.delete,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['products'] });
      toast({
        title: "Succès",
        description: "Produit supprimé avec succès",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Erreur",
        description: error.response?.data?.detail || "Erreur lors de la suppression du produit",
        variant: "destructive",
      });
    },
  });
};

// Hook pour les catégories de produits
export const useProductCategories = () => {
  return useQuery({
    queryKey: ['product-categories'],
    queryFn: productsAPI.getCategories,
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
};

// Hook pour les ventes
export const useSales = (params?: any) => {
  return useQuery({
    queryKey: ['sales', params],
    queryFn: async () => {
      try {
        const data = await salesAPI.getAll(params);
        console.log('Sales API response:', data);
        return data;
      } catch (error) {
        console.error('Sales API Error:', error);
        // En cas d'erreur API, retourner un tableau vide au lieu de données mock
        return [];
      }
    },
    staleTime: 1 * 60 * 1000, // 1 minute
  });
};

export const useSale = (id: string) => {
  return useQuery({
    queryKey: ['sales', id],
    queryFn: () => salesAPI.getById(id),
    enabled: !!id,
  });
};

export const useCreateSale = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: salesAPI.create,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['sales'] });
      queryClient.invalidateQueries({ queryKey: ['daily-summary'] });
      queryClient.invalidateQueries({ queryKey: ['inventory'] });
      toast({
        title: "Succès",
        description: "Vente enregistrée avec succès",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Erreur",
        description: error.response?.data?.detail || "Erreur lors de l'enregistrement de la vente",
        variant: "destructive",
      });
    },
  });
};

export const useDeleteSale = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: salesAPI.delete,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['sales'] });
      queryClient.invalidateQueries({ queryKey: ['daily-summary'] });
      queryClient.invalidateQueries({ queryKey: ['inventory'] });
      toast({
        title: "Succès",
        description: "Vente supprimée avec succès",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Erreur",
        description: error.response?.data?.detail || "Erreur lors de la suppression de la vente",
        variant: "destructive",
      });
    },
  });
};

export const useDailySummary = (date?: string) => {
  return useQuery({
    queryKey: ['daily-summary', date],
    queryFn: () => salesAPI.getDailySummary(date),
    staleTime: 30 * 1000, // 30 secondes
    retry: false, // Ne pas réessayer si l'endpoint n'existe pas
    enabled: false, // Désactiver temporairement
  });
};

// Hook pour l'inventaire
export const useInventory = (params?: any) => {
  return useQuery({
    queryKey: ['inventory', params],
    queryFn: async () => {
      try {
        return await inventoryAPI.getAll(params);
      } catch (error) {
        console.error('Inventory API Error:', error);
        throw error;
      }
    },
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
};

export const useStockMovements = (params?: any) => {
  return useQuery({
    queryKey: ['stock-movements', params],
    queryFn: () => inventoryAPI.getStockMovements(params),
    staleTime: 1 * 60 * 1000, // 1 minute
  });
};

export const useCreateStockMovement = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: inventoryAPI.createStockMovement,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['inventory'] });
      queryClient.invalidateQueries({ queryKey: ['stock-movements'] });
      queryClient.invalidateQueries({ queryKey: ['products'] });
      toast({
        title: "Succès",
        description: "Mouvement de stock enregistré avec succès",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Erreur",
        description: error.response?.data?.detail || "Erreur lors de l'enregistrement du mouvement",
        variant: "destructive",
      });
    },
  });
};

export const useLowStockAlerts = () => {
  return useQuery({
    queryKey: ['low-stock-alerts'],
    queryFn: inventoryAPI.getLowStockAlerts,
    staleTime: 1 * 60 * 1000, // 1 minute
    refetchInterval: 5 * 60 * 1000, // Refetch toutes les 5 minutes
    retry: 2, // Réessayer 2 fois en cas d'erreur
    enabled: true, // Activer maintenant que l'endpoint est correct
  });
};

// Hook pour les tables
export const useTables = (params?: any) => {
  return useQuery({
    queryKey: ['tables', params],
    queryFn: () => tablesAPI.getAll(params),
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
};

// Hook pour les fournisseurs
export const useSuppliers = (params?: any) => {
  return useQuery({
    queryKey: ['suppliers', params],
    queryFn: async () => {
      try {
        return await suppliersAPI.getAll(params);
      } catch (error) {
        console.error('Suppliers API Error:', error);
        throw error;
      }
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

export const useCreateSupplier = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: suppliersAPI.create,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['suppliers'] });
      toast({
        title: "Succès",
        description: "Fournisseur créé avec succès",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Erreur",
        description: error.response?.data?.detail || "Erreur lors de la création du fournisseur",
        variant: "destructive",
      });
    },
  });
};

export const useUpdateSupplier = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: any }) => suppliersAPI.update(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['suppliers'] });
      toast({
        title: "Succès",
        description: "Fournisseur modifié avec succès",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Erreur",
        description: error.response?.data?.detail || "Erreur lors de la modification du fournisseur",
        variant: "destructive",
      });
    },
  });
};

export const useDeleteSupplier = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: suppliersAPI.delete,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['suppliers'] });
      toast({
        title: "Succès",
        description: "Fournisseur supprimé avec succès",
        variant: "destructive",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Erreur",
        description: error.response?.data?.detail || "Erreur lors de la suppression du fournisseur",
        variant: "destructive",
      });
    },
  });
};

// Hook pour les dépenses
export const useExpenses = (params?: any) => {
  return useQuery({
    queryKey: ['expenses', params],
    queryFn: async () => {
      try {
        return await expensesAPI.getAll(params);
      } catch (error) {
        console.error('Expenses API Error, using mock data:', error);
        // Mock data en cas d'erreur API
        return [
          {
            id: '1',
            title: 'Achat de matières premières',
            amount: 50000,
            description: 'Achat de boissons pour le stock',
            date: new Date().toISOString().split('T')[0],
            category: 'Approvisionnement',
            created_by: 'Admin',
            created_at: new Date().toISOString()
          },
          {
            id: '2',
            title: 'Facture électricité',
            amount: 25000,
            description: 'Facture mensuelle d\'électricité',
            date: new Date().toISOString().split('T')[0],
            category: 'Utilities',
            created_by: 'Admin',
            created_at: new Date().toISOString()
          },
          {
            id: '3',
            title: 'Salaires personnel',
            amount: 150000,
            description: 'Salaires du mois',
            date: new Date().toISOString().split('T')[0],
            category: 'Personnel',
            created_by: 'Admin',
            created_at: new Date().toISOString()
          }
        ];
      }
    },
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
};

export const useCreateExpense = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: expensesAPI.create,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['expenses'] });
      toast({
        title: "Succès",
        description: "Dépense enregistrée avec succès",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Erreur",
        description: error.response?.data?.detail || "Erreur lors de l'enregistrement de la dépense",
        variant: "destructive",
      });
    },
  });
};

export const useUpdateExpense = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: any }) => expensesAPI.update(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['expenses'] });
      toast({
        title: "Succès",
        description: "Dépense modifiée avec succès",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Erreur",
        description: error.response?.data?.detail || "Erreur lors de la modification de la dépense",
        variant: "destructive",
      });
    },
  });
};

export const useDeleteExpense = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: expensesAPI.delete,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['expenses'] });
      toast({
        title: "Succès",
        description: "Dépense supprimée avec succès",
        variant: "destructive",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Erreur",
        description: error.response?.data?.detail || "Erreur lors de la suppression de la dépense",
        variant: "destructive",
      });
    },
  });
};

export const useExpenseCategories = () => {
  return useQuery({
    queryKey: ['expense-categories'],
    queryFn: async () => {
      try {
        return await expensesAPI.getCategories();
      } catch (error) {
        console.error('Expense Categories API Error, using mock data:', error);
        // Mock data en cas d'erreur API
        return [
          { id: '1', name: 'Approvisionnement' },
          { id: '2', name: 'Utilities' },
          { id: '3', name: 'Personnel' },
          { id: '4', name: 'Marketing' },
          { id: '5', name: 'Maintenance' },
          { id: '6', name: 'Transport' },
          { id: '7', name: 'Autres' }
        ];
      }
    },
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
};

// Hook pour les rapports journaliers
export const useDailyReports = (params?: any) => {
  return useQuery({
    queryKey: ['daily-reports', params],
    queryFn: () => reportsAPI.getDailyReports(params),
    staleTime: 1 * 60 * 1000, // 1 minute
  });
};

export const useCreateDailyReport = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: reportsAPI.createDailyReport,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['daily-reports'] });
      toast({
        title: "Succès",
        description: "Rapport journalier créé avec succès",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Erreur",
        description: error.response?.data?.detail || "Erreur lors de la création du rapport",
        variant: "destructive",
      });
    },
  });
};

export const useUpdateDailyReport = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: any }) => reportsAPI.updateDailyReport(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['daily-reports'] });
      toast({
        title: "Succès",
        description: "Rapport journalier mis à jour avec succès",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Erreur",
        description: error.response?.data?.detail || "Erreur lors de la mise à jour du rapport",
        variant: "destructive",
      });
    },
  });
};

// Hook pour les utilisateurs (admin seulement)
export const useUsers = (params?: any) => {
  return useQuery({
    queryKey: ['users', params],
    queryFn: () => usersAPI.getAll(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

export const useCreateUser = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: usersAPI.create,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['users'] });
      toast({
        title: "Succès",
        description: "Utilisateur créé avec succès",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Erreur",
        description: error.response?.data?.detail || "Erreur lors de la création de l'utilisateur",
        variant: "destructive",
      });
    },
  });
};

export const useUpdateUser = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: any }) => usersAPI.update(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['users'] });
      toast({
        title: "Succès",
        description: "Utilisateur mis à jour avec succès",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Erreur",
        description: error.response?.data?.detail || "Erreur lors de la mise à jour de l'utilisateur",
        variant: "destructive",
      });
    },
  });
};

export const useDeleteUser = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: usersAPI.delete,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['users'] });
      toast({
        title: "Succès",
        description: "Utilisateur supprimé avec succès",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Erreur",
        description: error.response?.data?.detail || "Erreur lors de la suppression de l'utilisateur",
        variant: "destructive",
      });
    },
  });
};

// Hooks pour le profil utilisateur
export const useUserProfile = () => {
  return useQuery({
    queryKey: ['user-profile'],
    queryFn: () => authAPI.getProfile(),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

export const useUpdateProfile = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: authAPI.updateProfile,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['user-profile'] });
      toast({
        title: "Succès",
        description: "Profil mis à jour avec succès",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Erreur",
        description: error.response?.data?.detail || "Erreur lors de la mise à jour du profil",
        variant: "destructive",
      });
    },
  });
};

export const useChangePassword = () => {
  const { toast } = useToast();

  return useMutation({
    mutationFn: authAPI.changePassword,
    onSuccess: () => {
      toast({
        title: "Succès",
        description: "Mot de passe modifié avec succès",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Erreur",
        description: error.response?.data?.detail || "Erreur lors du changement de mot de passe",
        variant: "destructive",
      });
    },
  });
};

// Hooks pour les paramètres système
export const useSettings = () => {
  return useQuery({
    queryKey: ['system-settings'],
    queryFn: () => settingsAPI.getAll(),
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
};

export const useUpdateSettings = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: settingsAPI.update,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['system-settings'] });
      toast({
        title: "Succès",
        description: "Paramètres mis à jour avec succès",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Erreur",
        description: error.response?.data?.detail || "Erreur lors de la mise à jour des paramètres",
        variant: "destructive",
      });
    },
  });
};

export const useSystemInfo = () => {
  return useQuery({
    queryKey: ['system-info'],
    queryFn: () => settingsAPI.getSystemInfo(),
    staleTime: 30 * 60 * 1000, // 30 minutes
  });
};

// Hooks pour les tables (CRUD)
export const useCreateTable = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: any) => tablesAPI.create(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['tables'] });
    },
  });
};

export const useUpdateTable = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: any }) => tablesAPI.update(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['tables'] });
    },
  });
};

export const useDeleteTable = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => tablesAPI.delete(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['tables'] });
    },
  });
};

// Hook pour les achats/approvisionnements
export const usePurchases = (params?: any) => {
  return useQuery({
    queryKey: ['purchases', params],
    queryFn: async () => {
      try {
        return await inventoryAPI.getPurchases(params);
      } catch (error) {
        console.error('API Error in usePurchases:', error);
        throw error;
      }
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

export const usePurchase = (id: string) => {
  return useQuery({
    queryKey: ['purchases', id],
    queryFn: () => inventoryAPI.getPurchaseById(id),
    enabled: !!id,
  });
};

export const useCreatePurchase = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: inventoryAPI.createPurchase,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['purchases'] });
      toast({
        title: "Succès",
        description: "Achat créé avec succès",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Erreur",
        description: error.response?.data?.detail || "Erreur lors de la création de l'achat",
        variant: "destructive",
      });
    },
  });
};

export const useUpdatePurchase = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: any }) => inventoryAPI.updatePurchase(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['purchases'] });
      toast({
        title: "Succès",
        description: "Achat mis à jour avec succès",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Erreur",
        description: error.response?.data?.detail || "Erreur lors de la mise à jour de l'achat",
        variant: "destructive",
      });
    },
  });
};

export const useDeletePurchase = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: inventoryAPI.deletePurchase,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['purchases'] });
      toast({
        title: "Succès",
        description: "Achat supprimé avec succès",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Erreur",
        description: error.response?.data?.detail || "Erreur lors de la suppression de l'achat",
        variant: "destructive",
      });
    },
  });
};

// Hooks pour les factures
export const useInvoices = (params?: any) => {
  return useQuery({
    queryKey: ['invoices', params],
    queryFn: async () => {
      try {
        return await invoicesAPI.getAll(params);
      } catch (error) {
        console.error('API Error in useInvoices:', error);
        throw error;
      }
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

export const useInvoice = (id: string) => {
  return useQuery({
    queryKey: ['invoices', id],
    queryFn: () => invoicesAPI.getById(id),
    enabled: !!id,
  });
};

export const useCreateInvoice = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: invoicesAPI.create,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['invoices'] });
      toast({
        title: "Succès",
        description: "Facture créée avec succès",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Erreur",
        description: error.response?.data?.detail || "Erreur lors de la création de la facture",
        variant: "destructive",
      });
    },
  });
};

export const useUpdateInvoice = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: any }) => invoicesAPI.update(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['invoices'] });
      toast({
        title: "Succès",
        description: "Facture mise à jour avec succès",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Erreur",
        description: error.response?.data?.detail || "Erreur lors de la mise à jour de la facture",
        variant: "destructive",
      });
    },
  });
};

export const useDeleteInvoice = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: invoicesAPI.delete,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['invoices'] });
      toast({
        title: "Succès",
        description: "Facture supprimée avec succès",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Erreur",
        description: error.response?.data?.detail || "Erreur lors de la suppression de la facture",
        variant: "destructive",
      });
    },
  });
};

export const useGenerateInvoiceFromSale = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: invoicesAPI.generateFromSale,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['invoices'] });
      toast({
        title: "Succès",
        description: "Facture générée avec succès",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Erreur",
        description: error.response?.data?.detail || "Erreur lors de la génération de la facture",
        variant: "destructive",
      });
    },
  });
};

// Hooks pour les analytics et rapports
export const useSalesAnalytics = (params?: any) => {
  return useQuery({
    queryKey: ['sales-analytics', params],
    queryFn: async () => {
      // Endpoint temporairement désactivé - retourner des données vides
      return { daily_analytics: [] };
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    enabled: false, // Désactiver temporairement
  });
};

export const useProductAnalytics = (params?: any) => {
  return useQuery({
    queryKey: ['product-analytics', params],
    queryFn: async () => {
      // Endpoint temporairement désactivé - retourner des données vides
      return { product_analytics: [] };
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    enabled: false, // Désactiver temporairement
  });
};

export const useCategoryAnalytics = (params?: any) => {
  return useQuery({
    queryKey: ['category-analytics', params],
    queryFn: async () => {
      // Endpoint temporairement désactivé - retourner des données vides
      return { category_analytics: [] };
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    enabled: false, // Désactiver temporairement
  });
};

export const useDashboardStats = (params?: any) => {
  return useQuery({
    queryKey: ['dashboard-stats', params],
    queryFn: async () => {
      try {
        return await reportsAPI.getDashboardStats(params);
      } catch (error) {
        console.error('Dashboard Stats API Error:', error);
        // Retourner des données par défaut en cas d'erreur
        return {
          today: { date: new Date().toISOString().split('T')[0], sales: 0, revenue: 0, pending_sales: 0 },
          alerts: { total_unresolved: 0, out_of_stock: 0, low_stock: 0 },
          quick_stats: { active_products: 0, total_categories: 0 }
        };
      }
    },
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
};

export const useStockAlerts = () => {
  return useQuery({
    queryKey: ['stock-alerts'],
    queryFn: async () => {
      try {
        return await reportsAPI.getStockAlerts();
      } catch (error) {
        console.error('Stock Alerts API Error:', error);
        throw error;
      }
    },
    staleTime: 1 * 60 * 1000, // 1 minute
  });
};
