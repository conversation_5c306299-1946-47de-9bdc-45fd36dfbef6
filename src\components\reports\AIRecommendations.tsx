import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { 
  Brain, 
  TrendingUp, 
  ShoppingCart, 
  AlertTriangle, 
  Target, 
  Lightbulb,
  Clock,
  DollarSign,
  Package,
  Zap
} from 'lucide-react';
import { DailyStockItem } from '@/types/dailyReport';
import { aiPredictor, RecommendationAction, PredictionData, SalesPredicition } from '@/lib/aiPredictions';
import { formatCurrency } from '@/lib/currency';

interface AIRecommendationsProps {
  reportData: DailyStockItem[];
  onActionTaken?: (action: RecommendationAction) => void;
}

const AIRecommendations: React.FC<AIRecommendationsProps> = ({ reportData, onActionTaken }) => {
  const [recommendations, setRecommendations] = useState<RecommendationAction[]>([]);
  const [predictions, setPredictions] = useState<PredictionData[]>([]);
  const [salesPredictions, setSalesPredictions] = useState<SalesPredicition[]>([]);
  const [marketTrends, setMarketTrends] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    if (reportData.length > 0) {
      generateInsights();
    }
  }, [reportData]);

  const generateInsights = async () => {
    setIsLoading(true);
    
    try {
      // Générer les recommandations
      const recs = aiPredictor.generateRecommendations(reportData);
      setRecommendations(recs);

      // Générer les prédictions de stock
      const stockPreds = reportData.map(item => aiPredictor.predictStockOut(item));
      setPredictions(stockPreds);

      // Générer les prédictions de ventes
      const salesPreds = reportData.map(item => aiPredictor.predictSales(item, 7));
      setSalesPredictions(salesPreds);

      // Analyser les tendances du marché
      const trends = aiPredictor.analyzeMarketTrends(reportData);
      setMarketTrends(trends);

    } catch (error) {
      console.error('Erreur lors de la génération des insights:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'critical': return 'bg-red-100 text-red-800 border-red-200';
      case 'high': return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'medium': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'low': return 'bg-blue-100 text-blue-800 border-blue-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getPriorityIcon = (priority: string) => {
    switch (priority) {
      case 'critical': return <AlertTriangle className="w-4 h-4" />;
      case 'high': return <Zap className="w-4 h-4" />;
      case 'medium': return <Target className="w-4 h-4" />;
      case 'low': return <Lightbulb className="w-4 h-4" />;
      default: return <Brain className="w-4 h-4" />;
    }
  };

  const getActionIcon = (type: string) => {
    switch (type) {
      case 'order': return <ShoppingCart className="w-4 h-4" />;
      case 'promotion': return <TrendingUp className="w-4 h-4" />;
      case 'price_adjustment': return <DollarSign className="w-4 h-4" />;
      case 'highlight': return <Target className="w-4 h-4" />;
      default: return <Package className="w-4 h-4" />;
    }
  };

  const handleActionClick = (action: RecommendationAction) => {
    if (onActionTaken) {
      onActionTaken(action);
    }
  };

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Brain className="w-5 h-5 animate-pulse" />
            Génération des insights IA...
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Brain className="w-5 h-5" />
            Insights & Recommandations IA
          </CardTitle>
          <CardDescription>
            Analyse intelligente de vos données avec recommandations personnalisées
          </CardDescription>
        </CardHeader>
      </Card>

      <Tabs defaultValue="recommendations" className="space-y-4">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="recommendations">Recommandations</TabsTrigger>
          <TabsTrigger value="predictions">Prédictions</TabsTrigger>
          <TabsTrigger value="sales">Ventes Futures</TabsTrigger>
          <TabsTrigger value="trends">Tendances</TabsTrigger>
        </TabsList>

        <TabsContent value="recommendations" className="space-y-4">
          {recommendations.length === 0 ? (
            <Card>
              <CardContent className="pt-6">
                <div className="text-center text-muted-foreground">
                  <Lightbulb className="w-12 h-12 mx-auto mb-4 opacity-50" />
                  <p>Aucune recommandation spécifique pour le moment.</p>
                  <p className="text-sm">Vos données semblent optimales !</p>
                </div>
              </CardContent>
            </Card>
          ) : (
            <div className="space-y-4">
              {recommendations.map((rec, index) => (
                <Card key={index} className="border-l-4" style={{
                  borderLeftColor: rec.priority === 'critical' ? '#ef4444' : 
                                  rec.priority === 'high' ? '#f97316' : 
                                  rec.priority === 'medium' ? '#eab308' : '#3b82f6'
                }}>
                  <CardHeader className="pb-3">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        {getActionIcon(rec.type)}
                        <div>
                          <CardTitle className="text-base">{rec.title}</CardTitle>
                          <CardDescription className="text-sm">
                            {rec.productName}
                          </CardDescription>
                        </div>
                      </div>
                      <Badge className={getPriorityColor(rec.priority)}>
                        {getPriorityIcon(rec.priority)}
                        <span className="ml-1 capitalize">{rec.priority}</span>
                      </Badge>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <p className="text-sm text-muted-foreground mb-3">
                      {rec.description}
                    </p>
                    <div className="bg-muted/50 rounded-lg p-3 mb-3">
                      <p className="text-sm font-medium text-green-700">
                        💡 Impact attendu: {rec.expectedImpact}
                      </p>
                    </div>
                    <div className="flex gap-2">
                      <Button 
                        size="sm" 
                        onClick={() => handleActionClick(rec)}
                        variant={rec.priority === 'critical' ? 'default' : 'outline'}
                      >
                        Appliquer
                      </Button>
                      <Button size="sm" variant="ghost">
                        Plus tard
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </TabsContent>

        <TabsContent value="predictions" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            {predictions.map((pred, index) => (
              <Card key={index}>
                <CardHeader className="pb-3">
                  <CardTitle className="text-base flex items-center justify-between">
                    <span>{pred.productName}</span>
                    <Badge variant="outline">
                      {pred.confidence.toFixed(0)}% confiance
                    </Badge>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-muted-foreground">Stock actuel:</span>
                      <span className="font-medium">{pred.currentStock} unités</span>
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-muted-foreground">Consommation/jour:</span>
                      <span className="font-medium">{pred.averageDailyConsumption.toFixed(1)} unités</span>
                    </div>
                    
                    {pred.daysUntilStockOut > 0 ? (
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-muted-foreground">Rupture dans:</span>
                        <span className={`font-medium ${
                          pred.daysUntilStockOut <= 3 ? 'text-red-600' : 
                          pred.daysUntilStockOut <= 7 ? 'text-orange-600' : 'text-green-600'
                        }`}>
                          <Clock className="w-4 h-4 inline mr-1" />
                          {pred.daysUntilStockOut} jour(s)
                        </span>
                      </div>
                    ) : (
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-red-600 font-medium">
                          ⚠️ Stock épuisé
                        </span>
                      </div>
                    )}
                    
                    <div className="border-t pt-3">
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-muted-foreground">Commande suggérée:</span>
                        <span className="font-medium text-blue-600">
                          {pred.recommendedOrderQuantity} unités
                        </span>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="sales" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            {salesPredictions.map((pred, index) => (
              <Card key={index}>
                <CardHeader className="pb-3">
                  <CardTitle className="text-base flex items-center justify-between">
                    <span>{pred.productName}</span>
                    <Badge variant="outline">
                      7 jours
                    </Badge>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="text-center">
                      <div className="text-2xl font-bold text-primary">
                        {pred.predictedSales}
                      </div>
                      <div className="text-sm text-muted-foreground">unités prédites</div>
                    </div>
                    
                    <div className="text-center">
                      <div className="text-xl font-semibold text-green-600">
                        {formatCurrency(pred.predictedRevenue)}
                      </div>
                      <div className="text-sm text-muted-foreground">recette prédite</div>
                    </div>
                    
                    <div className="border-t pt-3">
                      <div className="text-sm text-muted-foreground mb-2">
                        Facteurs influençant:
                      </div>
                      <div className="space-y-1">
                        {pred.factors.map((factor, i) => (
                          <div key={i} className="text-xs bg-muted rounded px-2 py-1">
                            {factor}
                          </div>
                        ))}
                      </div>
                    </div>
                    
                    <div className="text-center">
                      <Badge variant="secondary">
                        {pred.confidence.toFixed(0)}% confiance
                      </Badge>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="trends" className="space-y-4">
          {marketTrends && (
            <div className="space-y-4">
              {/* Produits en croissance */}
              {marketTrends.growingProducts.length > 0 && (
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2 text-green-700">
                      <TrendingUp className="w-5 h-5" />
                      Produits en Croissance
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="flex flex-wrap gap-2">
                      {marketTrends.growingProducts.map((product: string) => (
                        <Badge key={product} className="bg-green-100 text-green-800">
                          {product}
                        </Badge>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Produits en déclin */}
              {marketTrends.decliningProducts.length > 0 && (
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2 text-red-700">
                      <TrendingUp className="w-5 h-5 rotate-180" />
                      Produits en Déclin
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="flex flex-wrap gap-2">
                      {marketTrends.decliningProducts.map((product: string) => (
                        <Badge key={product} className="bg-red-100 text-red-800">
                          {product}
                        </Badge>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Insights */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Lightbulb className="w-5 h-5" />
                    Insights Stratégiques
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {marketTrends.insights.map((insight: string, index: number) => (
                      <div key={index} className="border-l-4 border-blue-500 pl-4">
                        <p className="text-sm">{insight}</p>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default AIRecommendations;
