import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  RefreshCw, 
  Database, 
  Package, 
  CheckCircle, 
  AlertTriangle, 
  Loader2,
  ArrowRightLeft,
  ArrowRight,
  ArrowLeft,
  Settings
} from 'lucide-react';
import { useStockSync } from '@/hooks/useStockSync';
import { StockSyncService } from '@/lib/syncService';

interface ConsistencyData {
  consistent: boolean;
  issues: string[];
  summary: {
    productsCount: number;
    inventoryCount: number;
    missingInInventory: number;
    missingInProducts: number;
  };
}

const StockSyncManager: React.FC = () => {
  const {
    syncState,
    isLoading,
    syncToInventory,
    syncToProducts,
    fullSync,
    checkConsistency
  } = useStockSync();

  const [consistencyData, setConsistencyData] = useState<ConsistencyData | null>(null);
  const [checkingConsistency, setCheckingConsistency] = useState(false);

  // Vérifier la cohérence au chargement
  useEffect(() => {
    handleCheckConsistency();
  }, []);

  const handleCheckConsistency = async () => {
    setCheckingConsistency(true);
    try {
      const result = await checkConsistency();
      setConsistencyData(result);
    } catch (error) {
      console.error('Erreur lors de la vérification:', error);
    } finally {
      setCheckingConsistency(false);
    }
  };

  const formatLastSync = (date: Date | null) => {
    if (!date) return 'Jamais';
    return new Intl.DateTimeFormat('fr-FR', {
      dateStyle: 'short',
      timeStyle: 'short'
    }).format(date);
  };

  return (
    <div className="space-y-6">
      {/* En-tête */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Synchronisation des Stocks</h2>
          <p className="text-muted-foreground">
            Gérer la cohérence entre Products et Inventory
          </p>
        </div>
        <Button 
          variant="outline" 
          onClick={handleCheckConsistency}
          disabled={checkingConsistency}
        >
          {checkingConsistency ? (
            <Loader2 className="w-4 h-4 mr-2 animate-spin" />
          ) : (
            <RefreshCw className="w-4 h-4 mr-2" />
          )}
          Vérifier
        </Button>
      </div>

      {/* État de la synchronisation */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="w-5 h-5" />
            État de la Synchronisation
          </CardTitle>
          <CardDescription>
            Dernière synchronisation et statut actuel
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-3">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-blue-100 rounded-lg">
                <Database className="w-4 h-4 text-blue-600" />
              </div>
              <div>
                <p className="text-sm font-medium">Dernière sync</p>
                <p className="text-xs text-muted-foreground">
                  {formatLastSync(syncState.lastSync)}
                </p>
              </div>
            </div>
            
            <div className="flex items-center gap-3">
              <div className="p-2 bg-green-100 rounded-lg">
                <CheckCircle className="w-4 h-4 text-green-600" />
              </div>
              <div>
                <p className="text-sm font-medium">Statut</p>
                <Badge variant={syncState.error ? "destructive" : "default"}>
                  {syncState.error ? "Erreur" : "OK"}
                </Badge>
              </div>
            </div>
            
            <div className="flex items-center gap-3">
              <div className="p-2 bg-purple-100 rounded-lg">
                <RefreshCw className="w-4 h-4 text-purple-600" />
              </div>
              <div>
                <p className="text-sm font-medium">En cours</p>
                <Badge variant={isLoading ? "secondary" : "outline"}>
                  {isLoading ? "Synchronisation..." : "Inactif"}
                </Badge>
              </div>
            </div>
          </div>

          {syncState.error && (
            <Alert className="mt-4" variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>{syncState.error}</AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>

      {/* Cohérence des données */}
      {consistencyData && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CheckCircle className={`w-5 h-5 ${consistencyData.consistent ? 'text-green-500' : 'text-red-500'}`} />
              Cohérence des Données
            </CardTitle>
            <CardDescription>
              Analyse de la synchronisation entre Products et Inventory
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-2">
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-sm">Produits:</span>
                  <Badge variant="outline">{consistencyData.summary.productsCount}</Badge>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm">Items Inventory:</span>
                  <Badge variant="outline">{consistencyData.summary.inventoryCount}</Badge>
                </div>
              </div>
              
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-sm">Manquants dans Inventory:</span>
                  <Badge variant={consistencyData.summary.missingInInventory > 0 ? "destructive" : "default"}>
                    {consistencyData.summary.missingInInventory}
                  </Badge>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm">Orphelins dans Inventory:</span>
                  <Badge variant={consistencyData.summary.missingInProducts > 0 ? "destructive" : "default"}>
                    {consistencyData.summary.missingInProducts}
                  </Badge>
                </div>
              </div>
            </div>

            {!consistencyData.consistent && (
              <Alert className="mt-4" variant="destructive">
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>
                  <strong>Incohérences détectées:</strong>
                  <ul className="mt-2 list-disc list-inside">
                    {consistencyData.issues.map((issue, index) => (
                      <li key={index}>{issue}</li>
                    ))}
                  </ul>
                </AlertDescription>
              </Alert>
            )}
          </CardContent>
        </Card>
      )}

      {/* Actions de synchronisation */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <ArrowRightLeft className="w-5 h-5" />
            Actions de Synchronisation
          </CardTitle>
          <CardDescription>
            Synchroniser les données entre les différentes tables
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-3">
            {/* Sync Products → Inventory */}
            <div className="space-y-3">
              <div className="flex items-center gap-2">
                <Package className="w-4 h-4 text-blue-500" />
                <ArrowRight className="w-4 h-4 text-gray-400" />
                <Database className="w-4 h-4 text-green-500" />
              </div>
              <div>
                <h4 className="font-medium">Products → Inventory</h4>
                <p className="text-xs text-muted-foreground">
                  Créer/mettre à jour l'inventaire depuis les produits
                </p>
              </div>
              <Button 
                variant="outline" 
                size="sm" 
                onClick={() => syncToInventory()}
                disabled={isLoading}
                className="w-full"
              >
                {isLoading ? (
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                ) : (
                  <ArrowRight className="w-4 h-4 mr-2" />
                )}
                Synchroniser
              </Button>
            </div>

            {/* Sync Inventory → Products */}
            <div className="space-y-3">
              <div className="flex items-center gap-2">
                <Database className="w-4 h-4 text-green-500" />
                <ArrowLeft className="w-4 h-4 text-gray-400" />
                <Package className="w-4 h-4 text-blue-500" />
              </div>
              <div>
                <h4 className="font-medium">Inventory → Products</h4>
                <p className="text-xs text-muted-foreground">
                  Mettre à jour les stocks des produits
                </p>
              </div>
              <Button 
                variant="outline" 
                size="sm" 
                onClick={() => syncToProducts()}
                disabled={isLoading}
                className="w-full"
              >
                {isLoading ? (
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                ) : (
                  <ArrowLeft className="w-4 h-4 mr-2" />
                )}
                Synchroniser
              </Button>
            </div>

            {/* Sync Bidirectionnelle */}
            <div className="space-y-3">
              <div className="flex items-center gap-2">
                <Package className="w-4 h-4 text-blue-500" />
                <ArrowRightLeft className="w-4 h-4 text-purple-500" />
                <Database className="w-4 h-4 text-green-500" />
              </div>
              <div>
                <h4 className="font-medium">Synchronisation Complète</h4>
                <p className="text-xs text-muted-foreground">
                  Synchronisation bidirectionnelle complète
                </p>
              </div>
              <Button 
                size="sm" 
                onClick={() => fullSync()}
                disabled={isLoading}
                className="w-full"
              >
                {isLoading ? (
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                ) : (
                  <ArrowRightLeft className="w-4 h-4 mr-2" />
                )}
                Sync Complète
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default StockSyncManager;
