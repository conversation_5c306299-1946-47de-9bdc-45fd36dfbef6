# Generated by Django 5.2.4 on 2025-07-30 14:12

import django.core.validators
import django.db.models.deletion
from decimal import Decimal
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Category',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True, verbose_name='Nom de la catégorie')),
                ('type', models.CharField(choices=[('boissons', 'Boissons'), ('plats', 'Plats'), ('snacks', 'Snacks')], max_length=20, verbose_name='Type de catégorie')),
                ('description', models.TextField(blank=True, null=True, verbose_name='Description')),
                ('is_active', models.BooleanField(default=True, verbose_name='Actif')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Date de création')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Date de modification')),
            ],
            options={
                'verbose_name': 'Catégorie',
                'verbose_name_plural': 'Catégories',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='Product',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200, verbose_name='Nom du produit')),
                ('code', models.CharField(blank=True, max_length=50, null=True, unique=True, verbose_name='Code produit')),
                ('description', models.TextField(blank=True, null=True, verbose_name='Description')),
                ('unit', models.CharField(choices=[('piece', 'Pièce'), ('bouteille', 'Bouteille'), ('casier', 'Casier'), ('litre', 'Litre'), ('kg', 'Kilogramme'), ('portion', 'Portion')], default='piece', max_length=20, verbose_name='Unité de mesure')),
                ('purchase_price', models.DecimalField(decimal_places=2, max_digits=10, validators=[django.core.validators.MinValueValidator(Decimal('0.00'))], verbose_name="Prix d'achat (BIF)")),
                ('selling_price', models.DecimalField(decimal_places=2, max_digits=10, validators=[django.core.validators.MinValueValidator(Decimal('0.00'))], verbose_name='Prix de vente (BIF)')),
                ('initial_stock', models.PositiveIntegerField(default=0, verbose_name='Stock initial')),
                ('current_stock', models.PositiveIntegerField(default=0, verbose_name='Stock actuel')),
                ('minimum_stock', models.PositiveIntegerField(default=5, verbose_name='Stock minimum (alerte)')),
                ('units_per_case', models.PositiveIntegerField(default=1, verbose_name='Unités par casier')),
                ('case_price', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, validators=[django.core.validators.MinValueValidator(Decimal('0.00'))], verbose_name='Prix du casier (BIF)')),
                ('is_active', models.BooleanField(default=True, verbose_name='Produit actif')),
                ('is_available', models.BooleanField(default=True, verbose_name='Disponible à la vente')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Date de création')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Date de modification')),
                ('category', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='products', to='products.category', verbose_name='Catégorie')),
            ],
            options={
                'verbose_name': 'Produit',
                'verbose_name_plural': 'Produits',
                'ordering': ['category', 'name'],
                'unique_together': {('name', 'category')},
            },
        ),
    ]
