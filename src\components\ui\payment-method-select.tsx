import React from 'react';
import CustomSelect from './custom-select';

interface PaymentMethodOption {
  value: string;
  label: string;
  icon?: string;
}

interface PaymentMethodSelectProps {
  value: string;
  onValueChange: (value: string) => void;
  disabled?: boolean;
  className?: string;
}

const PaymentMethodSelect: React.FC<PaymentMethodSelectProps> = ({
  value,
  onValueChange,
  disabled = false,
  className
}) => {
  const paymentMethods: PaymentMethodOption[] = [
    { value: 'cash', label: 'Espèces' },
    { value: 'card', label: 'Carte bancaire' },
    { value: 'mobile', label: 'Mobile Money' },
    { value: 'credit', label: 'Crédit' }
  ];

  return (
    <CustomSelect
      value={value}
      onValueChange={onValueChange}
      options={paymentMethods}
      placeholder="Sélectionner le mode de paiement"
      disabled={disabled}
      className={className}
    />
  );
};

export default PaymentMethodSelect; 