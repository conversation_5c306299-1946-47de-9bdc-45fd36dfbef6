import React, { useState, useEffect, useCallback } from 'react';
import { SettingsService, SystemSettings } from '@/lib/settingsService';
import { useToast } from '@/hooks/use-toast';

interface UseSettingsReturn {
  settings: SystemSettings | null;
  isLoading: boolean;
  isAPIAvailable: boolean;
  error: string | null;
  updateSettings: (newSettings: SystemSettings) => Promise<void>;
  resetToDefaults: () => Promise<void>;
  syncToServer: () => Promise<void>;
  refresh: () => Promise<void>;
}

/**
 * Hook pour gérer les paramètres système avec fallback localStorage
 */
export const useSettingsService = (): UseSettingsReturn => {
  const [settings, setSettings] = useState<SystemSettings | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isAPIAvailable, setIsAPIAvailable] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { toast } = useToast();

  // Charger les paramètres au démarrage
  const loadSettings = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Vérifier la disponibilité de l'API (avec fallback)
      let apiAvailable = false;
      try {
        apiAvailable = await SettingsService.checkAPIAvailability();
      } catch (error) {
        console.warn('API non disponible, utilisation du mode local');
        apiAvailable = false;
      }
      setIsAPIAvailable(apiAvailable);

      // Charger les paramètres (avec fallback localStorage)
      let loadedSettings = null;
      try {
        loadedSettings = await SettingsService.getSettings();
      } catch (error) {
        console.warn('Erreur chargement paramètres, utilisation des défauts');
        // Utiliser des paramètres par défaut
        loadedSettings = {
          restaurant: {
            name: "BarStock Wise",
            address: "Bujumbura, Burundi",
            phone: "+257 XX XX XX XX",
            email: "<EMAIL>",
            currency: "BIF",
            tax_rate: 18
          },
          notifications: {
            email_enabled: true,
            sms_enabled: false,
            low_stock_alerts: true,
            daily_reports: true
          },
          printing: {
            auto_print_receipts: true,
            receipt_copies: 1,
            printer_name: "Imprimante par défaut"
          },
          system: {
            language: "fr",
            timezone: "Africa/Bujumbura",
            date_format: "DD/MM/YYYY",
            backup_frequency: "daily"
          }
        };
      }
      setSettings(loadedSettings);

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Erreur inconnue';
      setError(errorMessage);
      console.error('Erreur chargement paramètres:', err);
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Mettre à jour les paramètres
  const updateSettings = useCallback(async (newSettings: SystemSettings) => {
    try {
      setIsLoading(true);
      await SettingsService.saveSettings(newSettings);
      setSettings(newSettings);
      
      toast({
        title: "Paramètres sauvegardés",
        description: isAPIAvailable 
          ? "Paramètres synchronisés avec le serveur"
          : "Paramètres sauvegardés localement",
      });
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Erreur de sauvegarde';
      setError(errorMessage);
      
      toast({
        title: "Erreur de sauvegarde",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  }, [isAPIAvailable, toast]);

  // Réinitialiser aux paramètres par défaut
  const resetToDefaults = useCallback(async () => {
    try {
      setIsLoading(true);
      const defaultSettings = await SettingsService.resetToDefaults();
      setSettings(defaultSettings);
      
      toast({
        title: "Paramètres réinitialisés",
        description: "Les paramètres par défaut ont été restaurés",
      });
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Erreur de réinitialisation';
      setError(errorMessage);
      
      toast({
        title: "Erreur de réinitialisation",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  }, [toast]);

  // Synchroniser avec le serveur
  const syncToServer = useCallback(async () => {
    try {
      setIsLoading(true);
      const result = await SettingsService.syncToServer();
      
      if (result.success) {
        // Vérifier à nouveau la disponibilité de l'API
        const apiAvailable = await SettingsService.checkAPIAvailability();
        setIsAPIAvailable(apiAvailable);
        
        toast({
          title: "Synchronisation réussie",
          description: result.message,
        });
      } else {
        toast({
          title: "Échec de synchronisation",
          description: result.message,
          variant: "destructive",
        });
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Erreur de synchronisation';
      toast({
        title: "Erreur de synchronisation",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  }, [toast]);

  // Actualiser les paramètres
  const refresh = useCallback(async () => {
    await loadSettings();
  }, [loadSettings]);

  // Charger les paramètres au montage du composant
  useEffect(() => {
    loadSettings();
  }, [loadSettings]);

  return {
    settings,
    isLoading,
    isAPIAvailable,
    error,
    updateSettings,
    resetToDefaults,
    syncToServer,
    refresh
  };
};

export default useSettingsService;
