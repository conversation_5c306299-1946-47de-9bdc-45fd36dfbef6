# Generated by Django 5.2.4 on 2025-07-30 14:12

import django.core.validators
import django.db.models.deletion
from decimal import Decimal
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('suppliers', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='ExpenseCategory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True, verbose_name='Nom de la catégorie')),
                ('description', models.TextField(blank=True, null=True, verbose_name='Description')),
                ('is_active', models.BooleanField(default=True, verbose_name='Catégorie active')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Date de création')),
            ],
            options={
                'verbose_name': 'Catégorie de dépense',
                'verbose_name_plural': 'Catégories de dépenses',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='Expense',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('reference', models.CharField(max_length=100, unique=True, verbose_name='Référence')),
                ('description', models.CharField(max_length=200, verbose_name='Description')),
                ('amount', models.DecimalField(decimal_places=2, max_digits=12, validators=[django.core.validators.MinValueValidator(Decimal('0.00'))], verbose_name='Montant (BIF)')),
                ('payment_method', models.CharField(choices=[('cash', 'Espèces'), ('card', 'Carte'), ('mobile', 'Mobile Money'), ('bank_transfer', 'Virement bancaire'), ('check', 'Chèque')], max_length=20, verbose_name='Mode de paiement')),
                ('receipt_number', models.CharField(blank=True, max_length=100, null=True, verbose_name='Numéro de reçu')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='Notes')),
                ('expense_date', models.DateTimeField(verbose_name='Date de dépense')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Date de création')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Date de modification')),
                ('supplier', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='suppliers.supplier', verbose_name='Fournisseur')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='Utilisateur')),
                ('category', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='expenses', to='expenses.expensecategory', verbose_name='Catégorie')),
            ],
            options={
                'verbose_name': 'Dépense',
                'verbose_name_plural': 'Dépenses',
                'ordering': ['-expense_date'],
            },
        ),
    ]
