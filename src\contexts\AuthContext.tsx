import React, { createContext, useContext, useState, useEffect } from 'react';

export type UserRole = 'Admin' | 'Gérant' | 'Serveur';

export interface User {
  id: string;
  username: string;
  role: UserRole;
  name: string;
}

interface AuthContextType {
  user: User | null;
  login: (username: string, password: string) => Promise<boolean>;
  logout: () => void;
  isAuthenticated: boolean;
  isLoading: boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Mock users pour la démo
const mockUsers: (User & { password: string })[] = [
  {
    id: '1',
    username: 'admin',
    password: 'admin123',
    role: 'Admin',
    name: 'Administrateur'
  },
  {
    id: '2',
    username: 'gerant',
    password: 'gerant123',
    role: 'Gérant',
    name: '<PERSON><PERSON>nt Principal'
  },
  {
    id: '3',
    username: 'serveur1',
    password: 'serveur123',
    role: 'Serveur',
    name: 'Serveur 1'
  }
];

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Vérifier si un utilisateur est déjà connecté
    const initializeAuth = async () => {
      try {
        const savedUser = localStorage.getItem('currentUser');
        const lastActivity = localStorage.getItem('lastActivity');

        if (savedUser && lastActivity) {
          const timeSinceLastActivity = Date.now() - parseInt(lastActivity);
          const sessionTimeout = 24 * 60 * 60 * 1000; // 24 heures

          if (timeSinceLastActivity < sessionTimeout) {
            setUser(JSON.parse(savedUser));
            // Mettre à jour la dernière activité
            localStorage.setItem('lastActivity', Date.now().toString());
          } else {
            // Session expirée
            localStorage.removeItem('currentUser');
            localStorage.removeItem('lastActivity');
          }
        }
      } catch (error) {
        console.error('Erreur lors de l\'initialisation de l\'authentification:', error);
        localStorage.removeItem('currentUser');
        localStorage.removeItem('lastActivity');
      } finally {
        setIsLoading(false);
      }
    };

    initializeAuth();
  }, []);

  const login = async (username: string, password: string): Promise<boolean> => {
    const foundUser = mockUsers.find(
      u => u.username === username && u.password === password
    );

    if (foundUser) {
      const userWithoutPassword = {
        id: foundUser.id,
        username: foundUser.username,
        role: foundUser.role,
        name: foundUser.name
      };
      setUser(userWithoutPassword);
      localStorage.setItem('currentUser', JSON.stringify(userWithoutPassword));
      localStorage.setItem('lastActivity', Date.now().toString());
      return true;
    }
    return false;
  };

  const logout = () => {
    setUser(null);
    localStorage.removeItem('currentUser');
    localStorage.removeItem('lastActivity');
  };

  return (
    <AuthContext.Provider value={{
      user,
      login,
      logout,
      isAuthenticated: !!user,
      isLoading
    }}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};