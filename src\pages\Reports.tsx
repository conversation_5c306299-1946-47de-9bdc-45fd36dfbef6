import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Download, FileText, Calendar, BarChart3, TrendingUp, DollarSign } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { formatCurrency } from '@/lib/currency';
import { usePermissions } from '@/contexts/PermissionsContext';
import { useSales, useInventory, useExpenses, useSalesAnalytics, useDashboardStats } from '@/hooks/useApi';
import { Skeleton } from '@/components/ui/skeleton';
import { parseNumericValue } from '@/lib/utils/numeric';
import {
  exportSalesReportToPDF,
  exportStockReportToPDF,
  exportExpenseReportToPDF,
  exportToExcel,
  type SalesReportData,
  type StockReportData,
  type ExpenseReportData
} from '@/lib/pdfExport';

// Fonction pour calculer les données de rapport dynamiques
const calculateReportData = (reportData: any, selectedPeriod: string) => {
  const today = new Date().toISOString().split('T')[0];
  const currentMonth = new Date().toLocaleDateString('fr-FR', { month: 'long', year: 'numeric' });

  return {
    daily: {
      date: today,
      sales: reportData?.sales?.total || 0,
      benefits: Math.round((reportData?.sales?.total || 0) * 0.44), // Estimation 44% de marge
      orders: reportData?.sales?.count || 0,
      topProducts: reportData?.sales?.topProducts || []
    },
    monthly: {
      month: currentMonth,
      sales: reportData?.sales?.total || 0,
      benefits: Math.round((reportData?.sales?.total || 0) * 0.44),
      orders: reportData?.sales?.count || 0,
      categories: reportData?.sales?.byCategory || []
    },
    stocks: {
      totalProducts: reportData?.stocks?.totalProducts || 0,
      lowStock: reportData?.stocks?.lowStock || 0,
      outOfStock: reportData?.stocks?.outOfStock || 0,
      totalValue: reportData?.stocks?.totalValue || 0
    }
  };
};

const Reports = () => {
  const [reportType, setReportType] = useState('daily');
  const [selectedPeriod, setSelectedPeriod] = useState('today');
  const [startDate, setStartDate] = useState('');
  const [endDate, setEndDate] = useState('');
  const { toast } = useToast();
  const { canExportReports, userRole } = usePermissions();

  // Hooks pour les données API d'analytics
  const { data: salesAnalytics, isLoading: salesAnalyticsLoading, error: salesAnalyticsError } = useSalesAnalytics({ period: selectedPeriod });
  const { data: dashboardStats, isLoading: dashboardStatsLoading, error: dashboardStatsError } = useDashboardStats({ period: selectedPeriod });

  // Hooks de fallback pour les données de base
  const { data: salesData, isLoading: salesLoading } = useSales();
  const { data: inventoryData, isLoading: inventoryLoading } = useInventory();
  const { data: expensesData, isLoading: expensesLoading } = useExpenses();

  // Vérifier si les APIs d'analytics sont disponibles
  const hasAnalyticsAPI = !salesAnalyticsError && !dashboardStatsError;

  // Calculer les données de rapport
  const reportData = React.useMemo(() => {
    if (hasAnalyticsAPI && salesAnalytics && dashboardStats) {
      // Utiliser les données d'analytics si disponibles
      return {
        sales: {
          total: salesAnalytics.total_revenue || 0,
          count: salesAnalytics.total_sales || 0,
          byCategory: salesAnalytics.sales_by_category || []
        },
        stocks: {
          totalProducts: dashboardStats.total_products || 0,
          lowStock: dashboardStats.low_stock_count || 0,
          outOfStock: dashboardStats.out_of_stock_count || 0,
          totalValue: dashboardStats.total_stock_value || 0
        },
        expenses: {
          total: dashboardStats.total_expenses || 0,
          count: dashboardStats.expense_count || 0
        }
      };
    }

    // Fallback: calculer à partir des données de base
    let sales = { total: 0, count: 0, byCategory: [] };
    let stocks = { totalProducts: 0, lowStock: 0, outOfStock: 0, totalValue: 0 };
    let expenses = { total: 0, count: 0 };

    // Traiter les ventes
    if (salesData) {
      const salesList = salesData?.results || (Array.isArray(salesData) ? salesData : []);
      const totalRevenue = salesList.reduce((sum: number, sale: any) => sum + parseNumericValue(sale.total_amount), 0);
      sales = {
        total: totalRevenue,
        count: salesList.length,
        byCategory: []
      };
    }

    // Traiter l'inventaire
    if (inventoryData) {
      const inventoryList = inventoryData?.results || (Array.isArray(inventoryData) ? inventoryData : []);

      const lowStock = inventoryList.filter((item: any) =>
        item.current_stock <= item.minimum_stock && item.current_stock > 0
      ).length;

      const outOfStock = inventoryList.filter((item: any) =>
        item.current_stock === 0
      ).length;

      const totalValue = inventoryList.reduce((sum: number, item: any) =>
        sum + (parseNumericValue(item.current_stock) * parseNumericValue(item.purchase_price)), 0
      );

      stocks = {
        totalProducts: inventoryList.length,
        lowStock,
        outOfStock,
        totalValue
      };
    }

    // Traiter les dépenses
    if (expensesData) {
      const expensesList = expensesData?.results || (Array.isArray(expensesData) ? expensesData : []);
      const totalExpenses = expensesList.reduce((sum: number, expense: any) => sum + parseNumericValue(expense.amount), 0);
      expenses = {
        total: totalExpenses,
        count: expensesList.length
      };
    }

    return { sales, stocks, expenses };
  }, [hasAnalyticsAPI, salesAnalytics, dashboardStats, salesData, inventoryData, expensesData]);

  // État de chargement global
  const isLoading = salesAnalyticsLoading || dashboardStatsLoading || salesLoading || inventoryLoading || expensesLoading;

  // Calculer les données de rapport dynamiques
  const dynamicReportData = calculateReportData(reportData, selectedPeriod);

  // Préparer les données pour les rapports PDF
  const prepareSalesReportData = (): SalesReportData => {
    const salesList = salesData?.results || (Array.isArray(salesData) ? salesData : []);

    // Si pas de données, créer des données de démonstration réalistes
    const salesDataForReport = salesList.length > 0 ? salesList.map((sale: any) => ({
      date: new Date(sale.created_at).toLocaleDateString('fr-FR'),
      orderId: sale.reference || `#${sale.id}`,
      items: sale.items?.length || sale.items_count || 0,
      total: parseNumericValue(sale.total_amount || sale.final_amount),
      server: sale.server_name || sale.server?.username || 'N/A',
      table: sale.table_number || sale.table?.number || 'Emporter'
    })) : [
      // Données de démonstration réalistes
      {
        date: new Date().toLocaleDateString('fr-FR'),
        orderId: '#DEMO-001',
        items: 3,
        total: 25000,
        server: 'Jean Ndayishimiye',
        table: 'Table 5'
      },
      {
        date: new Date().toLocaleDateString('fr-FR'),
        orderId: '#DEMO-002',
        items: 2,
        total: 18000,
        server: 'Marie Nzeyimana',
        table: 'Table 3'
      },
      {
        date: new Date().toLocaleDateString('fr-FR'),
        orderId: '#DEMO-003',
        items: 5,
        total: 42000,
        server: 'Paul Nkurunziza',
        table: 'Emporter'
      }
    ];

    const totalSales = salesDataForReport.reduce((sum, sale) => sum + sale.total, 0);
    const avgOrder = totalSales / Math.max(1, salesDataForReport.length);

    return {
      title: "Rapport de Ventes",
      period: selectedPeriod === 'today' ? 'Aujourd\'hui' :
              selectedPeriod === 'thisWeek' ? 'Cette semaine' :
              selectedPeriod === 'thisMonth' ? 'Ce mois' : 'Période personnalisée',
      generatedAt: new Date().toLocaleString('fr-FR'),
      generatedBy: "Système BarStock Wise",
      data: salesDataForReport,
      summary: {
        'Total des ventes': formatCurrency(totalSales),
        'Nombre de commandes': salesDataForReport.length,
        'Panier moyen': formatCurrency(avgOrder),
        'Bénéfice estimé': formatCurrency(Math.round(totalSales * 0.44))
      }
    };
  };

  const prepareStockReportData = (): StockReportData => {
    const inventoryList = inventoryData?.results || (Array.isArray(inventoryData) ? inventoryData : []);

    // Si pas de données, créer des données de démonstration réalistes
    const stockDataForReport = inventoryList.length > 0 ? inventoryList.map((item: any) => ({
      product: item.name || item.product_name,
      category: item.category_name || item.category || 'Non classé',
      currentStock: item.current_stock || item.quantity || 0,
      minThreshold: item.minimum_stock || 10,
      value: (item.current_stock || 0) * (item.unit_price || item.price || 0),
      status: item.is_out_of_stock ? 'Rupture' :
              item.is_low_stock ? 'Stock faible' : 'Normal'
    })) : [
      // Données de démonstration réalistes
      {
        product: 'Primus 72cl',
        category: 'Bières',
        currentStock: 15,
        minThreshold: 20,
        value: 45000,
        status: 'Stock faible'
      },
      {
        product: 'Coca-Cola 50cl',
        category: 'Boissons gazeuses',
        currentStock: 0,
        minThreshold: 10,
        value: 0,
        status: 'Rupture'
      },
      {
        product: 'Filet de bœuf',
        category: 'Viandes',
        currentStock: 25,
        minThreshold: 15,
        value: 375000,
        status: 'Normal'
      },
      {
        product: 'Amstel 50cl',
        category: 'Bières',
        currentStock: 45,
        minThreshold: 30,
        value: 135000,
        status: 'Normal'
      }
    ];

    const totalValue = stockDataForReport.reduce((sum, item) => sum + item.value, 0);
    const lowStockCount = stockDataForReport.filter(item => item.status === 'Stock faible').length;
    const outOfStockCount = stockDataForReport.filter(item => item.status === 'Rupture').length;

    return {
      title: "Rapport de Stock",
      period: "État actuel",
      generatedAt: new Date().toLocaleString('fr-FR'),
      generatedBy: "Système BarStock Wise",
      data: stockDataForReport,
      summary: {
        'Total produits': stockDataForReport.length,
        'Stock faible': lowStockCount,
        'Ruptures': outOfStockCount,
        'Valeur totale': formatCurrency(totalValue)
      }
    };
  };

  const prepareExpenseReportData = (): ExpenseReportData => {
    const expensesList = expensesData?.results || (Array.isArray(expensesData) ? expensesData : []);

    // Si pas de données, créer des données de démonstration réalistes
    const expenseDataForReport = expensesList.length > 0 ? expensesList.map((expense: any) => ({
      date: new Date(expense.created_at || expense.date).toLocaleDateString('fr-FR'),
      category: expense.category_name || expense.category || 'Divers',
      description: expense.description || expense.name || 'N/A',
      amount: parseNumericValue(expense.amount),
      approvedBy: expense.approved_by || expense.user?.username || 'Système'
    })) : [
      // Données de démonstration réalistes
      {
        date: new Date().toLocaleDateString('fr-FR'),
        category: 'Approvisionnement',
        description: 'Achat de bières et boissons',
        amount: 150000,
        approvedBy: 'Manager'
      },
      {
        date: new Date(Date.now() - 86400000).toLocaleDateString('fr-FR'), // Hier
        category: 'Maintenance',
        description: 'Réparation du réfrigérateur',
        amount: 75000,
        approvedBy: 'Admin'
      },
      {
        date: new Date(Date.now() - 172800000).toLocaleDateString('fr-FR'), // Avant-hier
        category: 'Personnel',
        description: 'Avance sur salaire - Jean',
        amount: 50000,
        approvedBy: 'RH'
      },
      {
        date: new Date(Date.now() - 259200000).toLocaleDateString('fr-FR'), // Il y a 3 jours
        category: 'Utilities',
        description: 'Facture d\'électricité',
        amount: 125000,
        approvedBy: 'Comptable'
      }
    ];

    const totalExpenses = expenseDataForReport.reduce((sum, expense) => sum + expense.amount, 0);
    const avgExpense = totalExpenses / Math.max(1, expenseDataForReport.length);

    return {
      title: "Rapport de Dépenses",
      period: selectedPeriod === 'today' ? 'Aujourd\'hui' :
              selectedPeriod === 'thisWeek' ? 'Cette semaine' :
              selectedPeriod === 'thisMonth' ? 'Ce mois' : 'Période personnalisée',
      generatedAt: new Date().toLocaleString('fr-FR'),
      generatedBy: "Système BarStock Wise",
      data: expenseDataForReport,
      summary: {
        'Total dépenses': formatCurrency(totalExpenses),
        'Nombre de dépenses': expenseDataForReport.length,
        'Dépense moyenne': formatCurrency(avgExpense)
      }
    };
  };

  const generateReport = () => {
    // Générer un aperçu du rapport dans une nouvelle fenêtre
    const reportData = reportType === 'sales' ? prepareSalesReportData() :
                      reportType === 'stock' ? prepareStockReportData() :
                      reportType === 'expenses' ? prepareExpenseReportData() :
                      prepareSalesReportData(); // Par défaut

    // Créer une fenêtre d'aperçu
    const previewWindow = window.open('', '_blank', 'width=800,height=600');
    if (previewWindow) {
      previewWindow.document.write(generateReportPreviewHTML(reportData));
      previewWindow.document.close();
    }

    toast({
      title: "Rapport généré",
      description: `Le rapport ${reportType} a été généré avec succès.`,
    });
  };

  // Fonction pour générer l'aperçu HTML du rapport
  const generateReportPreviewHTML = (data: ReportData) => {
    return `
      <!DOCTYPE html>
      <html lang="fr">
        <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>${data.title}</title>
          <style>
            body {
              font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
              margin: 0;
              padding: 20px;
              background-color: #f8f9fa;
              color: #333;
            }
            .report-container {
              max-width: 800px;
              margin: 0 auto;
              background: white;
              border-radius: 8px;
              box-shadow: 0 2px 10px rgba(0,0,0,0.1);
              overflow: hidden;
            }
            .report-header {
              background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
              color: white;
              padding: 30px;
              text-align: center;
            }
            .report-header h1 {
              margin: 0 0 10px 0;
              font-size: 28px;
              font-weight: 300;
            }
            .report-meta {
              background: #f8f9fa;
              padding: 20px;
              border-bottom: 1px solid #e9ecef;
            }
            .meta-grid {
              display: grid;
              grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
              gap: 15px;
            }
            .meta-item {
              display: flex;
              justify-content: space-between;
              padding: 8px 0;
              border-bottom: 1px solid #dee2e6;
            }
            .meta-label {
              font-weight: 600;
              color: #6c757d;
            }
            .report-content {
              padding: 30px;
            }
            .summary-section {
              margin-bottom: 30px;
            }
            .summary-grid {
              display: grid;
              grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
              gap: 20px;
              margin-bottom: 30px;
            }
            .summary-card {
              background: #f8f9fa;
              padding: 20px;
              border-radius: 8px;
              border-left: 4px solid #667eea;
              text-align: center;
            }
            .summary-value {
              font-size: 24px;
              font-weight: bold;
              color: #667eea;
              margin-bottom: 5px;
            }
            .summary-label {
              color: #6c757d;
              font-size: 14px;
            }
            .data-table {
              width: 100%;
              border-collapse: collapse;
              margin-top: 20px;
              background: white;
              border-radius: 8px;
              overflow: hidden;
              box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            }
            .data-table th {
              background: #667eea;
              color: white;
              padding: 15px;
              text-align: left;
              font-weight: 600;
            }
            .data-table td {
              padding: 12px 15px;
              border-bottom: 1px solid #e9ecef;
            }
            .data-table tr:hover {
              background-color: #f8f9fa;
            }
            .status-normal { color: #28a745; font-weight: bold; }
            .status-low { color: #ffc107; font-weight: bold; }
            .status-out { color: #dc3545; font-weight: bold; }
            .print-button {
              position: fixed;
              top: 20px;
              right: 20px;
              background: #667eea;
              color: white;
              border: none;
              padding: 12px 24px;
              border-radius: 6px;
              cursor: pointer;
              font-size: 14px;
              box-shadow: 0 2px 8px rgba(0,0,0,0.2);
            }
            .print-button:hover {
              background: #5a6fd8;
            }
            @media print {
              body { background: white; }
              .report-container { box-shadow: none; }
              .print-button { display: none; }
            }
          </style>
        </head>
        <body>
          <button class="print-button" onclick="window.print()">🖨️ Imprimer</button>

          <div class="report-container">
            <div class="report-header">
              <h1>${data.title}</h1>
              <p>BarStock Wise - Système de Gestion</p>
            </div>

            <div class="report-meta">
              <div class="meta-grid">
                <div class="meta-item">
                  <span class="meta-label">Période:</span>
                  <span>${data.period}</span>
                </div>
                <div class="meta-item">
                  <span class="meta-label">Généré le:</span>
                  <span>${data.generatedAt}</span>
                </div>
                <div class="meta-item">
                  <span class="meta-label">Généré par:</span>
                  <span>${data.generatedBy}</span>
                </div>
                <div class="meta-item">
                  <span class="meta-label">Nombre d'entrées:</span>
                  <span>${data.data.length}</span>
                </div>
              </div>
            </div>

            <div class="report-content">
              ${data.summary ? `
                <div class="summary-section">
                  <h2>Résumé Exécutif</h2>
                  <div class="summary-grid">
                    ${Object.entries(data.summary).map(([key, value]) => `
                      <div class="summary-card">
                        <div class="summary-value">${value}</div>
                        <div class="summary-label">${key}</div>
                      </div>
                    `).join('')}
                  </div>
                </div>
              ` : ''}

              <div class="data-section">
                <h2>Données Détaillées</h2>
                ${generateTableHTML(data)}
              </div>
            </div>
          </div>
        </body>
      </html>
    `;
  };

  // Fonction pour générer les tableaux HTML selon le type de rapport
  const generateTableHTML = (data: ReportData) => {
    if (data.title.includes('Ventes')) {
      const salesData = data as SalesReportData;
      return `
        <table class="data-table">
          <thead>
            <tr>
              <th>Date</th>
              <th>N° Commande</th>
              <th>Table</th>
              <th>Articles</th>
              <th>Serveur</th>
              <th>Montant</th>
            </tr>
          </thead>
          <tbody>
            ${salesData.data.map(sale => `
              <tr>
                <td>${sale.date}</td>
                <td>${sale.orderId}</td>
                <td>${sale.table}</td>
                <td>${sale.items}</td>
                <td>${sale.server}</td>
                <td style="font-weight: bold; color: #28a745;">${formatCurrency(sale.total)}</td>
              </tr>
            `).join('')}
          </tbody>
        </table>
      `;
    } else if (data.title.includes('Stock')) {
      const stockData = data as StockReportData;
      return `
        <table class="data-table">
          <thead>
            <tr>
              <th>Produit</th>
              <th>Catégorie</th>
              <th>Stock Actuel</th>
              <th>Seuil Min.</th>
              <th>Valeur</th>
              <th>Statut</th>
            </tr>
          </thead>
          <tbody>
            ${stockData.data.map(item => `
              <tr>
                <td>${item.product}</td>
                <td>${item.category}</td>
                <td>${item.currentStock}</td>
                <td>${item.minThreshold}</td>
                <td>${formatCurrency(item.value)}</td>
                <td class="status-${item.status === 'Normal' ? 'normal' : item.status === 'Stock faible' ? 'low' : 'out'}">
                  ${item.status}
                </td>
              </tr>
            `).join('')}
          </tbody>
        </table>
      `;
    } else if (data.title.includes('Dépenses')) {
      const expenseData = data as ExpenseReportData;
      return `
        <table class="data-table">
          <thead>
            <tr>
              <th>Date</th>
              <th>Catégorie</th>
              <th>Description</th>
              <th>Montant</th>
              <th>Approuvé par</th>
            </tr>
          </thead>
          <tbody>
            ${expenseData.data.map(expense => `
              <tr>
                <td>${expense.date}</td>
                <td>${expense.category}</td>
                <td>${expense.description}</td>
                <td style="font-weight: bold; color: #dc3545;">${formatCurrency(expense.amount)}</td>
                <td>${expense.approvedBy}</td>
              </tr>
            `).join('')}
          </tbody>
        </table>
      `;
    }
    return '<p>Aucune donnée disponible</p>';
  };

  const exportToPDF = () => {
    if (!canExportReports()) {
      toast({
        title: "Accès refusé",
        description: "Vous n'avez pas les permissions pour exporter les rapports.",
        variant: "destructive",
      });
      return;
    }

    try {
      const currentDate = new Date().toISOString();
      const period = `${startDate || 'Début'} - ${endDate || 'Fin'}`;

      // Préparer les données réelles selon le type de rapport
      if (reportType === 'sales') {
        const salesData = prepareSalesReportData();
        exportSalesReportToPDF(salesData);
      } else if (reportType === 'stock') {
        const stockData = prepareStockReportData();
        exportStockReportToPDF(stockData);
      } else if (reportType === 'expenses') {
        const expenseData = prepareExpenseReportData();
        exportExpenseReportToPDF(expenseData);
      } else {
        // Par défaut, rapport de ventes
        const salesData = prepareSalesReportData();
        exportSalesReportToPDF(salesData);
      }

      toast({
        title: "Export PDF réussi",
        description: "Le rapport a été exporté en PDF avec succès.",
      });
    } catch (error) {
      toast({
        title: "Erreur d'export",
        description: "Une erreur est survenue lors de l'export PDF.",
        variant: "destructive",
      });
    }
  };

  const handleExportToExcel = () => {
    if (!canExportReports()) {
      toast({
        title: "Accès refusé",
        description: "Vous n'avez pas les permissions pour exporter les rapports.",
        variant: "destructive",
      });
      return;
    }

    try {
      // Préparer les données selon le type de rapport
      let excelData: any[] = [];

      if (reportType === 'sales') {
        const salesData = prepareSalesReportData();
        excelData = salesData.data.map(sale => ({
          Date: sale.date,
          'N° Commande': sale.orderId,
          Table: sale.table,
          Articles: sale.items,
          Serveur: sale.server,
          Montant: sale.total
        }));
      } else if (reportType === 'stock') {
        const stockData = prepareStockReportData();
        excelData = stockData.data.map(item => ({
          Produit: item.product,
          Catégorie: item.category,
          'Stock Actuel': item.currentStock,
          'Seuil Min.': item.minThreshold,
          Valeur: item.value,
          Statut: item.status
        }));
      } else if (reportType === 'expenses') {
        const expenseData = prepareExpenseReportData();
        excelData = expenseData.data.map(expense => ({
          Date: expense.date,
          Catégorie: expense.category,
          Description: expense.description,
          Montant: expense.amount,
          'Approuvé par': expense.approvedBy
        }));
      }

      exportToExcel(excelData, `rapport-${reportType}-${new Date().toISOString().split('T')[0]}.csv`);

      toast({
        title: "Export Excel réussi",
        description: `Le rapport ${reportType} a été exporté en CSV avec succès.`,
      });
    } catch (error) {
      console.error('Erreur lors de l\'export Excel:', error);
      toast({
        title: "Erreur d'export",
        description: "Une erreur est survenue lors de l'export Excel.",
        variant: "destructive",
      });
    }
  };

  const ReportCard = ({ title, value, description, icon: Icon, variant = "default" }: {
    title: string;
    value: string | number;
    description: string;
    icon: any;
    variant?: "default" | "success" | "warning" | "destructive";
  }) => (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">{title}</CardTitle>
        <Icon className={`h-4 w-4 ${
          variant === "success" ? "text-success" :
          variant === "warning" ? "text-warning" :
          variant === "destructive" ? "text-destructive" :
          "text-muted-foreground"
        }`} />
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold">
          {typeof value === 'number' ? value.toLocaleString() : value}
        </div>
        <p className="text-xs text-muted-foreground">{description}</p>
      </CardContent>
    </Card>
  );

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Rapports et Analyses</h1>
          <p className="text-muted-foreground">
            Générez et consultez des rapports détaillés sur votre activité
          </p>
        </div>
        
        <div className="flex gap-2">
          {canExportReports() && (
            <>
              <Button variant="outline" onClick={exportToPDF}>
                <FileText className="w-4 h-4 mr-2" />
                PDF
              </Button>
              <Button variant="outline" onClick={handleExportToExcel}>
                <Download className="w-4 h-4 mr-2" />
                Excel
              </Button>
            </>
          )}
        </div>
      </div>

      {/* Report Configuration */}
      <Card>
        <CardHeader>
          <CardTitle>Configuration du Rapport</CardTitle>
          <CardDescription>
            Sélectionnez le type et la période pour générer votre rapport
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid gap-4 md:grid-cols-3">
            <div className="space-y-2">
              <Label htmlFor="reportType">Type de rapport</Label>
              <Select value={reportType} onValueChange={setReportType}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="daily">Rapport Quotidien</SelectItem>
                  <SelectItem value="weekly">Rapport Hebdomadaire</SelectItem>
                  <SelectItem value="monthly">Rapport Mensuel</SelectItem>
                  <SelectItem value="custom">Période Personnalisée</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="period">Période</Label>
              <Select value={selectedPeriod} onValueChange={setSelectedPeriod}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="today">Aujourd'hui</SelectItem>
                  <SelectItem value="yesterday">Hier</SelectItem>
                  <SelectItem value="thisWeek">Cette semaine</SelectItem>
                  <SelectItem value="lastWeek">Semaine dernière</SelectItem>
                  <SelectItem value="thisMonth">Ce mois</SelectItem>
                  <SelectItem value="lastMonth">Mois dernier</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-2">
              <Label>&nbsp;</Label>
              <Button onClick={generateReport} className="w-full">
                Générer le Rapport
              </Button>
            </div>
          </div>

          {reportType === 'custom' && (
            <div className="grid gap-4 md:grid-cols-2">
              <div className="space-y-2">
                <Label htmlFor="startDate">Date de début</Label>
                <Input
                  id="startDate"
                  type="date"
                  value={startDate}
                  onChange={(e) => setStartDate(e.target.value)}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="endDate">Date de fin</Label>
                <Input
                  id="endDate"
                  type="date"
                  value={endDate}
                  onChange={(e) => setEndDate(e.target.value)}
                />
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Loading State */}
      {isLoading && (
        <div className="space-y-6">
          <div className="grid gap-4 md:grid-cols-4">
            {[...Array(4)].map((_, i) => (
              <Card key={i}>
                <CardHeader>
                  <Skeleton className="h-4 w-24" />
                </CardHeader>
                <CardContent>
                  <Skeleton className="h-8 w-20 mb-2" />
                  <Skeleton className="h-3 w-32" />
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      )}

      {/* Key Metrics */}
      {!isLoading && (
        <div className="grid gap-4 md:grid-cols-4">
          <ReportCard
            title="Chiffre d'Affaires"
            value={formatCurrency(reportData.sales.total)}
            description="Ventes totales"
            icon={DollarSign}
            variant="success"
          />
        <ReportCard
          title="Bénéfices"
          value={formatCurrency(reportData.sales.total * 0.3)} // Estimation 30% de marge
          description="Marge estimée"
          icon={TrendingUp}
          variant="success"
        />
        <ReportCard
          title="Commandes"
          value={reportData.sales.count.toString()}
          description="Transactions totales"
          icon={BarChart3}
          variant="default"
        />
        <ReportCard
          title="Ticket Moyen"
          value={formatCurrency(reportData.sales.count > 0 ? Math.round(reportData.sales.total / reportData.sales.count) : 0)}
          description="Par transaction"
          icon={Calendar}
          variant="default"
        />
        </div>
      )}

      <div className="grid gap-6 lg:grid-cols-2">
        {/* Top Products */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BarChart3 className="w-5 h-5" />
              Produits les Plus Vendus
            </CardTitle>
            <CardDescription>
              Classement par chiffre d'affaires (aujourd'hui)
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {dynamicReportData.daily.topProducts.map((product, index) => (
                <div key={product.name} className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <Badge variant="outline" className="w-8 h-8 rounded-full flex items-center justify-center p-0">
                      {index + 1}
                    </Badge>
                    <div>
                      <p className="font-medium">{product.name}</p>
                      <p className="text-sm text-muted-foreground">
                        {product.quantity} vendus
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="font-bold">
                      {product.revenue.toLocaleString()} BIF
                    </p>
                    <div className="w-24 bg-muted rounded-full h-2 mt-1">
                      <div 
                        className="bg-primary h-2 rounded-full" 
                        style={{ 
                          width: `${dynamicReportData.daily.topProducts[0] ? (product.revenue / dynamicReportData.daily.topProducts[0].revenue) * 100 : 0}%`
                        }} 
                      />
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Sales by Category */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="w-5 h-5" />
              Ventes par Catégorie
            </CardTitle>
            <CardDescription>
              Répartition mensuelle ({dynamicReportData.monthly.month})
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {dynamicReportData.monthly.categories.map((category) => (
                <div key={category.name} className="space-y-2">
                  <div className="flex justify-between items-center">
                    <span className="font-medium">{category.name}</span>
                    <div className="text-right">
                      <span className="font-bold">
                        {formatCurrency(category.revenue)}
                      </span>
                      <span className="text-sm text-muted-foreground ml-2">
                        ({category.percentage}%)
                      </span>
                    </div>
                  </div>
                  <div className="w-full bg-muted rounded-full h-2">
                    <div 
                      className="bg-primary h-2 rounded-full" 
                      style={{ width: `${category.percentage}%` }}
                    />
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Stock Summary */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BarChart3 className="w-5 h-5" />
              Résumé des Stocks
            </CardTitle>
            <CardDescription>
              État actuel de l'inventaire
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="text-center p-4 bg-muted/50 rounded-lg">
                  <div className="text-2xl font-bold">{reportData.stocks.totalProducts}</div>
                  <p className="text-sm text-muted-foreground">Produits Total</p>
                </div>
                <div className="text-center p-4 bg-muted/50 rounded-lg">
                  <div className="text-2xl font-bold text-warning">{reportData.stocks.lowStock}</div>
                  <p className="text-sm text-muted-foreground">Stock Bas</p>
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div className="text-center p-4 bg-muted/50 rounded-lg">
                  <div className="text-2xl font-bold text-destructive">{reportData.stocks.outOfStock}</div>
                  <p className="text-sm text-muted-foreground">En Rupture</p>
                </div>
                <div className="text-center p-4 bg-muted/50 rounded-lg">
                  <div className="text-lg font-bold">
                    {(dynamicReportData.stocks.totalValue / 1000000).toFixed(1)}M BIF
                  </div>
                  <p className="text-sm text-muted-foreground">Valeur Stock</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Performance Trends */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="w-5 h-5" />
              Tendances de Performance
            </CardTitle>
            <CardDescription>
              Évolution sur 7 jours
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex justify-between items-center p-3 bg-muted/50 rounded-lg">
                <span>Ventes moyennes/jour</span>
                <div className="text-right">
                  <span className="font-bold">{formatCurrency(180000)}</span>
                  <Badge variant="default" className="ml-2">+15%</Badge>
                </div>
              </div>
              <div className="flex justify-between items-center p-3 bg-muted/50 rounded-lg">
                <span>Commandes moyennes/jour</span>
                <div className="text-right">
                  <span className="font-bold">42</span>
                  <Badge variant="default" className="ml-2">+8%</Badge>
                </div>
              </div>
              <div className="flex justify-between items-center p-3 bg-muted/50 rounded-lg">
                <span>Marge bénéficiaire</span>
                <div className="text-right">
                  <span className="font-bold">44%</span>
                  <Badge variant="default" className="ml-2">+2%</Badge>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default Reports;