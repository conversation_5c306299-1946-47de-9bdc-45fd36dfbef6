from rest_framework import generics, status, permissions
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework_simplejwt.tokens import RefreshToken
from django.contrib.auth import login, logout
from django.utils import timezone
from .models import User, UserActivity
from .serializers import (
    UserSerializer, UserLoginSerializer, UserActivitySerializer,
    ChangePasswordSerializer, UserProfileSerializer
)

class UserListCreateView(generics.ListCreateAPIView):
    """
    Vue pour lister et créer des utilisateurs
    """
    queryset = User.objects.all()
    serializer_class = UserSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        # Seuls les admins peuvent voir tous les utilisateurs
        if self.request.user.is_admin:
            return User.objects.all()
        else:
            return User.objects.filter(id=self.request.user.id)

    def perform_create(self, serializer):
        # Seuls les admins peuvent créer des utilisateurs
        if not self.request.user.is_admin:
            raise permissions.PermissionDenied("Seuls les admins peuvent créer des utilisateurs.")

        user = serializer.save()

        # Enregistrer l'activité
        UserActivity.objects.create(
            user=self.request.user,
            action='create',
            description=f"Création de l'utilisateur {user.username}"
        )


class UserDetailView(generics.RetrieveUpdateDestroyAPIView):
    """
    Vue pour récupérer, modifier ou supprimer un utilisateur
    """
    queryset = User.objects.all()
    serializer_class = UserSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_object(self):
        obj = super().get_object()
        # Les utilisateurs ne peuvent modifier que leur propre profil, sauf les admins
        if not self.request.user.is_admin and obj != self.request.user:
            raise permissions.PermissionDenied("Vous ne pouvez modifier que votre propre profil.")
        return obj

    def perform_update(self, serializer):
        user = serializer.save()

        # Enregistrer l'activité
        UserActivity.objects.create(
            user=self.request.user,
            action='update',
            description=f"Modification de l'utilisateur {user.username}"
        )

    def perform_destroy(self, instance):
        # Seuls les admins peuvent supprimer des utilisateurs
        if not self.request.user.is_admin:
            raise permissions.PermissionDenied("Seuls les admins peuvent supprimer des utilisateurs.")

        # Enregistrer l'activité avant suppression
        UserActivity.objects.create(
            user=self.request.user,
            action='delete',
            description=f"Suppression de l'utilisateur {instance.username}"
        )

        instance.delete()


@api_view(['POST'])
@permission_classes([permissions.AllowAny])
def login_view(request):
    """
    Vue pour l'authentification
    """
    serializer = UserLoginSerializer(data=request.data)
    if serializer.is_valid():
        user = serializer.validated_data['user']

        # Générer les tokens JWT
        refresh = RefreshToken.for_user(user)
        access_token = refresh.access_token

        # Mettre à jour le statut de session
        user.is_active_session = True
        user.last_activity = timezone.now()
        user.save()

        # Enregistrer l'activité de connexion
        UserActivity.objects.create(
            user=user,
            action='login',
            description='Connexion réussie',
            ip_address=request.META.get('REMOTE_ADDR'),
            user_agent=request.META.get('HTTP_USER_AGENT')
        )

        return Response({
            'message': 'Connexion réussie',
            'user': UserSerializer(user).data,
            'tokens': {
                'access': str(access_token),
                'refresh': str(refresh)
            }
        }, status=status.HTTP_200_OK)

    return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def logout_view(request):
    """
    Vue pour la déconnexion
    """
    user = request.user
    user.is_active_session = False
    user.save()

    # Enregistrer l'activité de déconnexion
    UserActivity.objects.create(
        user=user,
        action='logout',
        description='Déconnexion',
        ip_address=request.META.get('REMOTE_ADDR'),
        user_agent=request.META.get('HTTP_USER_AGENT')
    )

    return Response({'message': 'Déconnexion réussie'}, status=status.HTTP_200_OK)


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def profile_view(request):
    """
    Vue pour récupérer le profil de l'utilisateur connecté
    """
    serializer = UserProfileSerializer(request.user)
    return Response(serializer.data)


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def change_password_view(request):
    """
    Vue pour changer le mot de passe
    """
    serializer = ChangePasswordSerializer(data=request.data, context={'request': request})
    if serializer.is_valid():
        user = request.user
        user.set_password(serializer.validated_data['new_password'])
        user.save()

        # Enregistrer l'activité
        UserActivity.objects.create(
            user=user,
            action='update',
            description='Changement de mot de passe'
        )

        return Response({'message': 'Mot de passe changé avec succès'}, status=status.HTTP_200_OK)

    return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class UserActivityListView(generics.ListAPIView):
    """
    Vue pour lister les activités des utilisateurs
    """
    serializer_class = UserActivitySerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        # Les admins voient toutes les activités, les autres seulement les leurs
        if self.request.user.is_admin:
            return UserActivity.objects.all()
        else:
            return UserActivity.objects.filter(user=self.request.user)


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def check_permissions_view(request):
    """
    Vue pour vérifier les permissions de l'utilisateur connecté
    """
    user = request.user
    permissions_data = {
        'role': user.role,
        'permissions': {
            'can_manage_users': user.can_manage_users(),
            'can_manage_products': user.can_manage_products(),
            'can_make_sales': user.can_make_sales(),
            'can_view_sales_history': user.can_view_sales_history(),
            'can_manage_inventory': user.can_manage_inventory(),
            'can_view_stock_alerts': user.can_view_stock_alerts(),
            'can_generate_reports': user.can_generate_reports(),
            'can_manage_expenses': user.can_manage_expenses(),
            'can_delete_records': user.can_delete_records(),
            'can_manage_database': user.can_manage_database(),
        }
    }

    return Response(permissions_data)
