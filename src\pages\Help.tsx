import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { 
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion';
import { 
  HelpCircle, 
  Search, 
  Book, 
  Video, 
  MessageCircle, 
  Phone,
  Mail,
  FileText,
  Settings,
  ShoppingCart,
  Package,
  Users,
  BarChart3
} from 'lucide-react';

const Help = () => {
  const [searchQuery, setSearchQuery] = useState('');

  const helpCategories = [
    {
      id: 'getting-started',
      title: 'Premiers Pas',
      icon: Book,
      description: 'Apprenez les bases de BarStockWise',
      articles: [
        'Configuration initiale du système',
        'Création de votre premier utilisateur',
        'Navigation dans l\'interface',
        'Personnalisation du tableau de bord'
      ]
    },
    {
      id: 'sales',
      title: 'Gestion des Ventes',
      icon: ShoppingCart,
      description: 'Tout sur les ventes et commandes',
      articles: [
        'Créer une nouvelle vente',
        'Gérer les tables et clients',
        'Imprimer les reçus',
        'Annuler ou modifier une commande'
      ]
    },
    {
      id: 'inventory',
      title: 'Gestion des Stocks',
      icon: Package,
      description: 'Contrôlez votre inventaire',
      articles: [
        'Ajouter de nouveaux produits',
        'Mettre à jour les stocks',
        'Alertes de stock bas',
        'Rapports d\'inventaire'
      ]
    },
    {
      id: 'users',
      title: 'Gestion des Utilisateurs',
      icon: Users,
      description: 'Administration des comptes',
      articles: [
        'Créer de nouveaux utilisateurs',
        'Gérer les rôles et permissions',
        'Réinitialiser les mots de passe',
        'Historique des connexions'
      ]
    },
    {
      id: 'reports',
      title: 'Rapports et Analyses',
      icon: BarChart3,
      description: 'Analysez vos performances',
      articles: [
        'Générer des rapports de ventes',
        'Analyser les tendances',
        'Exporter vers Excel/PDF',
        'Rapports personnalisés'
      ]
    },
    {
      id: 'settings',
      title: 'Configuration',
      icon: Settings,
      description: 'Paramètres du système',
      articles: [
        'Paramètres généraux',
        'Configuration des imprimantes',
        'Sauvegardes automatiques',
        'Sécurité et accès'
      ]
    }
  ];

  const faqItems = [
    {
      question: "Comment réinitialiser mon mot de passe ?",
      answer: "Contactez votre administrateur système pour réinitialiser votre mot de passe. Seuls les administrateurs peuvent modifier les mots de passe des utilisateurs."
    },
    {
      question: "Que faire si le stock d'un produit est négatif ?",
      answer: "Un stock négatif indique que plus de produits ont été vendus que ce qui était disponible. Vérifiez les entrées de stock et ajustez l'inventaire dans la section Stocks."
    },
    {
      question: "Comment imprimer les reçus ?",
      answer: "Les reçus s'impriment automatiquement après chaque vente si l'impression automatique est activée dans les paramètres. Vous pouvez aussi réimprimer depuis l'historique des ventes."
    },
    {
      question: "Puis-je modifier une vente déjà enregistrée ?",
      answer: "Les ventes finalisées ne peuvent pas être modifiées pour des raisons de traçabilité. Vous pouvez créer un avoir ou une nouvelle transaction pour corriger les erreurs."
    },
    {
      question: "Comment sauvegarder mes données ?",
      answer: "Les données sont automatiquement sauvegardées. Les administrateurs peuvent créer des sauvegardes manuelles depuis la section Paramètres > Base de données."
    }
  ];

  const filteredCategories = helpCategories.filter(category =>
    category.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
    category.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
    category.articles.some(article => 
      article.toLowerCase().includes(searchQuery.toLowerCase())
    )
  );

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Centre d'Aide</h1>
          <p className="text-muted-foreground">
            Trouvez des réponses à vos questions et apprenez à utiliser BarStockWise
          </p>
        </div>
        
        <div className="flex gap-2">
          <Button variant="outline">
            <Video className="w-4 h-4 mr-2" />
            Tutoriels Vidéo
          </Button>
          <Button>
            <MessageCircle className="w-4 h-4 mr-2" />
            Contacter le Support
          </Button>
        </div>
      </div>

      {/* Search */}
      <Card>
        <CardContent className="pt-6">
          <div className="relative">
            <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Rechercher dans l'aide..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
        </CardContent>
      </Card>

      {/* Quick Actions */}
      <div className="grid gap-4 md:grid-cols-3">
        <Card className="cursor-pointer hover:shadow-md transition-shadow">
          <CardContent className="pt-6 text-center">
            <Book className="w-12 h-12 mx-auto mb-4 text-primary" />
            <h3 className="font-semibold mb-2">Guide de Démarrage</h3>
            <p className="text-sm text-muted-foreground">
              Apprenez les bases en 10 minutes
            </p>
          </CardContent>
        </Card>
        
        <Card className="cursor-pointer hover:shadow-md transition-shadow">
          <CardContent className="pt-6 text-center">
            <Video className="w-12 h-12 mx-auto mb-4 text-primary" />
            <h3 className="font-semibold mb-2">Tutoriels Vidéo</h3>
            <p className="text-sm text-muted-foreground">
              Regardez des démonstrations pratiques
            </p>
          </CardContent>
        </Card>
        
        <Card className="cursor-pointer hover:shadow-md transition-shadow">
          <CardContent className="pt-6 text-center">
            <HelpCircle className="w-12 h-12 mx-auto mb-4 text-primary" />
            <h3 className="font-semibold mb-2">FAQ</h3>
            <p className="text-sm text-muted-foreground">
              Réponses aux questions fréquentes
            </p>
          </CardContent>
        </Card>
      </div>

      <div className="grid gap-6 lg:grid-cols-3">
        {/* Help Categories */}
        <div className="lg:col-span-2">
          <Card>
            <CardHeader>
              <CardTitle>Catégories d'Aide</CardTitle>
              <CardDescription>
                Explorez nos guides par sujet
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-2">
                {filteredCategories.map((category) => {
                  const Icon = category.icon;
                  return (
                    <Card key={category.id} className="cursor-pointer hover:shadow-md transition-shadow">
                      <CardHeader className="pb-3">
                        <div className="flex items-center gap-3">
                          <Icon className="w-5 h-5 text-primary" />
                          <CardTitle className="text-base">{category.title}</CardTitle>
                        </div>
                        <CardDescription className="text-sm">
                          {category.description}
                        </CardDescription>
                      </CardHeader>
                      <CardContent className="pt-0">
                        <div className="space-y-1">
                          {category.articles.slice(0, 3).map((article, index) => (
                            <p key={index} className="text-sm text-muted-foreground hover:text-primary cursor-pointer">
                              • {article}
                            </p>
                          ))}
                          {category.articles.length > 3 && (
                            <p className="text-sm text-primary cursor-pointer">
                              + {category.articles.length - 3} autres articles
                            </p>
                          )}
                        </div>
                      </CardContent>
                    </Card>
                  );
                })}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* FAQ and Contact */}
        <div className="space-y-6">
          {/* FAQ */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <HelpCircle className="w-5 h-5" />
                Questions Fréquentes
              </CardTitle>
            </CardHeader>
            <CardContent>
              <Accordion type="single" collapsible>
                {faqItems.map((item, index) => (
                  <AccordionItem key={index} value={`item-${index}`}>
                    <AccordionTrigger className="text-left">
                      {item.question}
                    </AccordionTrigger>
                    <AccordionContent>
                      {item.answer}
                    </AccordionContent>
                  </AccordionItem>
                ))}
              </Accordion>
            </CardContent>
          </Card>

          {/* Contact Support */}
          <Card>
            <CardHeader>
              <CardTitle>Besoin d'Aide ?</CardTitle>
              <CardDescription>
                Notre équipe est là pour vous aider
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center gap-3 p-3 bg-muted/50 rounded-lg">
                <Mail className="w-5 h-5 text-primary" />
                <div>
                  <p className="font-medium">Email</p>
                  <p className="text-sm text-muted-foreground"><EMAIL></p>
                </div>
              </div>
              
              <div className="flex items-center gap-3 p-3 bg-muted/50 rounded-lg">
                <Phone className="w-5 h-5 text-primary" />
                <div>
                  <p className="font-medium">Téléphone</p>
                  <p className="text-sm text-muted-foreground">+257 XX XX XX XX</p>
                </div>
              </div>
              
              <div className="flex items-center gap-3 p-3 bg-muted/50 rounded-lg">
                <MessageCircle className="w-5 h-5 text-primary" />
                <div>
                  <p className="font-medium">Chat en Direct</p>
                  <p className="text-sm text-muted-foreground">Lun-Ven 8h-18h</p>
                </div>
              </div>
              
              <Button className="w-full">
                <MessageCircle className="w-4 h-4 mr-2" />
                Ouvrir un Ticket
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default Help;
