// Configuration de l'API pour connecter le frontend au backend Django
import axios, { AxiosInstance, AxiosResponse, InternalAxiosRequestConfig } from 'axios';

// Configuration de base
const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:8000/api';

// Instance Axios configurée
const apiClient: AxiosInstance = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Types pour l'authentification
export interface LoginCredentials {
  username: string;
  password: string;
}

export interface AuthTokens {
  access: string;
  refresh: string;
}

export interface User {
  id: string;
  username: string;
  email: string;
  first_name: string;
  last_name: string;
  role: 'admin' | 'gerant' | 'serveur';
  phone?: string;
  address?: string;
  is_active: boolean;
  last_activity: string;
}

// Gestion des tokens
class TokenManager {
  private static ACCESS_TOKEN_KEY = 'access_token';
  private static REFRESH_TOKEN_KEY = 'refresh_token';

  static getAccessToken(): string | null {
    return localStorage.getItem(this.ACCESS_TOKEN_KEY);
  }

  static getRefreshToken(): string | null {
    return localStorage.getItem(this.REFRESH_TOKEN_KEY);
  }

  static setTokens(tokens: AuthTokens): void {
    localStorage.setItem(this.ACCESS_TOKEN_KEY, tokens.access);
    localStorage.setItem(this.REFRESH_TOKEN_KEY, tokens.refresh);
  }

  static clearTokens(): void {
    localStorage.removeItem(this.ACCESS_TOKEN_KEY);
    localStorage.removeItem(this.REFRESH_TOKEN_KEY);
  }

  static isTokenExpired(token: string): boolean {
    try {
      const payload = JSON.parse(atob(token.split('.')[1]));
      const currentTime = Date.now() / 1000;
      return payload.exp < currentTime;
    } catch {
      return true;
    }
  }
}

// Intercepteur pour ajouter le token d'authentification
apiClient.interceptors.request.use(
  (config: InternalAxiosRequestConfig) => {
    const token = TokenManager.getAccessToken();
    if (token && config.headers) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => Promise.reject(error)
);

// Intercepteur pour gérer le refresh des tokens
apiClient.interceptors.response.use(
  (response: AxiosResponse) => response,
  async (error) => {
    const originalRequest = error.config;

    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;

      const refreshToken = TokenManager.getRefreshToken();
      if (refreshToken) {
        try {
          const response = await axios.post(`${API_BASE_URL}/auth/token/refresh/`, {
            refresh: refreshToken,
          });

          const newTokens: AuthTokens = response.data;
          TokenManager.setTokens(newTokens);

          // Retry la requête originale avec le nouveau token
          originalRequest.headers.Authorization = `Bearer ${newTokens.access}`;
          return apiClient(originalRequest);
        } catch (refreshError) {
          // Le refresh a échoué, déconnecter l'utilisateur
          TokenManager.clearTokens();
          window.location.href = '/login';
          return Promise.reject(refreshError);
        }
      }
    }

    return Promise.reject(error);
  }
);

// API d'authentification
export const authAPI = {
  login: async (credentials: LoginCredentials): Promise<{ user: User; tokens: AuthTokens }> => {
    const response = await apiClient.post('/auth/login/', credentials);
    return response.data;
  },

  logout: async (): Promise<void> => {
    await apiClient.post('/auth/logout/');
    TokenManager.clearTokens();
  },

  getProfile: async (): Promise<User> => {
    const response = await apiClient.get('/auth/profile/');
    return response.data;
  },

  updateProfile: async (data: any): Promise<User> => {
    const response = await apiClient.put('/auth/profile/', data);
    return response.data;
  },

  changePassword: async (data: { current_password: string; new_password: string }): Promise<void> => {
    await apiClient.post('/auth/change-password/', data);
  },

  checkPermissions: async (): Promise<any> => {
    const response = await apiClient.get('/auth/permissions/');
    return response.data;
  },
};

// API des produits
export const productsAPI = {
  getAll: async (params?: any) => {
    console.log('Fetching products with params:', params);
    const response = await apiClient.get('/products/', { params });
    console.log('Products API response:', response);
    return response.data;
  },

  getById: async (id: string) => {
    const response = await apiClient.get(`/products/${id}/`);
    return response.data;
  },

  create: async (data: any) => {
    const response = await apiClient.post('/products/', data);
    return response.data;
  },

  update: async (id: string, data: any) => {
    const response = await apiClient.put(`/products/${id}/`, data);
    return response.data;
  },

  delete: async (id: string) => {
    await apiClient.delete(`/products/${id}/`);
  },

  getCategories: async () => {
    const response = await apiClient.get('/products/categories/');
    return response.data;
  },
};

// API des ventes
export const salesAPI = {
  getAll: async (params?: any) => {
    const response = await apiClient.get('/sales/', { params });
    return response.data;
  },

  getById: async (id: string) => {
    const response = await apiClient.get(`/sales/${id}/`);
    return response.data;
  },

  create: async (data: any) => {
    const response = await apiClient.post('/sales/', data);
    return response.data;
  },

  update: async (id: string, data: any) => {
    const response = await apiClient.put(`/sales/${id}/`, data);
    return response.data;
  },

  delete: async (id: string) => {
    await apiClient.delete(`/sales/${id}/`);
  },

  getDailySummary: async (date?: string) => {
    const response = await apiClient.get('/sales/daily-summary/', { 
      params: date ? { date } : {} 
    });
    return response.data;
  },
};

// API des stocks/inventaire
export const inventoryAPI = {
  getAll: async (params?: any) => {
    const response = await apiClient.get('/inventory/', { params });
    return response.data;
  },

  getStockMovements: async (params?: any) => {
    const response = await apiClient.get('/inventory/movements/', { params });
    return response.data;
  },

  createStockMovement: async (data: any) => {
    const response = await apiClient.post('/inventory/movements/', data);
    return response.data;
  },

  getPurchases: async (params?: any) => {
    const response = await apiClient.get('/inventory/purchases/', { params });
    return response.data;
  },

  getPurchaseById: async (id: string) => {
    const response = await apiClient.get(`/inventory/purchases/${id}/`);
    return response.data;
  },

  createPurchase: async (data: any) => {
    const response = await apiClient.post('/inventory/purchases/', data);
    return response.data;
  },

  updatePurchase: async (id: string, data: any) => {
    const response = await apiClient.put(`/inventory/purchases/${id}/`, data);
    return response.data;
  },

  deletePurchase: async (id: string) => {
    const response = await apiClient.delete(`/inventory/purchases/${id}/`);
    return response.data;
  },

  getLowStockAlerts: async () => {
    const response = await apiClient.get('/products/low-stock/');
    return response.data;
  },
};

// API des fournisseurs
export const suppliersAPI = {
  getAll: async (params?: any) => {
    const response = await apiClient.get('/suppliers/', { params });
    return response.data;
  },

  getById: async (id: string) => {
    const response = await apiClient.get(`/suppliers/${id}/`);
    return response.data;
  },

  create: async (data: any) => {
    const response = await apiClient.post('/suppliers/', data);
    return response.data;
  },

  update: async (id: string, data: any) => {
    const response = await apiClient.put(`/suppliers/${id}/`, data);
    return response.data;
  },

  delete: async (id: string) => {
    await apiClient.delete(`/suppliers/${id}/`);
  },
};

// API des tables
export const tablesAPI = {
  getAll: async (params?: any) => {
    const response = await apiClient.get('/sales/tables/', { params });
    return response.data;
  },

  getById: async (id: string) => {
    const response = await apiClient.get(`/sales/tables/${id}/`);
    return response.data;
  },

  create: async (data: any) => {
    const response = await apiClient.post('/sales/tables/', data);
    return response.data;
  },

  update: async (id: string, data: any) => {
    const response = await apiClient.put(`/sales/tables/${id}/`, data);
    return response.data;
  },

  delete: async (id: string) => {
    await apiClient.delete(`/sales/tables/${id}/`);
  },
};

// API des dépenses
export const expensesAPI = {
  getAll: async (params?: any) => {
    const response = await apiClient.get('/expenses/', { params });
    return response.data;
  },

  getById: async (id: string) => {
    const response = await apiClient.get(`/expenses/${id}/`);
    return response.data;
  },

  create: async (data: any) => {
    const response = await apiClient.post('/expenses/', data);
    return response.data;
  },

  update: async (id: string, data: any) => {
    const response = await apiClient.put(`/expenses/${id}/`, data);
    return response.data;
  },

  delete: async (id: string) => {
    await apiClient.delete(`/expenses/${id}/`);
  },

  getCategories: async () => {
    const response = await apiClient.get('/expenses/categories/');
    return response.data;
  },

  getMonthlySummary: async (year: number, month: number) => {
    const response = await apiClient.get('/expenses/monthly-summary/', {
      params: { year, month }
    });
    return response.data;
  },
};

// API des factures
export const invoicesAPI = {
  getAll: async (params?: any) => {
    const response = await apiClient.get('/sales/invoices/', { params });
    return response.data;
  },

  getById: async (id: string) => {
    const response = await apiClient.get(`/sales/invoices/${id}/`);
    return response.data;
  },

  create: async (data: any) => {
    const response = await apiClient.post('/sales/invoices/', data);
    return response.data;
  },

  update: async (id: string, data: any) => {
    const response = await apiClient.put(`/sales/invoices/${id}/`, data);
    return response.data;
  },

  delete: async (id: string) => {
    const response = await apiClient.delete(`/sales/invoices/${id}/`);
    return response.data;
  },

  generateFromSale: async (saleId: string) => {
    const response = await apiClient.post(`/sales/${saleId}/generate-invoice/`);
    return response.data;
  },

  exportPDF: async (id: string) => {
    const response = await apiClient.get(`/sales/invoices/${id}/export-pdf/`, {
      responseType: 'blob'
    });
    return response.data;
  },
};

// API des rapports
export const reportsAPI = {
  getDailyReports: async (params?: any) => {
    const response = await apiClient.get('/reports/daily/', { params });
    return response.data;
  },

  createDailyReport: async (data: any) => {
    const response = await apiClient.post('/reports/daily/', data);
    return response.data;
  },

  updateDailyReport: async (id: string, data: any) => {
    const response = await apiClient.put(`/reports/daily/${id}/`, data);
    return response.data;
  },

  exportDailyReportPDF: async (id: string) => {
    const response = await apiClient.get(`/reports/daily/${id}/export-pdf/`, {
      responseType: 'blob'
    });
    return response.data;
  },

  exportDailyReportExcel: async (id: string) => {
    const response = await apiClient.get(`/reports/daily/${id}/export-excel/`, {
      responseType: 'blob'
    });
    return response.data;
  },

  getStockAlerts: async () => {
    const response = await apiClient.get('/reports/alerts/');
    return response.data;
  },

  // Analytics endpoints - temporairement désactivés car non implémentés dans le backend
  getSalesAnalytics: async (params?: any) => {
    // Endpoint non disponible - retourner des données vides
    return { daily_analytics: [] };
  },

  getProductAnalytics: async (params?: any) => {
    // Endpoint non disponible - retourner des données vides
    return { product_analytics: [] };
  },

  getCategoryAnalytics: async (params?: any) => {
    // Endpoint non disponible - retourner des données vides
    return { category_analytics: [] };
  },

  getDashboardStats: async (params?: any) => {
    // Utiliser l'endpoint existant /reports/dashboard/
    const response = await apiClient.get('/reports/dashboard/', { params });
    return response.data;
  },
};

// API des utilisateurs (pour les admins)
export const usersAPI = {
  getAll: async (params?: any) => {
    const response = await apiClient.get('/auth/users/', { params });
    return response.data;
  },

  getById: async (id: string) => {
    const response = await apiClient.get(`/auth/users/${id}/`);
    return response.data;
  },

  create: async (data: any) => {
    const response = await apiClient.post('/auth/users/', data);
    return response.data;
  },

  update: async (id: string, data: any) => {
    const response = await apiClient.put(`/auth/users/${id}/`, data);
    return response.data;
  },

  delete: async (id: string) => {
    await apiClient.delete(`/auth/users/${id}/`);
  },

  getActivities: async (params?: any) => {
    const response = await apiClient.get('/auth/activities/', { params });
    return response.data;
  },
};

// API des paramètres système
export const settingsAPI = {
  getAll: async () => {
    const response = await apiClient.get('/settings/');
    return response.data;
  },

  update: async (data: any) => {
    const response = await apiClient.put('/settings/', data);
    return response.data;
  },

  getSystemInfo: async () => {
    const response = await apiClient.get('/settings/system-info/');
    return response.data;
  },

  backup: async () => {
    const response = await apiClient.post('/settings/backup/');
    return response.data;
  },

  restore: async (file: File) => {
    const formData = new FormData();
    formData.append('backup_file', file);
    const response = await apiClient.post('/settings/restore/', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  },
};

export { TokenManager };
export default apiClient;
