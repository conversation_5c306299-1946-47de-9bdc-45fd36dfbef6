import React from 'react';
import { Button } from '@/components/ui/button';
import { Download, FileText } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

interface InvoiceItem {
  id: string;
  product_name: string;
  quantity: number;
  unit_price: number;
  total_price: number;
}

interface InvoiceData {
  id: string;
  invoice_number: string;
  customer_name?: string;
  customer_email?: string;
  customer_phone?: string;
  customer_address?: string;
  table_number?: string;
  items: InvoiceItem[];
  subtotal: number;
  tax_amount: number;
  discount_amount: number;
  total_amount: number;
  payment_method: string;
  status: string;
  created_at: string;
  server: string;
  notes?: string;
}

interface InvoiceGeneratorProps {
  invoice: InvoiceData;
  onGenerated?: (blob: Blob) => void;
}

const InvoiceGenerator: React.FC<InvoiceGeneratorProps> = ({ 
  invoice, 
  onGenerated 
}) => {
  const { toast } = useToast();

  const generatePDF = async () => {
    try {
      // Dynamiquement importer jsPD<PERSON> pour éviter les erreurs de SSR
      const { default: jsPDF } = await import('jspdf');
      const pdf = new jsPDF();

      // Configuration de la page
      const pageWidth = pdf.internal.pageSize.getWidth();
      const pageHeight = pdf.internal.pageSize.getHeight();
      const margin = 20;
      const contentWidth = pageWidth - (margin * 2);
      let yPosition = margin;

      // Fonction pour ajouter du texte avec gestion de la pagination
      const addText = (text: string, x: number, y: number, options: any = {}) => {
        if (y > pageHeight - margin) {
          pdf.addPage();
          yPosition = margin;
        }
        pdf.text(text, x, y, options);
        return y + (options.fontSize || 12) + 2;
      };

      // En-tête
      pdf.setFontSize(24);
      pdf.setFont('helvetica', 'bold');
      yPosition = addText('BarStock Wise', pageWidth / 2, yPosition, { align: 'center' });

      pdf.setFontSize(12);
      pdf.setFont('helvetica', 'normal');
      yPosition = addText('Restaurant & Bar', pageWidth / 2, yPosition, { align: 'center' });
      yPosition = addText('Bujumbura, Burundi', pageWidth / 2, yPosition, { align: 'center' });
      yPosition = addText('Tél: +257 22 123 456 | Email: <EMAIL>', pageWidth / 2, yPosition, { align: 'center' });

      yPosition += 10;

      // Informations de facture
      pdf.setFontSize(14);
      pdf.setFont('helvetica', 'bold');
      yPosition = addText('FACTURE', margin, yPosition);

      yPosition += 5;

      // Détails de la facture
      pdf.setFontSize(10);
      pdf.setFont('helvetica', 'normal');

      const leftColumn = margin;
      const rightColumn = pageWidth / 2 + 10;

      // Colonne gauche - Informations client
      yPosition = addText('INFORMATIONS CLIENT:', leftColumn, yPosition);
      yPosition = addText(`Nom: ${invoice.customer_name || 'Client non spécifié'}`, leftColumn + 5, yPosition);
      if (invoice.customer_email) {
        yPosition = addText(`Email: ${invoice.customer_email}`, leftColumn + 5, yPosition);
      }
      if (invoice.customer_phone) {
        yPosition = addText(`Téléphone: ${invoice.customer_phone}`, leftColumn + 5, yPosition);
      }
      if (invoice.customer_address) {
        yPosition = addText(`Adresse: ${invoice.customer_address}`, leftColumn + 5, yPosition);
      }
      if (invoice.table_number) {
        yPosition = addText(`Table: ${invoice.table_number}`, leftColumn + 5, yPosition);
      }

      // Colonne droite - Informations facture
      const rightYStart = yPosition - 40; // Aligner avec la colonne gauche
      let rightY = rightYStart;
      rightY = addText('DÉTAILS FACTURE:', rightColumn, rightY);
      rightY = addText(`N° Facture: ${invoice.invoice_number}`, rightColumn + 5, rightY);
      rightY = addText(`Date: ${new Date(invoice.created_at).toLocaleDateString('fr-FR')}`, rightColumn + 5, rightY);
      rightY = addText(`Heure: ${new Date(invoice.created_at).toLocaleTimeString('fr-FR')}`, rightColumn + 5, rightY);
      rightY = addText(`Serveur: ${invoice.server}`, rightColumn + 5, rightY);
      rightY = addText(`Statut: ${invoice.status === 'paid' ? 'Payé' : 'En attente'}`, rightColumn + 5, rightY);

      yPosition = Math.max(yPosition, rightY) + 10;

      // Tableau des articles
      yPosition = addText('ARTICLES:', margin, yPosition);
      yPosition += 5;

      // En-tête du tableau
      pdf.setFont('helvetica', 'bold');
      const tableHeaders = ['Article', 'Qté', 'Prix unit.', 'Total'];
      const columnWidths = [80, 20, 30, 30];
      let currentX = margin;

      tableHeaders.forEach((header, index) => {
        pdf.text(header, currentX, yPosition);
        currentX += columnWidths[index];
      });

      yPosition += 5;

      // Ligne de séparation
      pdf.line(margin, yPosition, pageWidth - margin, yPosition);
      yPosition += 5;

      // Articles
      pdf.setFont('helvetica', 'normal');
      invoice.items.forEach((item) => {
        if (yPosition > pageHeight - 60) {
          pdf.addPage();
          yPosition = margin;
        }

        currentX = margin;
        pdf.text(item.product_name || item.product?.name || 'Article', currentX, yPosition);
        currentX += columnWidths[0];
        pdf.text(item.quantity.toString(), currentX, yPosition);
        currentX += columnWidths[1];
        pdf.text(`${(item.unit_price || 0).toLocaleString()} BIF`, currentX, yPosition);
        currentX += columnWidths[2];
        pdf.text(`${(item.total_price || item.subtotal || (item.quantity * item.unit_price) || 0).toLocaleString()} BIF`, currentX, yPosition);

        yPosition += 5;
      });

      yPosition += 5;

      // Ligne de séparation
      pdf.line(margin, yPosition, pageWidth - margin, yPosition);
      yPosition += 10;

      // Totaux
      pdf.setFont('helvetica', 'bold');
      const totalsX = pageWidth - margin - 60;

      // Calculer le sous-total à partir des items si pas disponible
      const subtotal = invoice.subtotal || invoice.items.reduce((sum, item) =>
        sum + (item.total_price || item.subtotal || (item.quantity * item.unit_price) || 0), 0
      );

      yPosition = addText(`Sous-total: ${subtotal.toLocaleString()} BIF`, totalsX, yPosition);

      if (invoice.tax_amount && invoice.tax_amount > 0) {
        yPosition = addText(`TVA: ${invoice.tax_amount.toLocaleString()} BIF`, totalsX, yPosition);
      }

      if (invoice.discount_amount && invoice.discount_amount > 0) {
        yPosition = addText(`Remise: -${invoice.discount_amount.toLocaleString()} BIF`, totalsX, yPosition);
      }

      pdf.setFontSize(12);
      const totalAmount = invoice.total_amount || subtotal;
      yPosition = addText(`TOTAL: ${totalAmount.toLocaleString()} BIF`, totalsX, yPosition);

      yPosition += 5;
      pdf.setFontSize(10);
      yPosition = addText(`Mode de paiement: ${invoice.payment_method || 'Non spécifié'}`, totalsX, yPosition);

      // Notes
      if (invoice.notes) {
        yPosition += 10;
        pdf.setFont('helvetica', 'bold');
        yPosition = addText('NOTES:', margin, yPosition);
        pdf.setFont('helvetica', 'normal');
        yPosition = addText(invoice.notes, margin + 5, yPosition);
      }

      // Pied de page
      yPosition = pageHeight - 30;
      pdf.setFontSize(8);
      pdf.setFont('helvetica', 'normal');
      pdf.text('Merci de votre visite !', pageWidth / 2, yPosition, { align: 'center' });
      yPosition += 5;
      pdf.text('BarStock Wise - Votre satisfaction est notre priorité', pageWidth / 2, yPosition, { align: 'center' });

      // Générer le blob
      const pdfBlob = pdf.output('blob');
      
      if (onGenerated) {
        onGenerated(pdfBlob);
      } else {
        // Téléchargement automatique
        const url = URL.createObjectURL(pdfBlob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `facture_${invoice.invoice_number}.pdf`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);
      }

      toast({
        title: "PDF généré",
        description: "La facture PDF a été générée avec succès.",
      });

    } catch (error) {
      console.error('Erreur lors de la génération PDF:', error);
      toast({
        title: "Erreur de génération",
        description: "Impossible de générer le PDF de la facture.",
        variant: "destructive",
      });
    }
  };

  return (
    <Button 
      variant="outline" 
      onClick={generatePDF}
      className="flex items-center gap-2"
    >
      <FileText className="w-4 h-4" />
      Générer PDF
    </Button>
  );
};

export default InvoiceGenerator; 