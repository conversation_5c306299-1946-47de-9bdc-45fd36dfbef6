import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useProducts, useInventory } from '@/hooks/useApi';

export const ApiDebugger = () => {
  const { data: products, isLoading: productsLoading, error: productsError } = useProducts();
  const { data: inventory, isLoading: inventoryLoading, error: inventoryError } = useInventory();

  return (
    <div className="space-y-4 p-4 bg-gray-50 rounded-lg">
      <h2 className="text-lg font-bold">🔍 API Debug Info</h2>
      
      {/* Products Debug */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            Products API
            <Badge variant={productsError ? "destructive" : productsLoading ? "secondary" : "default"}>
              {productsError ? "Error" : productsLoading ? "Loading" : "Loaded"}
            </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <p><strong>Type:</strong> {typeof products}</p>
            <p><strong>Is Array:</strong> {Array.isArray(products) ? "Yes" : "No"}</p>
            {products && (
              <>
                <p><strong>Keys:</strong> {typeof products === 'object' ? Object.keys(products).join(', ') : 'N/A'}</p>
                <p><strong>Length:</strong> {Array.isArray(products) ? products.length : 'N/A'}</p>
                {products.results && <p><strong>Results Length:</strong> {Array.isArray(products.results) ? products.results.length : 'N/A'}</p>}
              </>
            )}
            {productsError && (
              <div className="text-red-600">
                <p><strong>Error:</strong> {productsError.message}</p>
              </div>
            )}
            <details className="mt-2">
              <summary className="cursor-pointer font-medium">Raw Data</summary>
              <pre className="mt-2 p-2 bg-gray-100 rounded text-xs overflow-auto max-h-40">
                {JSON.stringify(products, null, 2)}
              </pre>
            </details>
          </div>
        </CardContent>
      </Card>

      {/* Inventory Debug */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            Inventory API
            <Badge variant={inventoryError ? "destructive" : inventoryLoading ? "secondary" : "default"}>
              {inventoryError ? "Error" : inventoryLoading ? "Loading" : "Loaded"}
            </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <p><strong>Type:</strong> {typeof inventory}</p>
            <p><strong>Is Array:</strong> {Array.isArray(inventory) ? "Yes" : "No"}</p>
            {inventory && (
              <>
                <p><strong>Keys:</strong> {typeof inventory === 'object' ? Object.keys(inventory).join(', ') : 'N/A'}</p>
                <p><strong>Length:</strong> {Array.isArray(inventory) ? inventory.length : 'N/A'}</p>
                {inventory.results && <p><strong>Results Length:</strong> {Array.isArray(inventory.results) ? inventory.results.length : 'N/A'}</p>}
              </>
            )}
            {inventoryError && (
              <div className="text-red-600">
                <p><strong>Error:</strong> {inventoryError.message}</p>
              </div>
            )}
            <details className="mt-2">
              <summary className="cursor-pointer font-medium">Raw Data</summary>
              <pre className="mt-2 p-2 bg-gray-100 rounded text-xs overflow-auto max-h-40">
                {JSON.stringify(inventory, null, 2)}
              </pre>
            </details>
          </div>
        </CardContent>
      </Card>

      {/* API Endpoints Info */}
      <Card>
        <CardHeader>
          <CardTitle>API Endpoints</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-1 text-sm">
            <p><strong>Base URL:</strong> {import.meta.env.VITE_API_URL || 'http://localhost:8000/api'}</p>
            <p><strong>Products:</strong> /products/</p>
            <p><strong>Inventory:</strong> /inventory/</p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
