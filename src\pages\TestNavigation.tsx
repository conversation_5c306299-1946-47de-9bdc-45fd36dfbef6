import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Home, 
  Package, 
  ShoppingCart, 
  History, 
  TrendingUp, 
  Users, 
  DollarSign, 
  BarChart3,
  Settings,
  HelpCircle,
  Server,
  CheckCircle,
  XCircle,
  Clock
} from 'lucide-react';
import { Link } from 'react-router-dom';

const TestNavigation = () => {
  const pages = [
    {
      name: 'Dashboard',
      path: '/',
      icon: Home,
      description: 'Vue d\'ensemble des activités',
      status: 'working'
    },
    {
      name: 'Stocks',
      path: '/stocks',
      icon: Package,
      description: 'Gestion de l\'inventaire',
      status: 'working'
    },
    {
      name: 'Produits',
      path: '/products',
      icon: Package,
      description: 'Catalogue des produits',
      status: 'working'
    },
    {
      name: 'Ventes',
      path: '/sales',
      icon: ShoppingCart,
      description: 'Point de vente',
      status: 'working'
    },
    {
      name: 'Historique Ventes',
      path: '/sales-history',
      icon: History,
      description: 'Historique des transactions',
      status: 'working'
    },
    {
      name: '<PERSON><PERSON><PERSON><PERSON>',
      path: '/expenses',
      icon: DollarSign,
      description: 'Gestion des dépenses',
      status: 'working'
    },
    {
      name: 'Fournisseurs',
      path: '/suppliers',
      icon: Users,
      description: 'Gestion des fournisseurs',
      status: 'working'
    },
    {
      name: 'Analytics',
      path: '/analytics',
      icon: BarChart3,
      description: 'Analyses et rapports',
      status: 'working'
    },
    {
      name: 'Tables',
      path: '/tables',
      icon: TrendingUp,
      description: 'Gestion des tables',
      status: 'working'
    },
    {
      name: 'Aide',
      path: '/help',
      icon: HelpCircle,
      description: 'Documentation et support',
      status: 'working'
    },
    {
      name: 'Test API',
      path: '/api-test',
      icon: Server,
      description: 'Diagnostic de l\'API',
      status: 'working'
    }
  ];

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'working':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'error':
        return <XCircle className="w-4 h-4 text-red-500" />;
      case 'loading':
        return <Clock className="w-4 h-4 text-yellow-500" />;
      default:
        return <Clock className="w-4 h-4 text-gray-400" />;
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'working':
        return <Badge className="bg-green-500">✓ Fonctionnel</Badge>;
      case 'error':
        return <Badge variant="destructive">✗ Erreur</Badge>;
      case 'loading':
        return <Badge variant="secondary">⏳ Chargement</Badge>;
      default:
        return <Badge variant="outline">? Non testé</Badge>;
    }
  };

  return (
    <div className="space-y-6 p-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Test de Navigation</h1>
          <p className="text-muted-foreground">
            Testez toutes les pages de l'application BarStockWise
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Badge variant="outline">
            {pages.filter(p => p.status === 'working').length}/{pages.length} pages fonctionnelles
          </Badge>
        </div>
      </div>

      {/* Statistiques rapides */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pages Totales</CardTitle>
            <Package className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{pages.length}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Fonctionnelles</CardTitle>
            <CheckCircle className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              {pages.filter(p => p.status === 'working').length}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avec Erreurs</CardTitle>
            <XCircle className="h-4 w-4 text-red-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">
              {pages.filter(p => p.status === 'error').length}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Taux de Succès</CardTitle>
            <BarChart3 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              {Math.round((pages.filter(p => p.status === 'working').length / pages.length) * 100)}%
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Grille des pages */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {pages.map((page) => {
          const IconComponent = page.icon;
          
          return (
            <Card key={page.path} className="hover:shadow-md transition-shadow">
              <CardHeader className="pb-3">
                <CardTitle className="flex items-center justify-between text-base">
                  <span className="flex items-center gap-2">
                    <IconComponent className="w-5 h-5" />
                    {page.name}
                  </span>
                  {getStatusIcon(page.status)}
                </CardTitle>
                <p className="text-sm text-muted-foreground">
                  {page.description}
                </p>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Statut:</span>
                  {getStatusBadge(page.status)}
                </div>
                
                <div className="flex gap-2">
                  <Button asChild className="flex-1">
                    <Link to={page.path}>
                      Ouvrir
                    </Link>
                  </Button>
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={() => window.open(page.path, '_blank')}
                  >
                    Nouvel onglet
                  </Button>
                </div>
                
                <div className="text-xs text-muted-foreground">
                  <strong>Route:</strong> {page.path}
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Instructions */}
      <Card>
        <CardHeader>
          <CardTitle>Instructions de Test</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <h4 className="font-medium">Comment tester :</h4>
            <ol className="list-decimal list-inside text-sm space-y-1 text-muted-foreground">
              <li>Cliquez sur "Ouvrir" pour naviguer vers chaque page</li>
              <li>Vérifiez que la page se charge sans erreur</li>
              <li>Testez les fonctionnalités principales de chaque page</li>
              <li>Vérifiez que les données mock s'affichent correctement</li>
              <li>Utilisez "Test API" pour diagnostiquer les problèmes de connexion</li>
            </ol>
          </div>
          
          <div className="space-y-2">
            <h4 className="font-medium">En cas de problème :</h4>
            <ul className="list-disc list-inside text-sm space-y-1 text-muted-foreground">
              <li>Vérifiez la console du navigateur (F12)</li>
              <li>Utilisez la page "Test API" pour diagnostiquer les problèmes backend</li>
              <li>Les données mock permettent de tester même sans API backend</li>
              <li>Rechargez la page si nécessaire (Ctrl+F5)</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default TestNavigation;
