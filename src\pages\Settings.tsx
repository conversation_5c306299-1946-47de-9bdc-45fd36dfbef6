import React, { useState, useEffect } from 'react';
import { SettingsService } from '@/lib/settingsService';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { 
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Settings as SettingsIcon,
  Bell,
  Database,
  Printer,
  Shield,
  Download,
  Upload,
  AlertTriangle,
  Info
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/contexts/AuthContextBackend';
import { useSettingsService } from '@/hooks/useSettingsService';

const Settings = () => {
  const { user: currentUser } = useAuth();
  const { toast } = useToast();
  
  // Vérifier si l'utilisateur actuel est admin ou gérant
  const hasSettingsAccess = currentUser?.role === 'admin' || currentUser?.role === 'gerant';

  if (!hasSettingsAccess) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <Card className="w-full max-w-md">
          <CardContent className="pt-6 text-center">
            <Shield className="w-16 h-16 mx-auto mb-4 text-muted-foreground" />
            <h2 className="text-xl font-semibold mb-2">Accès Restreint</h2>
            <p className="text-muted-foreground">
              Seuls les administrateurs et gérants peuvent accéder aux paramètres système.
            </p>
            <div className="mt-4 text-sm text-muted-foreground">
              <p>Votre rôle actuel : <Badge variant="outline">{currentUser?.role || 'Non défini'}</Badge></p>
              <p>Rôles autorisés : <Badge variant="outline">admin</Badge> <Badge variant="outline">gerant</Badge></p>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Nouveau service de paramètres avec fallback localStorage
  const {
    settings: systemSettings,
    isLoading: settingsLoading,
    isAPIAvailable,
    error: settingsError,
    updateSettings,
    resetToDefaults: resetSettingsToDefaults,
    syncToServer
  } = useSettingsService();

  // Informations système (sera récupéré via le service)
  const [systemInfo, setSystemInfo] = useState<any>(null);

  // Charger les informations système
  useEffect(() => {
    const loadSystemInfo = async () => {
      try {
        const info = await SettingsService.getSystemInfo();
        setSystemInfo(info);
      } catch (error) {
        console.error('Erreur chargement info système:', error);
      }
    };
    loadSystemInfo();
  }, []);

  const [settings, setSettings] = useState({
    // Alertes de stock
    lowStockThreshold: 10,
    criticalStockThreshold: 0,
    stockAlerts: true,
    emailAlerts: false,

    // Impression
    printerName: 'Imprimante par défaut',
    autoprint: true,
    receiptFooter: 'Merci de votre visite !',

    // Général
    taxRate: 0,
    currency: 'BIF',
    businessName: 'BarStock Wise',
    businessAddress: 'Bujumbura, Burundi',

    // Sécurité
    sessionTimeout: 30,
    passwordComplexity: true,
    twoFactorAuth: false
  });

  // Note: Les paramètres système et locaux ont des structures différentes
  // Pour l'instant, nous gardons les paramètres locaux séparés

  const updateSetting = async (key: string, value: any) => {
    try {
      const newSettings = { ...settings, [key]: value };
      setSettings(newSettings);

      // Si nous avons des paramètres système, les mettre à jour
      if (systemSettings && updateSettings) {
        const updatedSystemSettings = { ...systemSettings };
        // Mapper les clés locales vers la structure SystemSettings
        // Pour l'instant, on garde les paramètres locaux
        await updateSettings(updatedSystemSettings);
      }
    } catch (error) {
      console.error('Erreur lors de la mise à jour des paramètres:', error);
      // Revenir à l'ancienne valeur en cas d'erreur
      setSettings(settings);
    }
  };

  const exportDatabase = () => {
    toast({
      title: "Export en cours",
      description: "La base de données est en cours d'export...",
    });
  };

  const importDatabase = () => {
    toast({
      title: "Import démarré",
      description: "Sélectionnez le fichier à importer.",
    });
  };

  const resetToDefaults = async () => {
    if (resetSettingsToDefaults) {
      await resetSettingsToDefaults();
    }
  };

  const testPrinter = () => {
    toast({
      title: "Test d'impression",
      description: "Une page de test a été envoyée à l'imprimante.",
    });
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Paramètres Système</h1>
          <p className="text-muted-foreground">
            Configurez les paramètres de l'application et les préférences
          </p>
        </div>
        
        <Button variant="outline" onClick={resetToDefaults}>
          Réinitialiser
        </Button>
      </div>

      {/* Message informatif sur l'état de synchronisation */}
      <Card className={`border-2 ${isAPIAvailable ? 'border-green-200 bg-green-50' : 'border-yellow-200 bg-yellow-50'}`}>
        <CardContent className="pt-4">
          <div className={`flex items-center gap-2 ${isAPIAvailable ? 'text-green-700' : 'text-yellow-700'}`}>
            <Info className="h-4 w-4" />
            <span className="text-sm font-medium">
              {isAPIAvailable ? 'Synchronisation Serveur Active' : 'Mode Local avec Fallback'}
            </span>
          </div>
          <p className={`text-xs mt-1 ${isAPIAvailable ? 'text-green-600' : 'text-yellow-600'}`}>
            {isAPIAvailable
              ? 'Les paramètres sont synchronisés avec le serveur en temps réel.'
              : 'Les paramètres sont sauvegardés localement. Synchronisation serveur indisponible.'
            }
          </p>
          {!isAPIAvailable && (
            <Button
              variant="outline"
              size="sm"
              onClick={syncToServer}
              disabled={settingsLoading}
              className="mt-2"
            >
              Tenter la Synchronisation
            </Button>
          )}
        </CardContent>
      </Card>

      <div className="grid gap-6 lg:grid-cols-2">
        {/* Stock Alerts */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Bell className="w-5 h-5" />
              Alertes de Stock
            </CardTitle>
            <CardDescription>
              Configurez les seuils d'alerte pour le stock
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="lowStock">Seuil stock bas</Label>
              <Input
                id="lowStock"
                type="number"
                value={settings.lowStockThreshold}
                onChange={(e) => updateSetting('lowStockThreshold', Number(e.target.value))}
              />
              <p className="text-xs text-muted-foreground">
                Alerter quand le stock descend sous cette valeur
              </p>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="criticalStock">Seuil stock critique</Label>
              <Input
                id="criticalStock"
                type="number"
                value={settings.criticalStockThreshold}
                onChange={(e) => updateSetting('criticalStockThreshold', Number(e.target.value))}
              />
              <p className="text-xs text-muted-foreground">
                Alerter en urgence (rupture de stock)
              </p>
            </div>
            
            <div className="flex items-center justify-between">
              <Label htmlFor="stockAlerts">Alertes visuelles</Label>
              <Switch
                id="stockAlerts"
                checked={settings.stockAlerts}
                onCheckedChange={(checked) => updateSetting('stockAlerts', checked)}
              />
            </div>
            
            <div className="flex items-center justify-between">
              <Label htmlFor="emailAlerts">Alertes par email</Label>
              <Switch
                id="emailAlerts"
                checked={settings.emailAlerts}
                onCheckedChange={(checked) => updateSetting('emailAlerts', checked)}
              />
            </div>
          </CardContent>
        </Card>

        {/* Printer Settings */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Printer className="w-5 h-5" />
              Configuration Impression
            </CardTitle>
            <CardDescription>
              Paramètres pour les factures et reçus
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="printer">Imprimante</Label>
              <Select 
                value={settings.printerName} 
                onValueChange={(value) => updateSetting('printerName', value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Imprimante par défaut">Imprimante par défaut</SelectItem>
                  <SelectItem value="HP LaserJet Pro">HP LaserJet Pro</SelectItem>
                  <SelectItem value="Canon PIXMA">Canon PIXMA</SelectItem>
                  <SelectItem value="Epson EcoTank">Epson EcoTank</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="flex items-center justify-between">
              <Label htmlFor="autoprint">Impression automatique</Label>
              <Switch
                id="autoprint"
                checked={settings.autoprint}
                onCheckedChange={(checked) => updateSetting('autoprint', checked)}
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="footer">Pied de page facture</Label>
              <Input
                id="footer"
                value={settings.receiptFooter}
                onChange={(e) => updateSetting('receiptFooter', e.target.value)}
                placeholder="Message de fin de facture"
              />
            </div>
            
            <Button onClick={testPrinter} variant="outline" className="w-full">
              <Printer className="w-4 h-4 mr-2" />
              Tester l'imprimante
            </Button>
          </CardContent>
        </Card>

        {/* Business Settings */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <SettingsIcon className="w-5 h-5" />
              Informations de l'Établissement
            </CardTitle>
            <CardDescription>
              Données de votre bar-restaurant
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="businessName">Nom de l'établissement</Label>
              <Input
                id="businessName"
                value={settings.businessName}
                onChange={(e) => updateSetting('businessName', e.target.value)}
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="businessAddress">Adresse</Label>
              <Input
                id="businessAddress"
                value={settings.businessAddress}
                onChange={(e) => updateSetting('businessAddress', e.target.value)}
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="currency">Devise</Label>
              <Select 
                value={settings.currency} 
                onValueChange={(value) => updateSetting('currency', value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="BIF">BIF (Franc Burundais)</SelectItem>
                  <SelectItem value="USD">USD (Dollar)</SelectItem>
                  <SelectItem value="EUR">EUR (Euro)</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="taxRate">Taux de taxe (%)</Label>
              <Input
                id="taxRate"
                type="number"
                step="0.1"
                value={settings.taxRate}
                onChange={(e) => updateSetting('taxRate', Number(e.target.value))}
              />
            </div>
          </CardContent>
        </Card>

        {/* Security Settings */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Shield className="w-5 h-5" />
              Sécurité
            </CardTitle>
            <CardDescription>
              Paramètres de sécurité et d'accès
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="sessionTimeout">Timeout session (minutes)</Label>
              <Input
                id="sessionTimeout"
                type="number"
                value={settings.sessionTimeout}
                onChange={(e) => updateSetting('sessionTimeout', Number(e.target.value))}
              />
              <p className="text-xs text-muted-foreground">
                Déconnexion automatique après inactivité
              </p>
            </div>
            
            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="passwordComplexity">Mots de passe complexes</Label>
                <p className="text-xs text-muted-foreground">
                  Exiger majuscules, chiffres et symboles
                </p>
              </div>
              <Switch
                id="passwordComplexity"
                checked={settings.passwordComplexity}
                onCheckedChange={(checked) => updateSetting('passwordComplexity', checked)}
              />
            </div>
            
            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="twoFactor">Authentification 2FA</Label>
                <p className="text-xs text-muted-foreground">
                  Double authentification (en développement)
                </p>
              </div>
              <Switch
                id="twoFactor"
                checked={settings.twoFactorAuth}
                onCheckedChange={(checked) => updateSetting('twoFactorAuth', checked)}
                disabled
              />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Database Management */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Database className="w-5 h-5" />
            Gestion de la Base de Données
          </CardTitle>
          <CardDescription>
            Sauvegarde et restauration des données
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-3">
            <Card className="p-4">
              <div className="text-center space-y-2">
                <Download className="w-8 h-8 mx-auto text-muted-foreground" />
                <h3 className="font-medium">Export des données</h3>
                <p className="text-xs text-muted-foreground">
                  Exporter toutes les données au format CSV/Excel
                </p>
                <Button onClick={exportDatabase} variant="outline" size="sm">
                  Exporter
                </Button>
              </div>
            </Card>
            
            <Card className="p-4">
              <div className="text-center space-y-2">
                <Upload className="w-8 h-8 mx-auto text-muted-foreground" />
                <h3 className="font-medium">Import des données</h3>
                <p className="text-xs text-muted-foreground">
                  Importer des données depuis un fichier
                </p>
                <Button onClick={importDatabase} variant="outline" size="sm">
                  Importer
                </Button>
              </div>
            </Card>
            
            <Card className="p-4 border-destructive/20">
              <div className="text-center space-y-2">
                <AlertTriangle className="w-8 h-8 mx-auto text-destructive" />
                <h3 className="font-medium">Réinitialisation</h3>
                <p className="text-xs text-muted-foreground">
                  Remettre à zéro toutes les données
                </p>
                <Button variant="destructive" size="sm" disabled>
                  Réinitialiser
                </Button>
              </div>
            </Card>
          </div>
        </CardContent>
      </Card>

      {/* System Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Info className="w-5 h-5" />
            Informations Système
          </CardTitle>
          <CardDescription>
            État actuel du système BarStock Wise
          </CardDescription>
        </CardHeader>
        <CardContent>
          {systemInfo ? (
            <div className="grid gap-4 md:grid-cols-2">
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-sm font-medium">Version:</span>
                  <Badge variant="outline">{systemInfo.version}</Badge>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm font-medium">Base de données:</span>
                  <span className="text-sm text-muted-foreground">{systemInfo.database}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm font-medium">Serveur:</span>
                  <span className="text-sm text-muted-foreground">{systemInfo.server}</span>
                </div>
              </div>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-sm font-medium">Temps de fonctionnement:</span>
                  <span className="text-sm text-muted-foreground">{systemInfo.uptime}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm font-medium">Dernière sauvegarde:</span>
                  <span className="text-sm text-muted-foreground">{systemInfo.last_backup}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm font-medium">Statut:</span>
                  <Badge variant="default" className="bg-green-500">Opérationnel</Badge>
                </div>
              </div>
            </div>
          ) : (
            <div className="flex items-center justify-center py-8">
              <div className="text-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto mb-2"></div>
                <p className="text-sm text-muted-foreground">Chargement des informations système...</p>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Current Settings Summary */}
      <Card>
        <CardHeader>
          <CardTitle>Résumé des Paramètres Actuels</CardTitle>
          <CardDescription>
            Aperçu de la configuration actuelle
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Paramètre</TableHead>
                <TableHead>Valeur</TableHead>
                <TableHead>Statut</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              <TableRow>
                <TableCell>Seuil stock bas</TableCell>
                <TableCell>{settings.lowStockThreshold} unités</TableCell>
                <TableCell><Badge variant="default">Actif</Badge></TableCell>
              </TableRow>
              <TableRow>
                <TableCell>Alertes activées</TableCell>
                <TableCell>{settings.stockAlerts ? 'Oui' : 'Non'}</TableCell>
                <TableCell>
                  <Badge variant={settings.stockAlerts ? "default" : "secondary"}>
                    {settings.stockAlerts ? 'Actif' : 'Inactif'}
                  </Badge>
                </TableCell>
              </TableRow>
              <TableRow>
                <TableCell>Impression automatique</TableCell>
                <TableCell>{settings.autoprint ? 'Activée' : 'Désactivée'}</TableCell>
                <TableCell>
                  <Badge variant={settings.autoprint ? "default" : "secondary"}>
                    {settings.autoprint ? 'Actif' : 'Inactif'}
                  </Badge>
                </TableCell>
              </TableRow>
              <TableRow>
                <TableCell>Timeout session</TableCell>
                <TableCell>{settings.sessionTimeout} minutes</TableCell>
                <TableCell><Badge variant="default">Configuré</Badge></TableCell>
              </TableRow>
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
};

export default Settings;