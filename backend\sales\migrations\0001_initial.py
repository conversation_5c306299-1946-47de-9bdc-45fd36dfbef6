# Generated by Django 5.2.4 on 2025-07-30 14:12

import django.core.validators
import django.db.models.deletion
from decimal import Decimal
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('products', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Table',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('number', models.CharField(max_length=10, unique=True, verbose_name='Numéro de table')),
                ('capacity', models.PositiveIntegerField(default=4, verbose_name='Capacité (personnes)')),
                ('status', models.CharField(choices=[('available', 'Disponible'), ('occupied', 'Occupée'), ('reserved', 'Réservée'), ('cleaning', 'Nettoyage')], default='available', max_length=20, verbose_name='Statut')),
                ('location', models.CharField(blank=True, max_length=100, null=True, verbose_name='Emplacement')),
                ('is_active', models.BooleanField(default=True, verbose_name='Table active')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Date de création')),
            ],
            options={
                'verbose_name': 'Table',
                'verbose_name_plural': 'Tables',
                'ordering': ['number'],
            },
        ),
        migrations.CreateModel(
            name='Sale',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('reference', models.CharField(max_length=100, unique=True, verbose_name='Référence de vente')),
                ('customer_name', models.CharField(blank=True, max_length=200, null=True, verbose_name='Nom du client')),
                ('status', models.CharField(choices=[('pending', 'En attente'), ('preparing', 'En préparation'), ('ready', 'Prêt'), ('served', 'Servi'), ('paid', 'Payé'), ('cancelled', 'Annulé')], default='pending', max_length=20, verbose_name='Statut')),
                ('payment_method', models.CharField(blank=True, choices=[('cash', 'Espèces'), ('card', 'Carte'), ('mobile', 'Mobile Money'), ('credit', 'Crédit')], max_length=20, null=True, verbose_name='Mode de paiement')),
                ('subtotal', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=12, validators=[django.core.validators.MinValueValidator(Decimal('0.00'))], verbose_name='Sous-total (BIF)')),
                ('tax_amount', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=10, validators=[django.core.validators.MinValueValidator(Decimal('0.00'))], verbose_name='Montant TVA (BIF)')),
                ('discount_amount', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=10, validators=[django.core.validators.MinValueValidator(Decimal('0.00'))], verbose_name='Remise (BIF)')),
                ('total_amount', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=12, validators=[django.core.validators.MinValueValidator(Decimal('0.00'))], verbose_name='Montant total (BIF)')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='Notes')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Date de création')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Date de modification')),
                ('paid_at', models.DateTimeField(blank=True, null=True, verbose_name='Date de paiement')),
                ('server', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='sales', to=settings.AUTH_USER_MODEL, verbose_name='Serveur')),
                ('table', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='sales', to='sales.table', verbose_name='Table')),
            ],
            options={
                'verbose_name': 'Vente',
                'verbose_name_plural': 'Ventes',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='SaleItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('quantity', models.PositiveIntegerField(verbose_name='Quantité')),
                ('unit_price', models.DecimalField(decimal_places=2, max_digits=10, validators=[django.core.validators.MinValueValidator(Decimal('0.00'))], verbose_name='Prix unitaire (BIF)')),
                ('total_price', models.DecimalField(decimal_places=2, max_digits=12, validators=[django.core.validators.MinValueValidator(Decimal('0.00'))], verbose_name='Prix total (BIF)')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='Notes')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Date de création')),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='products.product', verbose_name='Produit')),
                ('sale', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='items', to='sales.sale', verbose_name='Vente')),
            ],
            options={
                'verbose_name': 'Article de vente',
                'verbose_name_plural': 'Articles de vente',
                'unique_together': {('sale', 'product')},
            },
        ),
    ]
