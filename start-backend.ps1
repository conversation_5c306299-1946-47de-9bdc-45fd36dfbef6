# Script PowerShell pour démarrer le backend Django
Write-Host "🚀 Démarrage du backend BarStockWise..." -ForegroundColor Green

# Vérifier si Python est installé
try {
    $pythonVersion = python --version 2>&1
    Write-Host "✅ Python détecté: $pythonVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ Python n'est pas installé ou non accessible" -ForegroundColor Red
    exit 1
}

# Aller dans le dossier backend
Set-Location backend

# Vérifier si l'environnement virtuel existe
if (Test-Path "venv") {
    Write-Host "✅ Environnement virtuel détecté" -ForegroundColor Green
} else {
    Write-Host "📦 Création de l'environnement virtuel..." -ForegroundColor Yellow
    python -m venv venv
}

# Activer l'environnement virtuel
Write-Host "🔧 Activation de l'environnement virtuel..." -ForegroundColor Yellow
& "venv\Scripts\Activate.ps1"

# Installer les dépendances
Write-Host "📦 Installation des dépendances..." -ForegroundColor Yellow
pip install -r requirements.txt

# Appliquer les migrations
Write-Host "🗄️ Application des migrations..." -ForegroundColor Yellow
python manage.py migrate

# Créer des données de test si nécessaire
if (Test-Path "create_sample_data.py") {
    Write-Host "📊 Création des données de test..." -ForegroundColor Yellow
    python create_sample_data.py
}

# Créer un superutilisateur si nécessaire
if (Test-Path "create_admin.py") {
    Write-Host "👤 Création de l'administrateur..." -ForegroundColor Yellow
    python create_admin.py
}

# Démarrer le serveur
Write-Host "🌐 Démarrage du serveur Django sur http://localhost:8000" -ForegroundColor Green
Write-Host "📡 WebSocket disponible sur ws://localhost:8000/ws" -ForegroundColor Green
Write-Host "🛑 Appuyez sur Ctrl+C pour arrêter le serveur" -ForegroundColor Yellow

python manage.py runserver
