import React from 'react';
import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useNotifications } from '@/lib/notificationService';
import { useToast } from '@/hooks/use-toast';

const NotificationDebug = () => {
  const notifications = useNotifications();
  const { toast } = useToast();

  const debugInfo = () => {
    console.log('🔍 Debug Notifications Service:');
    console.log('- Service:', notifications);
    console.log('- Status:', notifications.status);
    console.log('- requestPermission:', typeof notifications.requestPermission);
    console.log('- sendNotification:', typeof notifications.sendNotification);
    console.log('- notifyLowStock:', typeof notifications.notifyLowStock);
    
    toast({
      title: "Debug Info",
      description: "Vérifiez la console pour les détails",
    });
  };

  const testBasicPermission = async () => {
    try {
      if ('Notification' in window) {
        const permission = await Notification.requestPermission();
        toast({
          title: "Permission native",
          description: `Résultat: ${permission}`,
        });
      } else {
        toast({
          title: "Non supporté",
          description: "Les notifications ne sont pas supportées",
          variant: "destructive",
        });
      }
    } catch (error) {
      toast({
        title: "Erreur native",
        description: `${error}`,
        variant: "destructive",
      });
    }
  };

  const testServicePermission = async () => {
    try {
      if (notifications.requestPermission) {
        const granted = await notifications.requestPermission();
        toast({
          title: "Permission service",
          description: `Résultat: ${granted}`,
        });
      } else {
        toast({
          title: "Méthode manquante",
          description: "requestPermission n'est pas disponible",
          variant: "destructive",
        });
      }
    } catch (error) {
      toast({
        title: "Erreur service",
        description: `${error}`,
        variant: "destructive",
      });
    }
  };

  const testNotification = async () => {
    try {
      const success = await notifications.sendNotification({
        title: 'Test Debug',
        body: 'Notification de test depuis le composant debug',
        icon: '/icon-192x192.png',
      });

      toast({
        title: success ? "Test réussi" : "Test échoué",
        description: `Résultat: ${success}`,
        variant: success ? "default" : "destructive",
      });
    } catch (error) {
      toast({
        title: "Erreur test",
        description: `${error}`,
        variant: "destructive",
      });
    }
  };

  return (
    <Card className="w-full max-w-md">
      <CardHeader>
        <CardTitle>🔧 Debug Notifications</CardTitle>
      </CardHeader>
      <CardContent className="space-y-3">
        <Button onClick={debugInfo} variant="outline" className="w-full">
          📊 Info Debug
        </Button>
        
        <Button onClick={testBasicPermission} variant="outline" className="w-full">
          🔐 Test Permission Native
        </Button>
        
        <Button onClick={testServicePermission} variant="outline" className="w-full">
          🔧 Test Permission Service
        </Button>
        
        <Button onClick={testNotification} variant="outline" className="w-full">
          🔔 Test Notification
        </Button>

        <div className="text-xs text-muted-foreground mt-4">
          <p><strong>Status:</strong> {notifications.status.permission}</p>
          <p><strong>Service Worker:</strong> {notifications.status.serviceWorkerAvailable ? 'Oui' : 'Non'}</p>
          <p><strong>Support:</strong> {notifications.status.browserSupport ? 'Oui' : 'Non'}</p>
        </div>
      </CardContent>
    </Card>
  );
};

export default NotificationDebug;
