import { useEffect, useRef, useState, useCallback } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';

interface WebSocketMessage {
  type: string;
  [key: string]: any;
}

interface UseWebSocketOptions {
  url: string;
  onMessage?: (message: WebSocketMessage) => void;
  onConnect?: () => void;
  onDisconnect?: () => void;
  onError?: (error: Event) => void;
  reconnectInterval?: number;
  maxReconnectAttempts?: number;
}

export function useWebSocket({
  url,
  onMessage,
  onConnect,
  onDisconnect,
  onError,
  reconnectInterval = 3000,
  maxReconnectAttempts = 5,
}: UseWebSocketOptions) {
  const [isConnected, setIsConnected] = useState(false);
  const [reconnectAttempts, setReconnectAttempts] = useState(0);
  const wsRef = useRef<WebSocket | null>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  const connect = useCallback(() => {
    try {
      // Ajouter le token d'authentification à l'URL
      const token = localStorage.getItem('access_token');
      const wsUrl = token ? `${url}?token=${token}` : url;
      
      wsRef.current = new WebSocket(wsUrl);

      wsRef.current.onopen = () => {
        console.log('WebSocket connecté:', url);
        setIsConnected(true);
        setReconnectAttempts(0);
        onConnect?.();
      };

      wsRef.current.onmessage = (event) => {
        try {
          const message: WebSocketMessage = JSON.parse(event.data);
          onMessage?.(message);
        } catch (error) {
          console.error('Erreur parsing message WebSocket:', error);
        }
      };

      wsRef.current.onclose = (event) => {
        console.log('WebSocket fermé:', event.code, event.reason);
        setIsConnected(false);
        onDisconnect?.();

        // Tentative de reconnexion si pas fermé intentionnellement
        if (event.code !== 1000 && reconnectAttempts < maxReconnectAttempts) {
          reconnectTimeoutRef.current = setTimeout(() => {
            setReconnectAttempts(prev => prev + 1);
            connect();
          }, reconnectInterval);
        }
      };

      wsRef.current.onerror = (error) => {
        console.error('Erreur WebSocket:', error);
        onError?.(error);
      };

    } catch (error) {
      console.error('Erreur connexion WebSocket:', error);
    }
  }, [url, onMessage, onConnect, onDisconnect, onError, reconnectAttempts, maxReconnectAttempts, reconnectInterval]);

  const disconnect = useCallback(() => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
    }
    
    if (wsRef.current) {
      wsRef.current.close(1000, 'Déconnexion intentionnelle');
      wsRef.current = null;
    }
    
    setIsConnected(false);
    setReconnectAttempts(0);
  }, []);

  const sendMessage = useCallback((message: any) => {
    if (wsRef.current && wsRef.current.readyState === WebSocket.OPEN) {
      wsRef.current.send(JSON.stringify(message));
    } else {
      console.warn('WebSocket non connecté, impossible d\'envoyer le message');
    }
  }, []);

  useEffect(() => {
    connect();
    return disconnect;
  }, [connect, disconnect]);

  return {
    isConnected,
    sendMessage,
    disconnect,
    reconnect: connect,
    reconnectAttempts,
  };
}

// Hook spécialisé pour les tables
export function useTablesWebSocket() {
  const queryClient = useQueryClient();
  const [lastUpdate, setLastUpdate] = useState<Date | null>(null);

  const handleMessage = useCallback((message: WebSocketMessage) => {
    console.log('Message WebSocket tables:', message);
    setLastUpdate(new Date());

    switch (message.type) {
      case 'tables_initial':
      case 'tables_update':
        // Mettre à jour le cache des tables
        queryClient.setQueryData(['tables'], message.data);
        break;

      case 'table_status_changed':
        // Mettre à jour une table spécifique
        queryClient.setQueryData(['tables'], (oldData: any) => {
          if (!oldData) return oldData;
          return oldData.map((table: any) => 
            table.id === message.table_id 
              ? { ...table, status: message.status, ...message.data }
              : table
          );
        });
        
        // Invalider le résumé des statuts
        queryClient.invalidateQueries({ queryKey: ['tables', 'status-summary'] });
        break;

      case 'table_occupied':
        queryClient.setQueryData(['tables'], (oldData: any) => {
          if (!oldData) return oldData;
          return oldData.map((table: any) => 
            table.id === message.table_id 
              ? { 
                  ...table, 
                  status: 'occupied',
                  is_occupied: true,
                  occupied_since: message.occupied_since,
                  ...message.data 
                }
              : table
          );
        });
        
        toast.success(`Table ${message.table_id} occupée${message.customer_name ? ` par ${message.customer_name}` : ''}`);
        break;

      case 'table_freed':
        queryClient.setQueryData(['tables'], (oldData: any) => {
          if (!oldData) return oldData;
          return oldData.map((table: any) => 
            table.id === message.table_id 
              ? { 
                  ...table, 
                  status: 'available',
                  is_occupied: false,
                  occupied_since: null,
                  occupation_duration: 0,
                  ...message.data 
                }
              : table
          );
        });
        
        toast.success(`Table ${message.table_id} libérée`);
        break;

      case 'tables_summary_update':
        queryClient.setQueryData(['tables', 'status-summary'], message.summary);
        break;

      default:
        console.log('Type de message non géré:', message.type);
    }
  }, [queryClient]);

  const handleConnect = useCallback(() => {
    console.log('WebSocket tables connecté');
    toast.success('Connexion temps réel établie');
  }, []);

  const handleDisconnect = useCallback(() => {
    console.log('WebSocket tables déconnecté');
    toast.warning('Connexion temps réel perdue');
  }, []);

  const handleError = useCallback((error: Event) => {
    console.error('Erreur WebSocket tables:', error);
    toast.error('Erreur de connexion temps réel');
  }, []);

  const wsUrl = process.env.NEXT_PUBLIC_WS_URL || 'ws://localhost:8000/ws/tables/';

  const { isConnected, sendMessage, disconnect, reconnect, reconnectAttempts } = useWebSocket({
    url: wsUrl,
    onMessage: handleMessage,
    onConnect: handleConnect,
    onDisconnect: handleDisconnect,
    onError: handleError,
  });

  const requestTablesUpdate = useCallback(() => {
    sendMessage({ type: 'request_tables_update' });
  }, [sendMessage]);

  return {
    isConnected,
    lastUpdate,
    requestTablesUpdate,
    disconnect,
    reconnect,
    reconnectAttempts,
  };
}

// Hook spécialisé pour les réservations
export function useReservationsWebSocket() {
  const queryClient = useQueryClient();

  const handleMessage = useCallback((message: WebSocketMessage) => {
    console.log('Message WebSocket réservations:', message);

    switch (message.type) {
      case 'reservations_initial':
      case 'reservations_update':
        queryClient.setQueryData(['reservations', 'today'], message.data);
        break;

      case 'reservation_created':
        queryClient.invalidateQueries({ queryKey: ['reservations'] });
        toast.success('Nouvelle réservation créée');
        break;

      case 'reservation_updated':
        queryClient.invalidateQueries({ queryKey: ['reservations'] });
        break;

      case 'reservation_reminder':
        toast.info(
          `Rappel: ${message.customer_name} - Table ${message.table_number} dans ${message.time_until}`,
          { duration: 10000 }
        );
        break;

      default:
        console.log('Type de message réservation non géré:', message.type);
    }
  }, [queryClient]);

  const wsUrl = process.env.NEXT_PUBLIC_WS_URL || 'ws://localhost:8000/ws/reservations/';

  const { isConnected, sendMessage } = useWebSocket({
    url: wsUrl,
    onMessage: handleMessage,
  });

  const requestReservationsUpdate = useCallback(() => {
    sendMessage({ type: 'request_reservations_update' });
  }, [sendMessage]);

  return {
    isConnected,
    requestReservationsUpdate,
  };
}
