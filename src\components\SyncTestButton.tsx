import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { useStockSync } from '@/hooks/useStockSync';
import { RefreshCw } from 'lucide-react';

const SyncTestButton = () => {
  const { syncToInventory, isLoading, syncState } = useStockSync();

  const handleTestSync = () => {
    console.log('🧪 Test de synchronisation...');
    syncToInventory();
  };

  return (
    <div className="flex items-center gap-2">
      <Button 
        onClick={handleTestSync} 
        disabled={isLoading}
        variant="outline"
        size="sm"
      >
        {isLoading ? (
          <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
        ) : (
          <RefreshCw className="w-4 h-4 mr-2" />
        )}
        Test Sync
      </Button>
      
      {syncState.lastSync && (
        <span className="text-xs text-muted-foreground">
          Dernière sync: {syncState.lastSync.toLocaleTimeString()}
        </span>
      )}
      
      {syncState.error && (
        <span className="text-xs text-red-500">
          Erreur: {syncState.error}
        </span>
      )}
    </div>
  );
};

export default SyncTestButton;
