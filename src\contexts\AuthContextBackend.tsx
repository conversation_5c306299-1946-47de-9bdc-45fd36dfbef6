import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { authAPI, User, LoginCredentials, TokenManager } from '@/lib/api';
import { useToast } from '@/hooks/use-toast';

interface AuthContextType {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (username: string, password: string) => Promise<boolean>;
  logout: () => void;
  refreshUser: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const { toast } = useToast();

  // Vérifier l'authentification au chargement
  useEffect(() => {
    const initializeAuth = async () => {
      try {
        const token = TokenManager.getAccessToken();
        if (token && !TokenManager.isTokenExpired(token)) {
          // Token valide, récupérer le profil utilisateur
          const userProfile = await authAPI.getProfile();
          setUser(userProfile);
        } else {
          // Token expiré ou inexistant
          TokenManager.clearTokens();
        }
      } catch (error) {
        console.error('Erreur lors de l\'initialisation de l\'authentification:', error);
        TokenManager.clearTokens();
        setUser(null);
      } finally {
        setIsLoading(false);
      }
    };

    initializeAuth();
  }, []);

  // Fonction de connexion
  const login = async (username: string, password: string): Promise<boolean> => {
    try {
      setIsLoading(true);
      const credentials: LoginCredentials = { username, password };
      const response = await authAPI.login(credentials);
      
      // Stocker les tokens
      TokenManager.setTokens(response.tokens);
      
      // Définir l'utilisateur
      setUser(response.user);
      
      // Mettre à jour l'activité
      localStorage.setItem('lastActivity', Date.now().toString());
      
      toast({
        title: "Connexion réussie",
        description: `Bienvenue ${response.user.first_name || response.user.username}`,
      });
      
      return true;
    } catch (error: any) {
      console.error('Erreur de connexion:', error);
      
      let errorMessage = "Une erreur s'est produite lors de la connexion";
      if (error.response?.status === 401) {
        errorMessage = "Nom d'utilisateur ou mot de passe incorrect";
      } else if (error.response?.status === 403) {
        errorMessage = "Compte désactivé ou accès refusé";
      } else if (error.response?.data?.detail) {
        errorMessage = error.response.data.detail;
      }
      
      toast({
        title: "Erreur de connexion",
        description: errorMessage,
        variant: "destructive",
      });
      
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  // Fonction de déconnexion
  const logout = async () => {
    try {
      // Appeler l'API de déconnexion
      await authAPI.logout();
    } catch (error) {
      console.error('Erreur lors de la déconnexion:', error);
    } finally {
      // Nettoyer l'état local
      TokenManager.clearTokens();
      setUser(null);
      localStorage.removeItem('lastActivity');
      localStorage.removeItem('lastVisitedPage');
      
      toast({
        title: "Déconnexion",
        description: "Vous avez été déconnecté avec succès",
      });
    }
  };

  // Fonction pour rafraîchir les données utilisateur
  const refreshUser = async () => {
    try {
      if (user) {
        const updatedUser = await authAPI.getProfile();
        setUser(updatedUser);
      }
    } catch (error) {
      console.error('Erreur lors du rafraîchissement du profil:', error);
      // Si l'erreur est 401, déconnecter l'utilisateur
      if (error.response?.status === 401) {
        logout();
      }
    }
  };

  // Vérification périodique de l'activité utilisateur
  useEffect(() => {
    if (!user) return;

    const checkActivity = () => {
      const lastActivity = localStorage.getItem('lastActivity');
      if (lastActivity) {
        const timeSinceLastActivity = Date.now() - parseInt(lastActivity);
        const maxInactivity = 24 * 60 * 60 * 1000; // 24 heures

        if (timeSinceLastActivity > maxInactivity) {
          toast({
            title: "Session expirée",
            description: "Votre session a expiré en raison d'une inactivité prolongée",
            variant: "destructive",
          });
          logout();
        }
      }
    };

    // Vérifier l'activité toutes les 5 minutes
    const activityInterval = setInterval(checkActivity, 5 * 60 * 1000);

    // Mettre à jour l'activité lors des interactions
    const updateActivity = () => {
      localStorage.setItem('lastActivity', Date.now().toString());
    };

    // Écouter les événements d'interaction
    const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart'];
    events.forEach(event => {
      document.addEventListener(event, updateActivity, true);
    });

    return () => {
      clearInterval(activityInterval);
      events.forEach(event => {
        document.removeEventListener(event, updateActivity, true);
      });
    };
  }, [user, toast]);

  const contextValue: AuthContextType = {
    user,
    isAuthenticated: !!user,
    isLoading,
    login,
    logout,
    refreshUser,
  };

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
