import React, { ReactNode, useMemo, useRef } from 'react';

interface StableListProps<T> {
  items: T[];
  renderItem: (item: T, index: number) => ReactNode;
  getKey: (item: T, index: number) => string;
  fallback?: ReactNode;
  className?: string;
  maxItems?: number;
}

function StableList<T>({
  items,
  renderItem,
  getKey,
  fallback = null,
  className,
  maxItems
}: StableListProps<T>) {
  const prevItemsRef = useRef<T[]>([]);
  const keysRef = useRef<Map<string, number>>(new Map());

  const stableItems = useMemo(() => {
    if (!Array.isArray(items)) return [];
    
    // Filtrer les éléments null/undefined
    const validItems = items.filter(item => item != null);
    
    // Limiter le nombre d'éléments si nécessaire
    const limitedItems = maxItems ? validItems.slice(0, maxItems) : validItems;
    
    // Générer des clés stables
    const newKeys = new Map<string, number>();
    limitedItems.forEach((item, index) => {
      const key = getKey(item, index);
      newKeys.set(key, index);
    });

    // Conserver les clés existantes pour éviter les re-rendus inutiles
    keysRef.current = newKeys;
    prevItemsRef.current = limitedItems;

    return limitedItems;
  }, [items, getKey, maxItems]);

  if (stableItems.length === 0) {
    return fallback ? <>{fallback}</> : null;
  }

  return (
    <div className={className}>
      {stableItems.map((item, index) => {
        const key = getKey(item, index);
        
        try {
          return (
            <React.Fragment key={key}>
              {renderItem(item, index)}
            </React.Fragment>
          );
        } catch (error) {
          console.error(`Error rendering item at index ${index}:`, error);
          return (
            <div key={`error-${key}`} className="p-2 text-sm text-destructive bg-destructive/5 rounded">
              Erreur de rendu
            </div>
          );
        }
      })}
    </div>
  );
}

export default StableList; 