import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { 
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { 
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Plus, Search, Package, Truck, Calendar } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { useProducts, useSuppliers, useCreateStockMovement } from '@/hooks/useApi';
import { formatCurrency } from '@/lib/currency';

const Supplies = () => {
  // Hooks pour les données API
  const { data: productsResponse, isLoading: productsLoading } = useProducts();
  const { data: suppliersResponse, isLoading: suppliersLoading } = useSuppliers();
  const createStockMovement = useCreateStockMovement();
  
  const [searchTerm, setSearchTerm] = useState('');
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const { toast } = useToast();

  // Extraire les données de la réponse paginée avec vérifications
  const products = productsResponse?.results || (Array.isArray(productsResponse) ? productsResponse : []);
  const suppliers = suppliersResponse?.results || (Array.isArray(suppliersResponse) ? suppliersResponse : []);

  const [formData, setFormData] = useState({
    productId: '',
    quantity: '',
    supplierId: '',
    date: new Date().toISOString().split('T')[0],
    unitPrice: ''
  });

  // Filtrer les produits pour la recherche
  const filteredProducts = products.filter((product: any) =>
    product?.name?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      const movementData = {
        product_id: formData.productId,
        movement_type: 'in',
        quantity: Number(formData.quantity),
        unit_price: Number(formData.unitPrice),
        supplier_id: formData.supplierId,
        reason: 'Approvisionnement',
        date: formData.date
      };

      await createStockMovement.mutateAsync(movementData);
      
      toast({
        title: "Succès",
        description: "Approvisionnement enregistré avec succès",
      });
      
      setIsDialogOpen(false);
      resetForm();
    } catch (error) {
      toast({
        title: "Erreur",
        description: "Erreur lors de l'enregistrement de l'approvisionnement",
        variant: "destructive",
      });
    }
  };

  const resetForm = () => {
    setFormData({
      productId: '',
      quantity: '',
      supplierId: '',
      date: new Date().toISOString().split('T')[0],
      unitPrice: ''
    });
  };

  const getTotalValue = (product: any) => {
    const quantity = product?.current_stock || 0;
    const price = product?.purchase_price || 0;
    return quantity * price;
  };

  // Calculer les statistiques avec vérifications
  const totalValue = products.reduce((sum: number, product: any) => sum + getTotalValue(product), 0);
  const totalSuppliers = suppliers.length;

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Approvisionnements</h1>
          <p className="text-muted-foreground">
            Gestion des approvisionnements et des fournisseurs
          </p>
        </div>
        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="w-4 h-4 mr-2" />
              Nouvel Approvisionnement
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[425px]">
            <DialogHeader>
              <DialogTitle>Nouvel Approvisionnement</DialogTitle>
              <DialogDescription>
                Enregistrer un nouvel approvisionnement de produits
              </DialogDescription>
            </DialogHeader>
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="productName">Produit</Label>
                <Select 
                  value={formData.productId} 
                  onValueChange={(value) => setFormData({...formData, productId: value})}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Sélectionnez un produit" />
                  </SelectTrigger>
                  <SelectContent>
                    {products.map((product: any) => (
                      <SelectItem key={product.id} value={product.id}>
                        {product.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="quantity">Quantité</Label>
                <Input
                  id="quantity"
                  type="number"
                  value={formData.quantity}
                  onChange={(e) => setFormData({...formData, quantity: e.target.value})}
                  placeholder="Ex: 48"
                  required
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="supplier">Fournisseur</Label>
                <Select
                  value={formData.supplierId}
                  onValueChange={(value) => setFormData({...formData, supplierId: value})}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Sélectionnez un fournisseur" />
                  </SelectTrigger>
                  <SelectContent>
                    {suppliers.map((supplier: any) => (
                      <SelectItem key={supplier.id} value={supplier.id}>
                        {supplier.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="unitPrice">Prix unitaire (BIF)</Label>
                <Input
                  id="unitPrice"
                  type="number"
                  value={formData.unitPrice}
                  onChange={(e) => setFormData({...formData, unitPrice: e.target.value})}
                  placeholder="Ex: 1500"
                  required
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="date">Date</Label>
                <Input
                  id="date"
                  type="date"
                  value={formData.date}
                  onChange={(e) => setFormData({...formData, date: e.target.value})}
                  required
                />
              </div>
              
              <div className="flex justify-end space-x-2">
                <Button type="button" variant="outline" onClick={() => setIsDialogOpen(false)}>
                  Annuler
                </Button>
                <Button type="submit">Enregistrer</Button>
              </div>
            </form>
          </DialogContent>
        </Dialog>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-3">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Produits</CardTitle>
            <Package className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{products.length}</div>
            <p className="text-xs text-muted-foreground">produits en stock</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Valeur totale</CardTitle>
            <Truck className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatCurrency(totalValue)}
            </div>
            <p className="text-xs text-muted-foreground">valeur du stock</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Fournisseurs</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {totalSuppliers}
            </div>
            <p className="text-xs text-muted-foreground">actifs</p>
          </CardContent>
        </Card>
      </div>

      {/* Search */}
      <Card>
        <CardContent className="pt-6">
          <div className="relative">
            <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Rechercher un produit..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
        </CardContent>
      </Card>

      {/* Products Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Package className="w-5 h-5" />
            État des Stocks
          </CardTitle>
          <CardDescription>
            {filteredProducts.length} produit(s) trouvé(s)
          </CardDescription>
        </CardHeader>
        <CardContent>
          {productsLoading ? (
            <div className="text-center py-8">
              <Package className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <p className="text-muted-foreground">Chargement des produits...</p>
            </div>
          ) : filteredProducts.length === 0 ? (
            <div className="text-center py-8">
              <Package className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">Aucun produit trouvé</h3>
              <p className="text-muted-foreground">
                {searchTerm ? 'Aucun produit ne correspond à votre recherche.' : 'Aucun produit en stock pour le moment.'}
              </p>
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Produit</TableHead>
                  <TableHead>Catégorie</TableHead>
                  <TableHead>Stock actuel</TableHead>
                  <TableHead>Prix d'achat</TableHead>
                  <TableHead>Valeur stock</TableHead>
                  <TableHead>Statut</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredProducts.map((product: any) => (
                  <TableRow key={product.id}>
                    <TableCell className="font-medium">{product.name}</TableCell>
                    <TableCell>{product.category}</TableCell>
                    <TableCell>{product.current_stock || 0}</TableCell>
                    <TableCell>
                      {product.purchase_price ? formatCurrency(product.purchase_price) : '-'}
                    </TableCell>
                    <TableCell className="font-medium">
                      {formatCurrency(getTotalValue(product))}
                    </TableCell>
                    <TableCell>
                      <Badge variant={product.current_stock > 0 ? "default" : "secondary"}>
                        {product.current_stock > 0 ? "En stock" : "Rupture"}
                      </Badge>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default Supplies; 