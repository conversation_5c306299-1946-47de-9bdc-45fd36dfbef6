"""
Services pour la gestion des recettes et des pertes
"""

from django.db import transaction
from django.utils import timezone
from decimal import Decimal
from typing import Dict, List, Tuple
from inventory.models import StockMovement
from .models import Recipe, RecipeIngredient, RecipeProduction


class RecipeStockService:
    """Service pour gérer les stocks des recettes"""
    
    @staticmethod
    def check_recipe_availability(recipe: Recipe, portions_needed: int = 1) -> Dict:
        """
        Vérifie si une recette peut être préparée
        
        Returns:
            {
                'can_prepare': bool,
                'available_portions': int,
                'missing_ingredients': list,
                'warnings': list
            }
        """
        result = {
            'can_prepare': True,
            'available_portions': recipe.available_portions(),
            'missing_ingredients': [],
            'warnings': []
        }
        
        for ingredient in recipe.ingredients.all():
            needed_quantity = ingredient.quantity_with_waste * portions_needed
            current_stock = ingredient.ingredient.current_stock
            
            if current_stock < needed_quantity:
                result['can_prepare'] = False
                result['missing_ingredients'].append({
                    'ingredient': ingredient.ingredient.name,
                    'needed': needed_quantity,
                    'available': current_stock,
                    'missing': needed_quantity - current_stock
                })
            
            # Vérifier si cela va créer une alerte de stock
            remaining_after = current_stock - needed_quantity
            if remaining_after <= ingredient.ingredient.minimum_stock:
                result['warnings'].append({
                    'ingredient': ingredient.ingredient.name,
                    'message': f'Stock bas après préparation: {remaining_after}'
                })
        
        return result
    
    @staticmethod
    @transaction.atomic
    def prepare_recipe(recipe: Recipe, portions_needed: int, user, notes: str = "") -> Dict:
        """
        Prépare une recette en déduisant les ingrédients du stock
        
        Returns:
            {
                'success': bool,
                'production_id': int,
                'ingredients_used': list,
                'total_cost': Decimal,
                'message': str
            }
        """
        # Vérifier la disponibilité
        availability = RecipeStockService.check_recipe_availability(recipe, portions_needed)
        
        if not availability['can_prepare']:
            return {
                'success': False,
                'message': 'Ingrédients insuffisants',
                'missing_ingredients': availability['missing_ingredients']
            }
        
        ingredients_used = []
        total_cost = Decimal('0.00')
        
        try:
            # Déduire chaque ingrédient du stock
            for ingredient in recipe.ingredients.all():
                quantity_to_deduct = ingredient.quantity_with_waste * portions_needed
                cost = ingredient.ingredient.purchase_price * quantity_to_deduct
                
                # Mettre à jour le stock
                ingredient.ingredient.current_stock -= quantity_to_deduct
                ingredient.ingredient.save()
                
                # Créer un mouvement de stock
                StockMovement.objects.create(
                    product=ingredient.ingredient,
                    movement_type='out',
                    reason='sale',  # ou 'production' si on ajoute cette raison
                    quantity=quantity_to_deduct,
                    stock_before=ingredient.ingredient.current_stock + quantity_to_deduct,
                    stock_after=ingredient.ingredient.current_stock,
                    unit_price=ingredient.ingredient.purchase_price,
                    total_amount=cost,
                    user=user,
                    reference=f"Préparation {recipe.name}",
                    notes=f"Préparation de {portions_needed} portion(s) de {recipe.name}"
                )
                
                ingredients_used.append({
                    'ingredient': ingredient.ingredient.name,
                    'quantity_used': quantity_to_deduct,
                    'cost': cost
                })
                
                total_cost += cost
            
            # Créer un enregistrement de production
            production = RecipeProduction.objects.create(
                recipe=recipe,
                portions_produced=portions_needed,
                production_cost=total_cost,
                produced_by=user,
                notes=notes
            )
            
            return {
                'success': True,
                'production_id': production.id,
                'ingredients_used': ingredients_used,
                'total_cost': total_cost,
                'message': f'{portions_needed} portion(s) de {recipe.name} préparée(s) avec succès'
            }
            
        except Exception as e:
            return {
                'success': False,
                'message': f'Erreur lors de la préparation: {str(e)}'
            }
    
    @staticmethod
    def calculate_recipe_profitability(recipe: Recipe) -> Dict:
        """
        Calcule la rentabilité d'une recette
        
        Returns:
            {
                'cost_per_portion': Decimal,
                'suggested_price': Decimal,
                'margin_amount': Decimal,
                'margin_percentage': Decimal,
                'break_even_portions': int
            }
        """
        cost_per_portion = recipe.cost_per_portion()
        suggested_price = recipe.suggested_selling_price()
        margin_amount = suggested_price - cost_per_portion
        
        margin_percentage = Decimal('0.00')
        if cost_per_portion > 0:
            margin_percentage = (margin_amount / cost_per_portion) * 100
        
        # Calcul du seuil de rentabilité (approximatif)
        # Basé sur les coûts fixes estimés
        fixed_costs_estimate = Decimal('50000.00')  # À ajuster selon le restaurant
        break_even_portions = 0
        if margin_amount > 0:
            break_even_portions = int(fixed_costs_estimate / margin_amount)
        
        return {
            'cost_per_portion': cost_per_portion,
            'suggested_price': suggested_price,
            'margin_amount': margin_amount,
            'margin_percentage': margin_percentage,
            'break_even_portions': break_even_portions
        }


class WasteManagementService:
    """Service pour gérer les pertes et gaspillages"""
    
    @staticmethod
    @transaction.atomic
    def record_waste(product, quantity: Decimal, reason: str, user, notes: str = "") -> Dict:
        """
        Enregistre une perte de stock
        
        Args:
            product: Produit concerné
            quantity: Quantité perdue
            reason: Raison de la perte ('damage', 'expiry', 'theft', etc.)
            user: Utilisateur qui enregistre la perte
            notes: Notes additionnelles
        
        Returns:
            {
                'success': bool,
                'movement_id': int,
                'message': str
            }
        """
        try:
            # Vérifier que la quantité est disponible
            if product.current_stock < quantity:
                return {
                    'success': False,
                    'message': f'Stock insuffisant. Disponible: {product.current_stock}'
                }
            
            # Calculer la valeur de la perte
            loss_value = product.purchase_price * quantity
            
            # Mettre à jour le stock
            stock_before = product.current_stock
            product.current_stock -= quantity
            product.save()
            
            # Créer un mouvement de stock
            movement = StockMovement.objects.create(
                product=product,
                movement_type='loss',
                reason=reason,
                quantity=quantity,
                stock_before=stock_before,
                stock_after=product.current_stock,
                unit_price=product.purchase_price,
                total_amount=loss_value,
                user=user,
                reference=f"Perte-{reason.upper()}-{timezone.now().strftime('%Y%m%d%H%M')}",
                notes=notes or f"Perte: {reason}"
            )
            
            return {
                'success': True,
                'movement_id': movement.id,
                'loss_value': loss_value,
                'message': f'Perte enregistrée: {quantity} {product.unit} de {product.name}'
            }
            
        except Exception as e:
            return {
                'success': False,
                'message': f'Erreur lors de l\'enregistrement: {str(e)}'
            }
    
    @staticmethod
    def analyze_waste_patterns(days: int = 30) -> Dict:
        """
        Analyse les patterns de pertes sur une période
        
        Returns:
            {
                'total_waste_value': Decimal,
                'waste_by_reason': dict,
                'waste_by_product': dict,
                'waste_trends': list
            }
        """
        from django.utils import timezone
        from datetime import timedelta
        from django.db.models import Sum, Count
        
        end_date = timezone.now()
        start_date = end_date - timedelta(days=days)
        
        # Mouvements de perte sur la période
        waste_movements = StockMovement.objects.filter(
            movement_type='loss',
            created_at__range=[start_date, end_date]
        )
        
        # Valeur totale des pertes
        total_waste_value = waste_movements.aggregate(
            total=Sum('total_amount')
        )['total'] or Decimal('0.00')
        
        # Pertes par raison
        waste_by_reason = {}
        for reason_code, reason_name in StockMovement.REASONS:
            if reason_code in ['damage', 'expiry', 'theft']:
                value = waste_movements.filter(reason=reason_code).aggregate(
                    total=Sum('total_amount')
                )['total'] or Decimal('0.00')
                waste_by_reason[reason_name] = value
        
        # Pertes par produit (top 10)
        waste_by_product = waste_movements.values(
            'product__name'
        ).annotate(
            total_value=Sum('total_amount'),
            total_quantity=Sum('quantity')
        ).order_by('-total_value')[:10]
        
        return {
            'total_waste_value': total_waste_value,
            'waste_by_reason': waste_by_reason,
            'waste_by_product': list(waste_by_product),
            'period_days': days
        }
    
    @staticmethod
    def get_waste_reduction_suggestions(product) -> List[str]:
        """
        Suggère des actions pour réduire les pertes d'un produit
        """
        suggestions = []
        
        # Analyser l'historique des pertes
        recent_waste = StockMovement.objects.filter(
            product=product,
            movement_type='loss',
            created_at__gte=timezone.now() - timezone.timedelta(days=30)
        )
        
        if recent_waste.exists():
            # Analyser les raisons principales
            main_reasons = recent_waste.values('reason').annotate(
                count=Count('id')
            ).order_by('-count')
            
            for reason_data in main_reasons:
                reason = reason_data['reason']
                if reason == 'expiry':
                    suggestions.append("Améliorer la rotation des stocks (FIFO)")
                    suggestions.append("Réduire les quantités commandées")
                elif reason == 'damage':
                    suggestions.append("Améliorer les conditions de stockage")
                    suggestions.append("Former le personnel à la manipulation")
                elif reason == 'theft':
                    suggestions.append("Renforcer la sécurité")
                    suggestions.append("Améliorer le contrôle d'accès")
        
        # Suggestions générales
        if product.is_low_stock:
            suggestions.append("Stock bas - vérifier les seuils de commande")
        
        return suggestions
