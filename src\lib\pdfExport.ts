import jsPDF from 'jspdf';
import autoTable from 'jspdf-autotable';
import { formatCurrency } from './currency';

// Types pour les données d'export
export interface ReportData {
  title: string;
  period: string;
  generatedAt: string;
  generatedBy: string;
  data: any[];
  summary?: {
    [key: string]: number | string;
  };
}

export interface SalesReportData extends ReportData {
  data: {
    date: string;
    orderId: string;
    items: number;
    total: number;
    server: string;
    table?: string;
  }[];
}

export interface StockReportData extends ReportData {
  data: {
    product: string;
    category: string;
    currentStock: number;
    minThreshold: number;
    value: number;
    status: string;
  }[];
}

export interface ExpenseReportData extends ReportData {
  data: {
    date: string;
    category: string;
    description: string;
    amount: number;
    approvedBy: string;
  }[];
}

// Configuration PDF
const PDF_CONFIG = {
  format: 'a4' as const,
  orientation: 'portrait' as const,
  unit: 'mm' as const,
  margins: {
    top: 20,
    right: 20,
    bottom: 20,
    left: 20
  },
  colors: {
    primary: '#2563eb',
    secondary: '#64748b',
    success: '#16a34a',
    warning: '#d97706',
    danger: '#dc2626'
  },
  fonts: {
    title: 16,
    subtitle: 14,
    body: 10,
    small: 8
  }
};

// Classe principale pour l'export PDF
export class PDFExporter {
  private doc: jsPDF;
  private currentY: number = PDF_CONFIG.margins.top;

  constructor() {
    this.doc = new jsPDF({
      orientation: PDF_CONFIG.orientation,
      unit: PDF_CONFIG.unit,
      format: PDF_CONFIG.format
    });
  }

  // Ajouter l'en-tête du document
  private addHeader(title: string, subtitle?: string) {
    const pageWidth = this.doc.internal.pageSize.width;
    
    // Logo/Titre principal
    this.doc.setFontSize(PDF_CONFIG.fonts.title);
    this.doc.setTextColor(PDF_CONFIG.colors.primary);
    this.doc.text('BarStock Wise - Gestion de Stock', PDF_CONFIG.margins.left, this.currentY);
    
    this.currentY += 10;
    
    // Titre du rapport
    this.doc.setFontSize(PDF_CONFIG.fonts.subtitle);
    this.doc.setTextColor('#000000');
    this.doc.text(title, PDF_CONFIG.margins.left, this.currentY);
    
    if (subtitle) {
      this.currentY += 7;
      this.doc.setFontSize(PDF_CONFIG.fonts.body);
      this.doc.setTextColor(PDF_CONFIG.colors.secondary);
      this.doc.text(subtitle, PDF_CONFIG.margins.left, this.currentY);
    }
    
    // Ligne de séparation
    this.currentY += 10;
    this.doc.setDrawColor(PDF_CONFIG.colors.secondary);
    this.doc.line(
      PDF_CONFIG.margins.left, 
      this.currentY, 
      pageWidth - PDF_CONFIG.margins.right, 
      this.currentY
    );
    
    this.currentY += 10;
  }

  // Ajouter les informations du rapport
  private addReportInfo(data: ReportData) {
    this.doc.setFontSize(PDF_CONFIG.fonts.body);
    this.doc.setTextColor('#000000');
    
    const info = [
      `Période: ${data.period}`,
      `Généré le: ${new Date(data.generatedAt).toLocaleString('fr-FR')}`,
      `Par: ${data.generatedBy}`
    ];
    
    info.forEach((line, index) => {
      this.doc.text(line, PDF_CONFIG.margins.left, this.currentY + (index * 5));
    });
    
    this.currentY += 20;
  }

  // Ajouter un résumé
  private addSummary(summary: { [key: string]: number | string }) {
    if (!summary || Object.keys(summary).length === 0) return;
    
    this.doc.setFontSize(PDF_CONFIG.fonts.subtitle);
    this.doc.setTextColor(PDF_CONFIG.colors.primary);
    this.doc.text('Résumé', PDF_CONFIG.margins.left, this.currentY);
    
    this.currentY += 10;
    
    const summaryData = Object.entries(summary).map(([key, value]) => [
      key,
      typeof value === 'number' && (key.toLowerCase().includes('montant') || key.toLowerCase().includes('total'))
        ? formatCurrency(value)
        : value.toString()
    ]);
    
    autoTable(this.doc, {
      startY: this.currentY,
      head: [['Indicateur', 'Valeur']],
      body: summaryData,
      theme: 'grid',
      headStyles: {
        fillColor: PDF_CONFIG.colors.primary,
        textColor: '#ffffff',
        fontSize: PDF_CONFIG.fonts.body
      },
      bodyStyles: {
        fontSize: PDF_CONFIG.fonts.body
      },
      margin: { left: PDF_CONFIG.margins.left, right: PDF_CONFIG.margins.right }
    });
    
    this.currentY = (this.doc as any).lastAutoTable.finalY + 15;
  }

  // Export rapport de ventes
  exportSalesReport(data: SalesReportData): void {
    this.addHeader('Rapport de Ventes', data.period);
    this.addReportInfo(data);
    this.addSummary(data.summary || {});
    
    // Tableau des ventes
    this.doc.setFontSize(PDF_CONFIG.fonts.subtitle);
    this.doc.setTextColor(PDF_CONFIG.colors.primary);
    this.doc.text('Détail des Ventes', PDF_CONFIG.margins.left, this.currentY);
    this.currentY += 10;
    
    const tableData = data.data.map(sale => [
      new Date(sale.date).toLocaleDateString('fr-FR'),
      sale.orderId,
      sale.items.toString(),
      formatCurrency(sale.total),
      sale.server,
      sale.table || '-'
    ]);
    
    autoTable(this.doc, {
      startY: this.currentY,
      head: [['Date', 'Commande', 'Articles', 'Total', 'Serveur', 'Table']],
      body: tableData,
      theme: 'striped',
      headStyles: {
        fillColor: PDF_CONFIG.colors.primary,
        textColor: '#ffffff',
        fontSize: PDF_CONFIG.fonts.body
      },
      bodyStyles: {
        fontSize: PDF_CONFIG.fonts.small
      },
      alternateRowStyles: {
        fillColor: '#f8fafc'
      },
      margin: { left: PDF_CONFIG.margins.left, right: PDF_CONFIG.margins.right }
    });
    
    this.addFooter();
    this.save(`rapport-ventes-${new Date().toISOString().split('T')[0]}.pdf`);
  }

  // Export rapport de stock
  exportStockReport(data: StockReportData): void {
    this.addHeader('Rapport de Stock', data.period);
    this.addReportInfo(data);
    this.addSummary(data.summary || {});
    
    // Tableau des stocks
    this.doc.setFontSize(PDF_CONFIG.fonts.subtitle);
    this.doc.setTextColor(PDF_CONFIG.colors.primary);
    this.doc.text('État des Stocks', PDF_CONFIG.margins.left, this.currentY);
    this.currentY += 10;
    
    const tableData = data.data.map(stock => [
      stock.product,
      stock.category,
      stock.currentStock.toString(),
      stock.minThreshold.toString(),
      formatCurrency(stock.value),
      stock.status
    ]);
    
    autoTable(this.doc, {
      startY: this.currentY,
      head: [['Produit', 'Catégorie', 'Stock', 'Seuil Min', 'Valeur', 'Statut']],
      body: tableData,
      theme: 'striped',
      headStyles: {
        fillColor: PDF_CONFIG.colors.primary,
        textColor: '#ffffff',
        fontSize: PDF_CONFIG.fonts.body
      },
      bodyStyles: {
        fontSize: PDF_CONFIG.fonts.small
      },
      alternateRowStyles: {
        fillColor: '#f8fafc'
      },
      margin: { left: PDF_CONFIG.margins.left, right: PDF_CONFIG.margins.right },
      didParseCell: (data) => {
        // Colorer les statuts
        if (data.column.index === 5) { // Colonne Statut
          const status = data.cell.text[0];
          if (status === 'Critique') {
            data.cell.styles.textColor = PDF_CONFIG.colors.danger;
            data.cell.styles.fontStyle = 'bold';
          } else if (status === 'Faible') {
            data.cell.styles.textColor = PDF_CONFIG.colors.warning;
          } else if (status === 'Normal') {
            data.cell.styles.textColor = PDF_CONFIG.colors.success;
          }
        }
      }
    });
    
    this.addFooter();
    this.save(`rapport-stock-${new Date().toISOString().split('T')[0]}.pdf`);
  }

  // Export rapport de dépenses
  exportExpenseReport(data: ExpenseReportData): void {
    this.addHeader('Rapport de Dépenses', data.period);
    this.addReportInfo(data);
    this.addSummary(data.summary || {});
    
    // Tableau des dépenses
    this.doc.setFontSize(PDF_CONFIG.fonts.subtitle);
    this.doc.setTextColor(PDF_CONFIG.colors.primary);
    this.doc.text('Détail des Dépenses', PDF_CONFIG.margins.left, this.currentY);
    this.currentY += 10;
    
    const tableData = data.data.map(expense => [
      new Date(expense.date).toLocaleDateString('fr-FR'),
      expense.category,
      expense.description,
      formatCurrency(expense.amount),
      expense.approvedBy
    ]);
    
    autoTable(this.doc, {
      startY: this.currentY,
      head: [['Date', 'Catégorie', 'Description', 'Montant', 'Approuvé par']],
      body: tableData,
      theme: 'striped',
      headStyles: {
        fillColor: PDF_CONFIG.colors.primary,
        textColor: '#ffffff',
        fontSize: PDF_CONFIG.fonts.body
      },
      bodyStyles: {
        fontSize: PDF_CONFIG.fonts.small
      },
      alternateRowStyles: {
        fillColor: '#f8fafc'
      },
      margin: { left: PDF_CONFIG.margins.left, right: PDF_CONFIG.margins.right }
    });
    
    this.addFooter();
    this.save(`rapport-depenses-${new Date().toISOString().split('T')[0]}.pdf`);
  }

  // Ajouter le pied de page
  private addFooter() {
    const pageHeight = this.doc.internal.pageSize.height;
    const pageWidth = this.doc.internal.pageSize.width;
    
    this.doc.setFontSize(PDF_CONFIG.fonts.small);
    this.doc.setTextColor(PDF_CONFIG.colors.secondary);
    
    // Ligne de séparation
    this.doc.line(
      PDF_CONFIG.margins.left,
      pageHeight - 15,
      pageWidth - PDF_CONFIG.margins.right,
      pageHeight - 15
    );
    
    // Informations du pied de page
    this.doc.text(
      'BarStock Wise - Système de Gestion de Stock pour Bar-Restaurant',
      PDF_CONFIG.margins.left,
      pageHeight - 10
    );
    
    this.doc.text(
      `Page 1 - Généré le ${new Date().toLocaleString('fr-FR')}`,
      pageWidth - PDF_CONFIG.margins.right - 50,
      pageHeight - 10
    );
  }

  // Sauvegarder le PDF
  private save(filename: string) {
    this.doc.save(filename);
  }
}

// Fonctions utilitaires d'export
export const exportSalesReportToPDF = (data: SalesReportData) => {
  const exporter = new PDFExporter();
  exporter.exportSalesReport(data);
};

export const exportStockReportToPDF = (data: StockReportData) => {
  const exporter = new PDFExporter();
  exporter.exportStockReport(data);
};

export const exportExpenseReportToPDF = (data: ExpenseReportData) => {
  const exporter = new PDFExporter();
  exporter.exportExpenseReport(data);
};

// Export Excel (simulation - nécessiterait une vraie librairie Excel)
export const exportToExcel = (data: any[], filename: string) => {
  // Pour l'instant, on exporte en CSV
  const csvContent = convertToCSV(data);
  downloadCSV(csvContent, filename);
};

const convertToCSV = (data: any[]): string => {
  if (data.length === 0) return '';
  
  const headers = Object.keys(data[0]);
  const csvRows = [
    headers.join(','),
    ...data.map(row => 
      headers.map(header => {
        const value = row[header];
        return typeof value === 'string' && value.includes(',') 
          ? `"${value}"` 
          : value;
      }).join(',')
    )
  ];
  
  return csvRows.join('\n');
};

const downloadCSV = (csvContent: string, filename: string) => {
  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
  const link = document.createElement('a');
  const url = URL.createObjectURL(blob);

  link.setAttribute('href', url);
  link.setAttribute('download', filename);
  link.style.visibility = 'hidden';

  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
};

// Interface pour les rapports journaliers
export interface DailyReportPDFData {
  date: string;
  generatedBy: string;
  items: Array<{
    produit: string;
    category: string;
    stockInitial: number;
    stockEntrant: number;
    stockTotal: number;
    consommation: number;
    perte: number;
    stockRestant: number;
    prixVenteUnitaire: number;
    stockVendu: number;
    benefice: number;
    marge: number;
  }>;
  totals: {
    recetteBieres: number;
    recetteLiqueurs: number;
    recetteTotal: number;
    beneficeTotal: number;
  };
  alerts: Array<{
    productName: string;
    alertType: string;
    message: string;
    severity: string;
  }>;
}

// Export PDF spécialisé pour rapport journalier
export const exportDailyReportToPDF = (data: DailyReportPDFData) => {
  const doc = new jsPDF();
  let yPosition = 20;

  // En-tête du rapport
  doc.setFontSize(18);
  doc.setFont('helvetica', 'bold');
  doc.text('RAPPORT JOURNALIER BOISSONS', 105, yPosition, { align: 'center' });

  yPosition += 10;
  doc.setFontSize(12);
  doc.setFont('helvetica', 'normal');
  doc.text(`Date: ${data.date}`, 20, yPosition);
  doc.text(`Généré par: ${data.generatedBy}`, 120, yPosition);

  yPosition += 15;

  // Résumé financier
  doc.setFontSize(14);
  doc.setFont('helvetica', 'bold');
  doc.text('RÉSUMÉ FINANCIER', 20, yPosition);
  yPosition += 10;

  doc.setFontSize(10);
  doc.setFont('helvetica', 'normal');

  const summaryData = [
    ['Recette Bières Brarudi', `${data.totals.recetteBieres.toLocaleString()} BIF`],
    ['Recette Liqueurs', `${data.totals.recetteLiqueurs.toLocaleString()} BIF`],
    ['RECETTE TOTALE', `${data.totals.recetteTotal.toLocaleString()} BIF`],
    ['BÉNÉFICE TOTAL', `${data.totals.beneficeTotal.toLocaleString()} BIF`]
  ];

  (doc as any).autoTable({
    startY: yPosition,
    head: [['Catégorie', 'Montant']],
    body: summaryData,
    theme: 'grid',
    headStyles: { fillColor: [41, 128, 185] },
    styles: { fontSize: 9 },
    columnStyles: {
      1: { halign: 'right', fontStyle: 'bold' }
    }
  });

  yPosition = (doc as any).lastAutoTable.finalY + 15;

  // Section Bières Brarudi
  doc.setFontSize(14);
  doc.setFont('helvetica', 'bold');
  doc.text('BIÈRES BRARUDI', 20, yPosition);
  yPosition += 10;

  const bieresData = data.items
    .filter(item => item.category === 'Bières Brarudi')
    .map(item => [
      item.produit,
      item.stockInitial.toString(),
      item.stockEntrant.toString(),
      item.stockTotal.toString(),
      item.consommation.toString(),
      item.stockRestant.toString(),
      `${item.prixVenteUnitaire.toLocaleString()} BIF`,
      item.stockVendu.toString(),
      `${item.benefice.toLocaleString()} BIF`
    ]);

  (doc as any).autoTable({
    startY: yPosition,
    head: [['Produit', 'Stock Initial', 'Stock Entrant', 'Stock Total', 'Consommation', 'Stock Restant', 'P.V.U', 'Stock Vendu', 'Bénéfice']],
    body: bieresData,
    theme: 'striped',
    headStyles: { fillColor: [52, 152, 219] },
    styles: { fontSize: 8 },
    columnStyles: {
      6: { halign: 'right' },
      8: { halign: 'right', fontStyle: 'bold' }
    }
  });

  yPosition = (doc as any).lastAutoTable.finalY + 15;

  // Pied de page
  const pageCount = doc.getNumberOfPages();
  for (let i = 1; i <= pageCount; i++) {
    doc.setPage(i);
    doc.setFontSize(8);
    doc.setFont('helvetica', 'normal');
    doc.text(`Page ${i} sur ${pageCount}`, 105, 290, { align: 'center' });
    doc.text(`Généré le ${new Date().toLocaleString('fr-FR')}`, 20, 290);
  }

  // Téléchargement
  doc.save(`rapport-journalier-${data.date}.pdf`);
};
