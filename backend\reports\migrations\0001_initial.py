# Generated by Django 5.2.4 on 2025-07-30 14:12

import django.core.validators
import django.db.models.deletion
from decimal import Decimal
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('products', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='DailyReport',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateField(unique=True, verbose_name='Date du rapport')),
                ('total_sales', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=12, validators=[django.core.validators.MinValueValidator(Decimal('0.00'))], verbose_name='Total des ventes (BIF)')),
                ('total_profit', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=12, validators=[django.core.validators.MinValueValidator(Decimal('0.00'))], verbose_name='Bénéfice total (BIF)')),
                ('number_of_sales', models.PositiveIntegerField(default=0, verbose_name='Nombre de ventes')),
                ('total_expenses', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=12, validators=[django.core.validators.MinValueValidator(Decimal('0.00'))], verbose_name='Total des dépenses (BIF)')),
                ('stock_alerts_count', models.PositiveIntegerField(default=0, verbose_name="Nombre d'alertes stock")),
                ('out_of_stock_count', models.PositiveIntegerField(default=0, verbose_name='Produits en rupture')),
                ('net_result', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=12, verbose_name='Résultat net (BIF)')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='Notes')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Date de création')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Date de modification')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='Utilisateur')),
            ],
            options={
                'verbose_name': 'Rapport journalier',
                'verbose_name_plural': 'Rapports journaliers',
                'ordering': ['-date'],
            },
        ),
        migrations.CreateModel(
            name='StockAlert',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('alert_type', models.CharField(choices=[('low_stock', 'Stock faible'), ('out_of_stock', 'Rupture de stock'), ('expiry_soon', 'Expiration proche')], max_length=20, verbose_name="Type d'alerte")),
                ('status', models.CharField(choices=[('active', 'Active'), ('resolved', 'Résolue'), ('ignored', 'Ignorée')], default='active', max_length=20, verbose_name='Statut')),
                ('current_stock', models.PositiveIntegerField(verbose_name='Stock actuel')),
                ('threshold', models.PositiveIntegerField(verbose_name="Seuil d'alerte")),
                ('message', models.TextField(verbose_name="Message d'alerte")),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Date de création')),
                ('resolved_at', models.DateTimeField(blank=True, null=True, verbose_name='Date de résolution')),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='alerts', to='products.product', verbose_name='Produit')),
                ('resolved_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='Résolu par')),
            ],
            options={
                'verbose_name': 'Alerte de stock',
                'verbose_name_plural': 'Alertes de stock',
                'ordering': ['-created_at'],
            },
        ),
    ]
