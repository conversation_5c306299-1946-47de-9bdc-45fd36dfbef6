from django.db import models
from django.core.validators import MinValueValidator
from decimal import Decimal
from django.conf import settings


class Recipe(models.Model):
    """
    Modèle pour les recettes de plats composés
    """
    
    DIFFICULTY_CHOICES = [
        ('easy', 'Facile'),
        ('medium', 'Moyen'),
        ('hard', 'Difficile'),
    ]
    
    dish = models.OneToOneField(
        'products.Product',
        on_delete=models.CASCADE,
        related_name='recipe',
        verbose_name='Plat'
    )
    
    name = models.CharField(
        max_length=200,
        verbose_name='Nom de la recette'
    )
    
    description = models.TextField(
        blank=True,
        null=True,
        verbose_name='Description'
    )
    
    portions_per_recipe = models.PositiveIntegerField(
        default=1,
        verbose_name='Portions par recette'
    )
    
    preparation_time = models.PositiveIntegerField(
        help_text='Temps en minutes',
        verbose_name='Temps de préparation'
    )
    
    cooking_time = models.PositiveIntegerField(
        help_text='Temps en minutes',
        verbose_name='Temps de cuisson'
    )
    
    difficulty = models.CharField(
        max_length=10,
        choices=DIFFICULTY_CHOICES,
        default='medium',
        verbose_name='Difficulté'
    )
    
    # Facteur de perte global pour cette recette
    waste_factor = models.DecimalField(
        max_digits=4,
        decimal_places=2,
        default=Decimal('1.10'),  # 10% de perte par défaut
        validators=[MinValueValidator(Decimal('1.00'))],
        help_text='Facteur de perte (1.10 = 10% de perte)',
        verbose_name='Facteur de perte'
    )
    
    is_active = models.BooleanField(
        default=True,
        verbose_name='Recette active'
    )
    
    created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        verbose_name='Créé par'
    )
    
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name='Date de création'
    )
    
    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name='Date de modification'
    )
    
    class Meta:
        verbose_name = 'Recette'
        verbose_name_plural = 'Recettes'
        ordering = ['name']
    
    def __str__(self):
        return f"{self.name} ({self.portions_per_recipe} portion(s))"
    
    @property
    def total_preparation_time(self):
        """Temps total de préparation"""
        return self.preparation_time + self.cooking_time
    
    def available_portions(self):
        """Calcule le nombre de portions disponibles"""
        if not self.ingredients.exists():
            return 0
        
        min_portions = float('inf')
        
        for ingredient in self.ingredients.all():
            if ingredient.ingredient.current_stock <= 0:
                return 0
            
            # Quantité nécessaire avec facteur de perte
            needed_quantity = ingredient.quantity_needed * ingredient.waste_factor
            
            # Portions possibles avec cet ingrédient
            possible_portions = ingredient.ingredient.current_stock / needed_quantity
            min_portions = min(min_portions, possible_portions)
        
        return int(min_portions * self.portions_per_recipe)
    
    def cost_per_portion(self):
        """Calcule le coût par portion"""
        total_cost = Decimal('0.00')
        
        for ingredient in self.ingredients.all():
            # Coût avec facteur de perte
            ingredient_cost = (
                ingredient.ingredient.purchase_price * 
                ingredient.quantity_needed * 
                ingredient.waste_factor
            )
            total_cost += ingredient_cost
        
        if self.portions_per_recipe > 0:
            return total_cost / self.portions_per_recipe
        return total_cost
    
    def suggested_selling_price(self, margin_percentage=300):
        """Prix de vente suggéré avec marge"""
        cost = self.cost_per_portion()
        return cost * (Decimal(str(margin_percentage)) / 100)


class RecipeIngredient(models.Model):
    """
    Ingrédients nécessaires pour une recette
    """
    
    recipe = models.ForeignKey(
        Recipe,
        on_delete=models.CASCADE,
        related_name='ingredients',
        verbose_name='Recette'
    )
    
    ingredient = models.ForeignKey(
        'products.Product',
        on_delete=models.CASCADE,
        verbose_name='Ingrédient'
    )
    
    quantity_needed = models.DecimalField(
        max_digits=8,
        decimal_places=3,
        validators=[MinValueValidator(Decimal('0.001'))],
        verbose_name='Quantité nécessaire'
    )
    
    unit = models.CharField(
        max_length=20,
        verbose_name='Unité'
    )
    
    # Facteur de perte spécifique à cet ingrédient
    waste_factor = models.DecimalField(
        max_digits=4,
        decimal_places=2,
        default=Decimal('1.05'),  # 5% de perte par défaut
        validators=[MinValueValidator(Decimal('1.00'))],
        help_text='Facteur de perte pour cet ingrédient',
        verbose_name='Facteur de perte'
    )
    
    is_optional = models.BooleanField(
        default=False,
        verbose_name='Ingrédient optionnel'
    )
    
    notes = models.TextField(
        blank=True,
        null=True,
        verbose_name='Notes de préparation'
    )
    
    order = models.PositiveIntegerField(
        default=0,
        verbose_name='Ordre dans la recette'
    )
    
    class Meta:
        verbose_name = 'Ingrédient de recette'
        verbose_name_plural = 'Ingrédients de recette'
        unique_together = ['recipe', 'ingredient']
        ordering = ['order', 'ingredient__name']
    
    def __str__(self):
        return f"{self.ingredient.name} - {self.quantity_needed} {self.unit}"
    
    @property
    def quantity_with_waste(self):
        """Quantité nécessaire avec facteur de perte"""
        return self.quantity_needed * self.waste_factor
    
    @property
    def cost_per_recipe(self):
        """Coût de cet ingrédient pour la recette"""
        return self.ingredient.purchase_price * self.quantity_with_waste


class RecipeProduction(models.Model):
    """
    Enregistrement de production de recettes
    """
    
    recipe = models.ForeignKey(
        Recipe,
        on_delete=models.CASCADE,
        related_name='productions',
        verbose_name='Recette'
    )
    
    portions_produced = models.PositiveIntegerField(
        verbose_name='Portions produites'
    )
    
    actual_waste = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        default=Decimal('0.00'),
        help_text='Perte réelle en pourcentage',
        verbose_name='Perte réelle (%)'
    )
    
    production_cost = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=Decimal('0.00'),
        verbose_name='Coût de production'
    )
    
    produced_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        verbose_name='Produit par'
    )
    
    production_date = models.DateTimeField(
        auto_now_add=True,
        verbose_name='Date de production'
    )
    
    notes = models.TextField(
        blank=True,
        null=True,
        verbose_name='Notes de production'
    )
    
    class Meta:
        verbose_name = 'Production de recette'
        verbose_name_plural = 'Productions de recettes'
        ordering = ['-production_date']
    
    def __str__(self):
        return f"{self.recipe.name} - {self.portions_produced} portions - {self.production_date.date()}"
    
    def save(self, *args, **kwargs):
        # Calculer le coût de production automatiquement
        if not self.production_cost:
            self.production_cost = self.recipe.cost_per_portion() * self.portions_produced
        super().save(*args, **kwargs)
