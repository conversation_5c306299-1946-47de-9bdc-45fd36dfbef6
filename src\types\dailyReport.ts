// Types pour les rapports journaliers de boissons
export interface DailyStockItem {
  id: string;
  produit: string;
  category: 'Bières Brarudi' | 'Liqueurs' | 'Autres Boissons';
  prixCasier: number;
  stockInitial: number;
  stockEntrant: number;
  stockTotal: number;
  consommation: number;
  perte: number;
  stockRestant: number;
  prixAchatUnitaire: number;
  prixVenteUnitaire: number;
  stockConsomme: number;
  stockVendu: number;
  marge: number;
  benefice: number;
  notes?: string;
}

export interface DailyReportSummary {
  date: string;
  recetteBoissonsBrarudi: number;
  recetteLiqueurs: number;
  recetteTotal: number;
  beneficeTotal: number;
  tauxMargeGlobal: number;
  nombreProduitsEnRupture: number;
  nombreProduitsStockFaible: number;
  valeurStockTotal: number;
}

export interface DailyReport {
  id: string;
  date: string;
  generatedBy: string;
  items: DailyStockItem[];
  summary: DailyReportSummary;
  alerts: StockAlert[];
  createdAt: string;
  updatedAt: string;
}

export interface StockAlert {
  id: string;
  productId: string;
  productName: string;
  type: 'stock_out' | 'stock_low' | 'calculation_error' | 'high_loss';
  alertType: 'Rupture' | 'Stock faible' | 'Incohérence' | 'Perte élevée';
  severity: 'Critique' | 'Élevée' | 'Moyenne';
  message: string;
  suggestedAction: string;
}

// Utilitaires de calcul
export interface StockCalculations {
  calculateStockTotal: (initial: number, entrant: number) => number;
  calculateStockRestant: (total: number, consommation: number, perte: number) => number;
  calculateMarge: (prixVente: number, prixAchat: number) => number;
  calculateBenefice: (stockVendu: number, marge: number) => number;
  validateStockCoherence: (item: DailyStockItem) => StockAlert[];
}

// Configuration des seuils d'alerte
export interface AlertThresholds {
  stockFaibleSeuil: number; // Pourcentage du stock initial
  perteMaximale: number; // Pourcentage acceptable de perte
  margeMinimale: number; // Marge minimale attendue en BIF
}

export const DEFAULT_ALERT_THRESHOLDS: AlertThresholds = {
  stockFaibleSeuil: 20, // 20% du stock initial
  perteMaximale: 5, // 5% de perte acceptable
  margeMinimale: 1000 // 1000 BIF de marge minimale
};
