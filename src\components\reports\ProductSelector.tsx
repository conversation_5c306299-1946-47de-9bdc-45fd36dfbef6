import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import { 
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Plus, Search, Package, Wine, Coffee } from 'lucide-react';
import { PRODUCT_TEMPLATES, ProductTemplate, getTemplatesByCategory, createItemFromTemplate } from '@/lib/reportTemplates';
import { DailyStockItem } from '@/types/dailyReport';
import { formatCurrency } from '@/lib/currency';

interface ProductSelectorProps {
  onProductsSelected: (items: DailyStockItem[]) => void;
  selectedProducts: string[];
}

const ProductSelector: React.FC<ProductSelectorProps> = ({ onProductsSelected, selectedProducts }) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedTemplates, setSelectedTemplates] = useState<string[]>(selectedProducts);
  const [stockValues, setStockValues] = useState<Record<string, { initial: number; entrant: number }>>({});
  const [isOpen, setIsOpen] = useState(false);

  // Filtrer les templates selon la recherche
  const filteredTemplates = PRODUCT_TEMPLATES.filter(template =>
    template.produit.toLowerCase().includes(searchTerm.toLowerCase()) &&
    template.isActive
  );

  // Grouper par catégorie
  const templatesByCategory = {
    'Bières Brarudi': getTemplatesByCategory('Bières Brarudi'),
    'Liqueurs': getTemplatesByCategory('Liqueurs'),
    'Autres Boissons': getTemplatesByCategory('Autres Boissons')
  };

  const handleTemplateToggle = (templateId: string) => {
    setSelectedTemplates(prev => 
      prev.includes(templateId)
        ? prev.filter(id => id !== templateId)
        : [...prev, templateId]
    );
  };

  const handleStockChange = (templateId: string, field: 'initial' | 'entrant', value: number) => {
    setStockValues(prev => ({
      ...prev,
      [templateId]: {
        ...prev[templateId],
        [field]: value
      }
    }));
  };

  const handleConfirm = () => {
    const selectedItems: DailyStockItem[] = selectedTemplates.map(templateId => {
      const template = PRODUCT_TEMPLATES.find(t => t.id === templateId);
      if (!template) return null;

      const stocks = stockValues[templateId] || { initial: 0, entrant: 0 };
      return createItemFromTemplate(template, stocks.initial, stocks.entrant);
    }).filter(Boolean) as DailyStockItem[];

    onProductsSelected(selectedItems);
    setIsOpen(false);
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'Bières Brarudi': return <Package className="w-4 h-4" />;
      case 'Liqueurs': return <Wine className="w-4 h-4" />;
      case 'Autres Boissons': return <Coffee className="w-4 h-4" />;
      default: return <Package className="w-4 h-4" />;
    }
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'Bières Brarudi': return 'bg-blue-100 text-blue-800';
      case 'Liqueurs': return 'bg-purple-100 text-purple-800';
      case 'Autres Boissons': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" className="w-full">
          <Plus className="w-4 h-4 mr-2" />
          Sélectionner les produits ({selectedProducts.length})
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Sélection des produits pour le rapport</DialogTitle>
          <DialogDescription>
            Choisissez les produits à inclure dans votre rapport journalier et définissez les stocks initiaux.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* Barre de recherche */}
          <div className="relative">
            <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Rechercher un produit..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>

          {/* Résumé de sélection */}
          <div className="flex items-center justify-between p-3 bg-muted rounded-lg">
            <span className="text-sm font-medium">
              {selectedTemplates.length} produit(s) sélectionné(s)
            </span>
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setSelectedTemplates(PRODUCT_TEMPLATES.map(t => t.id))}
              >
                Tout sélectionner
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setSelectedTemplates([])}
              >
                Tout désélectionner
              </Button>
            </div>
          </div>

          {/* Onglets par catégorie */}
          <Tabs defaultValue="Bières Brarudi" className="w-full">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="Bières Brarudi" className="flex items-center gap-2">
                <Package className="w-4 h-4" />
                Bières
              </TabsTrigger>
              <TabsTrigger value="Liqueurs" className="flex items-center gap-2">
                <Wine className="w-4 h-4" />
                Liqueurs
              </TabsTrigger>
              <TabsTrigger value="Autres Boissons" className="flex items-center gap-2">
                <Coffee className="w-4 h-4" />
                Autres
              </TabsTrigger>
            </TabsList>

            {Object.entries(templatesByCategory).map(([category, templates]) => (
              <TabsContent key={category} value={category} className="space-y-4">
                <div className="grid gap-4 md:grid-cols-2">
                  {templates
                    .filter(template => 
                      searchTerm === '' || 
                      template.produit.toLowerCase().includes(searchTerm.toLowerCase())
                    )
                    .map(template => {
                      const isSelected = selectedTemplates.includes(template.id);
                      const stocks = stockValues[template.id] || { initial: 0, entrant: 0 };

                      return (
                        <Card key={template.id} className={`cursor-pointer transition-all ${
                          isSelected ? 'ring-2 ring-primary' : ''
                        }`}>
                          <CardHeader className="pb-3">
                            <div className="flex items-center justify-between">
                              <div className="flex items-center space-x-2">
                                <Checkbox
                                  checked={isSelected}
                                  onCheckedChange={() => handleTemplateToggle(template.id)}
                                />
                                <div>
                                  <CardTitle className="text-sm">{template.produit}</CardTitle>
                                  <Badge className={`text-xs ${getCategoryColor(template.category)}`}>
                                    {getCategoryIcon(template.category)}
                                    <span className="ml-1">{template.category}</span>
                                  </Badge>
                                </div>
                              </div>
                            </div>
                          </CardHeader>
                          <CardContent className="pt-0">
                            <div className="space-y-2 text-xs text-muted-foreground">
                              <div className="flex justify-between">
                                <span>Prix casier:</span>
                                <span className="font-medium">{formatCurrency(template.prixCasier)}</span>
                              </div>
                              <div className="flex justify-between">
                                <span>P.A.U:</span>
                                <span className="font-medium">{formatCurrency(template.prixAchatUnitaire)}</span>
                              </div>
                              <div className="flex justify-between">
                                <span>P.V.U:</span>
                                <span className="font-medium">{formatCurrency(template.prixVenteUnitaire)}</span>
                              </div>
                              <div className="flex justify-between">
                                <span>Marge:</span>
                                <span className="font-medium text-green-600">
                                  {formatCurrency(template.prixVenteUnitaire - template.prixAchatUnitaire)}
                                </span>
                              </div>
                            </div>

                            {isSelected && (
                              <div className="mt-4 space-y-3 pt-3 border-t">
                                <div className="grid grid-cols-2 gap-2">
                                  <div>
                                    <Label htmlFor={`initial-${template.id}`} className="text-xs">
                                      Stock initial
                                    </Label>
                                    <Input
                                      id={`initial-${template.id}`}
                                      type="number"
                                      min="0"
                                      value={stocks.initial}
                                      onChange={(e) => handleStockChange(
                                        template.id, 
                                        'initial', 
                                        parseInt(e.target.value) || 0
                                      )}
                                      className="h-8 text-xs"
                                    />
                                  </div>
                                  <div>
                                    <Label htmlFor={`entrant-${template.id}`} className="text-xs">
                                      Stock entrant
                                    </Label>
                                    <Input
                                      id={`entrant-${template.id}`}
                                      type="number"
                                      min="0"
                                      value={stocks.entrant}
                                      onChange={(e) => handleStockChange(
                                        template.id, 
                                        'entrant', 
                                        parseInt(e.target.value) || 0
                                      )}
                                      className="h-8 text-xs"
                                    />
                                  </div>
                                </div>
                                {(stocks.initial > 0 || stocks.entrant > 0) && (
                                  <div className="text-xs text-muted-foreground">
                                    Stock total: <span className="font-medium">{stocks.initial + stocks.entrant}</span>
                                  </div>
                                )}
                              </div>
                            )}
                          </CardContent>
                        </Card>
                      );
                    })}
                </div>
              </TabsContent>
            ))}
          </Tabs>

          {/* Boutons d'action */}
          <div className="flex justify-end space-x-2 pt-4 border-t">
            <Button variant="outline" onClick={() => setIsOpen(false)}>
              Annuler
            </Button>
            <Button onClick={handleConfirm} disabled={selectedTemplates.length === 0}>
              Confirmer ({selectedTemplates.length} produits)
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default ProductSelector;
