// Service Worker pour BarStock Wise
// Mode hors-ligne et cache intelligent

const CACHE_NAME = 'barstock-wise-v1.0.0';
const OFFLINE_URL = '/offline.html';

// Ressources à mettre en cache
const STATIC_CACHE_URLS = [
  '/',
  '/offline.html',
  '/manifest.json',
  '/icon-192x192.png',
  '/icon-512x512.png',
  // Ajouter d'autres ressources statiques critiques
];

// Ressources API à mettre en cache
const API_CACHE_PATTERNS = [
  /\/api\/products\//,
  /\/api\/inventory\//,
  /\/api\/settings\//
];

// Installation du Service Worker
self.addEventListener('install', event => {
  console.log('Service Worker: Installation');
  
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then(cache => {
        console.log('Service Worker: Mise en cache des ressources statiques');
        return cache.addAll(STATIC_CACHE_URLS);
      })
      .then(() => {
        console.log('Service Worker: Installation terminée');
        return self.skipWaiting();
      })
      .catch(error => {
        console.error('Service Worker: Erreur installation', error);
      })
  );
});

// Activation du Service Worker
self.addEventListener('activate', event => {
  console.log('Service Worker: Activation');
  
  event.waitUntil(
    caches.keys()
      .then(cacheNames => {
        return Promise.all(
          cacheNames.map(cacheName => {
            if (cacheName !== CACHE_NAME) {
              console.log('Service Worker: Suppression ancien cache', cacheName);
              return caches.delete(cacheName);
            }
          })
        );
      })
      .then(() => {
        console.log('Service Worker: Activation terminée');
        return self.clients.claim();
      })
  );
});

// Interception des requêtes
self.addEventListener('fetch', event => {
  const { request } = event;
  const url = new URL(request.url);

  // Stratégie pour les pages HTML
  if (request.mode === 'navigate') {
    event.respondWith(
      fetch(request)
        .then(response => {
          // Mettre en cache la réponse si elle est valide
          if (response.status === 200) {
            const responseClone = response.clone();
            caches.open(CACHE_NAME)
              .then(cache => cache.put(request, responseClone));
          }
          return response;
        })
        .catch(() => {
          // Retourner la page depuis le cache ou la page offline
          return caches.match(request)
            .then(cachedResponse => {
              return cachedResponse || caches.match(OFFLINE_URL);
            });
        })
    );
    return;
  }

  // Stratégie pour les APIs
  if (url.pathname.startsWith('/api/')) {
    event.respondWith(
      handleAPIRequest(request)
    );
    return;
  }

  // Stratégie pour les ressources statiques
  event.respondWith(
    caches.match(request)
      .then(cachedResponse => {
        if (cachedResponse) {
          return cachedResponse;
        }
        
        return fetch(request)
          .then(response => {
            // Mettre en cache les ressources statiques
            if (response.status === 200 && request.method === 'GET') {
              const responseClone = response.clone();
              caches.open(CACHE_NAME)
                .then(cache => cache.put(request, responseClone));
            }
            return response;
          });
      })
      .catch(() => {
        // Retourner une réponse par défaut pour les ressources manquantes
        if (request.destination === 'image') {
          return new Response('', { status: 404 });
        }
      })
  );
});

// Gestion spécialisée des requêtes API
async function handleAPIRequest(request) {
  const url = new URL(request.url);
  
  try {
    // Essayer la requête réseau d'abord
    const networkResponse = await fetch(request);
    
    // Mettre en cache les réponses GET réussies
    if (request.method === 'GET' && networkResponse.status === 200) {
      const responseClone = networkResponse.clone();
      const cache = await caches.open(CACHE_NAME);
      await cache.put(request, responseClone);
    }
    
    return networkResponse;
  } catch (error) {
    console.log('Service Worker: Requête réseau échouée, tentative cache', url.pathname);
    
    // Si la requête réseau échoue, essayer le cache
    if (request.method === 'GET') {
      const cachedResponse = await caches.match(request);
      if (cachedResponse) {
        console.log('Service Worker: Réponse depuis le cache', url.pathname);
        return cachedResponse;
      }
    }
    
    // Retourner une réponse d'erreur appropriée
    return new Response(
      JSON.stringify({
        error: 'Mode hors-ligne',
        message: 'Cette fonctionnalité nécessite une connexion internet',
        offline: true
      }),
      {
        status: 503,
        statusText: 'Service Unavailable',
        headers: { 'Content-Type': 'application/json' }
      }
    );
  }
}

// Gestion des notifications push
self.addEventListener('push', event => {
  console.log('Service Worker: Notification push reçue');
  
  const options = {
    body: 'Nouvelle notification BarStock Wise',
    icon: '/icon-192x192.png',
    badge: '/badge-72x72.png',
    vibrate: [200, 100, 200],
    data: {
      dateOfArrival: Date.now(),
      primaryKey: 1
    },
    actions: [
      {
        action: 'explore',
        title: 'Voir',
        icon: '/icons/checkmark.png'
      },
      {
        action: 'close',
        title: 'Fermer',
        icon: '/icons/xmark.png'
      }
    ]
  };

  if (event.data) {
    const data = event.data.json();
    options.body = data.body || options.body;
    options.data = { ...options.data, ...data };
  }

  event.waitUntil(
    self.registration.showNotification('BarStock Wise', options)
  );
});

// Gestion des clics sur les notifications
self.addEventListener('notificationclick', event => {
  console.log('Service Worker: Clic sur notification', event.action);
  
  event.notification.close();

  if (event.action === 'explore') {
    // Ouvrir l'application
    event.waitUntil(
      clients.openWindow('/')
    );
  } else if (event.action === 'close') {
    // Fermer la notification (déjà fait ci-dessus)
    console.log('Notification fermée');
  } else {
    // Clic sur la notification elle-même
    event.waitUntil(
      clients.openWindow('/')
    );
  }
});

// Synchronisation en arrière-plan
self.addEventListener('sync', event => {
  console.log('Service Worker: Synchronisation en arrière-plan', event.tag);
  
  if (event.tag === 'background-sync') {
    event.waitUntil(
      syncData()
    );
  }
});

// Fonction de synchronisation des données
async function syncData() {
  try {
    console.log('Service Worker: Début synchronisation données');
    
    // Ici, vous pourriez synchroniser les données locales avec le serveur
    // Par exemple, envoyer les données stockées localement pendant la période hors-ligne
    
    const cache = await caches.open(CACHE_NAME);
    const requests = await cache.keys();
    
    console.log(`Service Worker: ${requests.length} requêtes en cache`);
    
    // Logique de synchronisation personnalisée ici
    
    console.log('Service Worker: Synchronisation terminée');
  } catch (error) {
    console.error('Service Worker: Erreur synchronisation', error);
  }
}

// Gestion des erreurs globales
self.addEventListener('error', event => {
  console.error('Service Worker: Erreur globale', event.error);
});

self.addEventListener('unhandledrejection', event => {
  console.error('Service Worker: Promise rejetée', event.reason);
});

console.log('Service Worker: Chargé et prêt');
