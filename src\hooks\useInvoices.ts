import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import axios from 'axios';
import { useAuth } from '@/contexts/AuthContextBackend';

// Types pour les factures
export interface InvoiceItem {
  id: string;
  product_name: string;
  quantity: number;
  unit_price: number;
  total_price: number;
}

export interface InvoiceData {
  id: string;
  invoice_number: string;
  customer_name?: string;
  customer_email?: string;
  customer_phone?: string;
  customer_address?: string;
  table_number?: string;
  items: InvoiceItem[];
  subtotal: number;
  tax_amount: number;
  discount_amount: number;
  total_amount: number;
  payment_method: string;
  status: string;
  created_at: string;
  server: string;
  notes?: string;
}

export interface CreateInvoiceData {
  customer_name?: string;
  customer_email?: string;
  customer_phone?: string;
  customer_address?: string;
  table_number?: string;
  items: {
    product: string;
    quantity: number;
    unit_price: number;
  }[];
  tax_amount?: number;
  discount_amount?: number;
  payment_method: string;
  notes?: string;
}

// API functions
const fetchInvoices = async (): Promise<InvoiceData[]> => {
  const response = await axios.get('/api/invoices/');
  return response.data.results || response.data;
};

const fetchInvoice = async (id: string): Promise<InvoiceData> => {
  const response = await axios.get(`/api/invoices/${id}/`);
  return response.data;
};

const createInvoice = async (data: CreateInvoiceData): Promise<InvoiceData> => {
  const response = await axios.post('/api/invoices/', data);
  return response.data;
};

const updateInvoice = async ({ id, data }: { id: string; data: Partial<CreateInvoiceData> }): Promise<InvoiceData> => {
  const response = await axios.patch(`/api/invoices/${id}/`, data);
  return response.data;
};

const deleteInvoice = async (id: string): Promise<void> => {
  await axios.delete(`/api/invoices/${id}/`);
};

// Hooks
export const useInvoices = () => {
  return useQuery({
    queryKey: ['invoices'],
    queryFn: fetchInvoices,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

export const useInvoice = (id: string) => {
  return useQuery({
    queryKey: ['invoices', id],
    queryFn: () => fetchInvoice(id),
    enabled: !!id,
  });
};

export const useCreateInvoice = () => {
  const queryClient = useQueryClient();
  const { user } = useAuth();

  return useMutation({
    mutationFn: createInvoice,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['invoices'] });
    },
  });
};

export const useUpdateInvoice = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: updateInvoice,
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['invoices'] });
      queryClient.invalidateQueries({ queryKey: ['invoices', data.id] });
    },
  });
};

export const useDeleteInvoice = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: deleteInvoice,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['invoices'] });
    },
  });
};

// Utility functions
export const generateInvoiceNumber = (): string => {
  const timestamp = new Date().toISOString().slice(0, 19).replace(/[-:]/g, '').replace('T', '-');
  const randomId = Math.random().toString(36).substring(2, 8).toUpperCase();
  return `INV-${timestamp}-${randomId}`;
};

export const calculateInvoiceTotals = (items: CreateInvoiceData['items'], taxAmount = 0, discountAmount = 0) => {
  const subtotal = items.reduce((sum, item) => sum + (item.quantity * item.unit_price), 0);
  const total = subtotal + taxAmount - discountAmount;
  
  return {
    subtotal,
    tax_amount: taxAmount,
    discount_amount: discountAmount,
    total_amount: total,
  };
}; 