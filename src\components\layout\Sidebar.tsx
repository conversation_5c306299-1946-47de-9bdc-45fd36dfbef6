import React from 'react';
import { useAuth } from '@/contexts/AuthContextBackend';
import { usePermissions, ProtectedComponent } from '@/contexts/PermissionsContext';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import {
  BarChart3,
  Package,
  ShoppingCart,
  TrendingUp,
  Users,
  Settings,
  Wine,
  FileText,
  CreditCard,
  History,
  HelpCircle,
  Truck,
  Coffee,
  Bell,
  Calendar,
  LogOut
} from 'lucide-react';
import { useLocation, useNavigate } from 'react-router-dom';

interface SidebarProps {
  collapsed: boolean;
  onCollapse: (collapsed: boolean) => void;
}

const Sidebar: React.FC<SidebarProps> = ({ collapsed, onCollapse }) => {
  const { user, logout } = useAuth();
  const { userRole } = usePermissions();
  const location = useLocation();
  const navigate = useNavigate();

  const menuItems = [
    {
      title: 'Tableau de Bord',
      icon: BarChart3,
      path: '/dashboard',
      roles: ['Admin', 'Gérant', 'Serveur']
    },
    {
      title: 'Produits',
      icon: Package,
      path: '/products',
      roles: ['Admin', 'Gérant']
    },
    {
      title: 'Ventes',
      icon: ShoppingCart,
      path: '/sales',
      roles: ['Admin', 'Gérant', 'Serveur']
    },
    {
      title: 'Historique Ventes',
      icon: History,
      path: '/sales-history',
      roles: ['Admin', 'Gérant']
    },
    {
      title: 'Factures',
      icon: FileText,
      path: '/invoices',
      roles: ['Admin', 'Gérant']
    },
    {
      title: 'Stocks',
      icon: TrendingUp,
      path: '/stocks',
      roles: ['Admin', 'Gérant']
    },
    {
      title: 'Approvisionnements',
      icon: Package,
      path: '/supplies',
      roles: ['Admin', 'Gérant']
    },
    {
      title: 'Dépenses',
      icon: CreditCard,
      path: '/expenses',
      roles: ['Admin', 'Gérant']
    },
    {
      title: 'Fournisseurs',
      icon: Truck,
      path: '/suppliers',
      roles: ['Admin', 'Gérant']
    },
    {
      title: 'Tables',
      icon: Coffee,
      path: '/tables',
      roles: ['Admin', 'Gérant', 'Serveur']
    },
    {
      title: 'Alertes',
      icon: Bell,
      path: '/alerts',
      roles: ['Admin', 'Gérant']
    },
    {
      title: 'Rapport Journalier',
      icon: Calendar,
      path: '/daily-report',
      roles: ['Admin', 'Gérant']
    },
    {
      title: 'Analytics & IA',
      icon: TrendingUp,
      path: '/analytics',
      roles: ['Admin', 'Gérant']
    },
    {
      title: 'Rapports',
      icon: FileText,
      path: '/reports',
      roles: ['Admin', 'Gérant']
    },
    {
      title: 'Utilisateurs',
      icon: Users,
      path: '/users',
      roles: ['Admin']
    },
    {
      title: 'Paramètres',
      icon: Settings,
      path: '/settings',
      roles: ['Admin']
    },
    {
      title: 'Aide',
      icon: HelpCircle,
      path: '/help',
      roles: ['Admin', 'Gérant', 'Serveur']
    }
  ];

  const filteredMenuItems = menuItems.filter(item =>
    item.roles.includes(userRole)
  );

  const handleLogout = () => {
    logout();
    navigate('/login');
  };

  return (
    <div className={cn(
      "flex flex-col h-full bg-card border-r border-border transition-all duration-300",
      collapsed ? "w-16" : "w-64"
    )}>
      {/* Header */}
      <div className="p-4 border-b border-border">
        <div className="flex items-center gap-3">
          <div className="w-8 h-8 bg-gradient-to-br from-primary to-accent rounded-lg flex items-center justify-center">
            <Wine className="w-5 h-5 text-primary-foreground" />
          </div>
          {!collapsed && (
            <div>
              <h2 className="font-bold text-lg text-foreground">BarStockWise</h2>
              <p className="text-xs text-muted-foreground">{user?.role}</p>
            </div>
          )}
        </div>
      </div>

      {/* Menu Items */}
      <nav className="flex-1 p-2 space-y-1">
        {filteredMenuItems.map((item) => {
          const Icon = item.icon;
          const isActive = location.pathname === item.path;
          
          return (
            <Button
              key={item.path}
              variant={isActive ? "secondary" : "ghost"}
              className={cn(
                "w-full justify-start gap-3 h-10",
                collapsed && "justify-center px-2",
                isActive && "bg-primary/10 text-primary border-primary/20"
              )}
              onClick={() => navigate(item.path)}
            >
              <Icon className="w-5 h-5 flex-shrink-0" />
              {!collapsed && <span className="text-sm">{item.title}</span>}
            </Button>
          );
        })}
      </nav>

      {/* User Info & Logout */}
      <div className="p-2 border-t border-border">
        {!collapsed && user && (
          <div className="px-3 py-2 mb-2">
            <p className="text-sm font-medium text-foreground">{user.name}</p>
            <p className="text-xs text-muted-foreground">@{user.username}</p>
          </div>
        )}
        
        <Button
          variant="ghost"
          className={cn(
            "w-full justify-start gap-3 h-10 text-destructive hover:text-destructive hover:bg-destructive/10",
            collapsed && "justify-center px-2"
          )}
          onClick={handleLogout}
        >
          <LogOut className="w-5 h-5 flex-shrink-0" />
          {!collapsed && <span className="text-sm">Déconnexion</span>}
        </Button>
      </div>
    </div>
  );
};

export default Sidebar;