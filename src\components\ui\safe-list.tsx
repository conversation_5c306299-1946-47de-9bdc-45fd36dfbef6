import React, { ReactNode, useMemo } from 'react';

interface SafeListProps<T> {
  items: T[];
  renderItem: (item: T, index: number) => ReactNode;
  getKey: (item: T, index: number) => string;
  fallback?: ReactNode;
  className?: string;
}

function SafeList<T>({
  items,
  renderItem,
  getKey,
  fallback = null,
  className
}: SafeListProps<T>) {
  const safeItems = useMemo(() => {
    if (!Array.isArray(items)) return [];
    return items.filter(item => item != null);
  }, [items]);

  if (safeItems.length === 0) {
    return fallback ? <>{fallback}</> : null;
  }

  return (
    <div className={className}>
      {safeItems.map((item, index) => {
        const key = getKey(item, index);
        try {
          return (
            <React.Fragment key={key}>
              {renderItem(item, index)}
            </React.Fragment>
          );
        } catch (error) {
          console.error(`Error rendering item at index ${index}:`, error);
          return null;
        }
      })}
    </div>
  );
}

export default SafeList; 