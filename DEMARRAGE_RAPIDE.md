# 🚀 Démarrage Rapide - BarStockWise avec Backend Intégré

## 📋 Prérequis

### Logiciels Requis
- ✅ **Python 3.8+** - [Télécharger](https://python.org)
- ✅ **Node.js 16+** - [Télécharger](https://nodejs.org)
- ✅ **Git** - [Télécharger](https://git-scm.com)

### Vérification
```powershell
python --version    # Python 3.8+
node --version      # v16+
npm --version       # 8+
```

## 🎯 Démarrage en 3 Étapes

### 1. **Démarrage Automatique (Recommandé)**
```powershell
# Dans le dossier bar-stock-wise
.\start-dev.ps1
```
Choisissez l'option **3** pour démarrer backend + frontend.

### 2. **Démarrage Manuel**

**Terminal 1 - Backend Django:**
```powershell
.\start-backend.ps1
```

**Terminal 2 - Frontend React:**
```powershell
.\start-frontend.ps1
```

### 3. **Accès à l'Application**
- 🌐 **Frontend**: http://localhost:5173
- 🔧 **Backend API**: http://localhost:8000/api
- 👤 **Admin Django**: http://localhost:8000/admin

## 🔑 Comptes de Test

### Administrateur
- **Username**: `admin`
- **Password**: `admin123`
- **Accès**: Complet (tous les modules)

### Gérant
- **Username**: `gerant`
- **Password**: `gerant123`
- **Accès**: Gestion opérationnelle

### Serveur
- **Username**: `serveur1`
- **Password**: `serveur123`
- **Accès**: Ventes et consultation

## 🧪 Test de l'Intégration

### 1. **Test d'Authentification**
1. Ouvrez http://localhost:5173
2. Connectez-vous avec `admin` / `admin123`
3. Vérifiez que le dashboard s'affiche

### 2. **Test des APIs**
1. Allez dans **Produits**
2. Créez un nouveau produit
3. Vérifiez qu'il apparaît dans la liste

### 3. **Test WebSocket**
1. Ouvrez deux onglets de l'application
2. Créez une vente dans un onglet
3. Vérifiez les notifications dans l'autre onglet

### 4. **Test du Rapport Journalier**
1. Allez dans **Rapport Journalier**
2. Sélectionnez des produits
3. Saisissez des données
4. Exportez en PDF

## 🔧 Résolution de Problèmes

### Backend ne démarre pas
```powershell
# Vérifier Python
python --version

# Installer les dépendances
cd backend
pip install -r requirements.txt

# Appliquer les migrations
python manage.py migrate
```

### Frontend ne démarre pas
```powershell
# Vérifier Node.js
node --version

# Installer les dépendances
npm install

# Vérifier le fichier .env
copy .env.example .env
```

### Erreur de connexion API
1. Vérifiez que le backend est démarré sur le port 8000
2. Vérifiez le fichier `.env`:
   ```env
   VITE_API_URL=http://localhost:8000/api
   ```

### WebSocket ne fonctionne pas
1. Vérifiez la configuration WebSocket:
   ```env
   VITE_WS_URL=ws://localhost:8000/ws
   ```
2. Redémarrez le backend Django

## 📊 Fonctionnalités Testables

### ✅ Authentification
- Connexion/Déconnexion
- Gestion des sessions
- Permissions par rôle

### ✅ Gestion des Produits
- CRUD complet
- Catégories
- Prix et stocks

### ✅ Ventes
- Création de ventes
- Historique
- Résumés journaliers

### ✅ Stocks
- Mouvements de stock
- Alertes automatiques
- Inventaire temps réel

### ✅ Rapports
- Rapport journalier interactif
- Export PDF
- Calculs automatiques

### ✅ Notifications Temps Réel
- Alertes de stock
- Notifications de ventes
- Mises à jour automatiques

## 🎉 Prochaines Étapes

### Développement
1. Modifiez les composants React dans `src/`
2. Ajoutez des APIs dans `backend/`
3. Testez avec les hooks React Query

### Production
1. Configurez une base de données PostgreSQL
2. Déployez le backend sur un serveur
3. Buildez le frontend pour production

### Personnalisation
1. Ajoutez vos propres produits
2. Configurez les catégories
3. Personnalisez les rapports

## 📚 Documentation

- 📖 **Guide d'Intégration**: `INTEGRATION_BACKEND.md`
- 🔧 **Guide Fonctionnalités**: `GUIDE_FONCTIONNALITES_AVANCEES.md`
- 📊 **Guide Rapport**: `GUIDE_RAPPORT_JOURNALIER.md`
- 🧪 **Données de Démo**: `DEMO_DONNEES_REELLES.md`

## 🆘 Support

### Logs de Débogage
```javascript
// Dans la console du navigateur
localStorage.setItem('debug', 'api,websocket');
```

### Vérification de l'État
- Backend: http://localhost:8000/admin
- API: http://localhost:8000/api/
- WebSocket: Outils de développement → Network → WS

---

**🎯 Objectif**: Passer de données mock à une API complète en 5 minutes !

**✨ Résultat**: Application de gestion de bar-restaurant professionnelle avec backend Django et frontend React intégrés.
