import React, { useState, useEffect } from 'react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Check, X, Edit3 } from 'lucide-react';
import { formatCurrency } from '@/lib/currency';

interface EditableCellProps {
  value: number;
  onSave: (newValue: number) => void;
  type?: 'number' | 'currency';
  min?: number;
  max?: number;
  disabled?: boolean;
  className?: string;
}

const EditableCell: React.FC<EditableCellProps> = ({
  value,
  onSave,
  type = 'number',
  min = 0,
  max,
  disabled = false,
  className = ''
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [editValue, setEditValue] = useState(value.toString());
  const [hasError, setHasError] = useState(false);

  useEffect(() => {
    setEditValue(value.toString());
  }, [value]);

  const handleStartEdit = () => {
    if (disabled) return;
    setIsEditing(true);
    setEditValue(value.toString());
    setHasError(false);
  };

  const handleSave = () => {
    const numValue = parseFloat(editValue);
    
    // Validation
    if (isNaN(numValue)) {
      setHasError(true);
      return;
    }
    
    if (numValue < min || (max !== undefined && numValue > max)) {
      setHasError(true);
      return;
    }

    onSave(numValue);
    setIsEditing(false);
    setHasError(false);
  };

  const handleCancel = () => {
    setIsEditing(false);
    setEditValue(value.toString());
    setHasError(false);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSave();
    } else if (e.key === 'Escape') {
      handleCancel();
    }
  };

  if (isEditing) {
    return (
      <div className="flex items-center gap-1">
        <Input
          type="number"
          value={editValue}
          onChange={(e) => {
            setEditValue(e.target.value);
            setHasError(false);
          }}
          onKeyDown={handleKeyPress}
          className={`h-8 w-20 text-sm ${hasError ? 'border-red-500' : ''}`}
          min={min}
          max={max}
          autoFocus
        />
        <Button
          size="sm"
          variant="ghost"
          className="h-6 w-6 p-0"
          onClick={handleSave}
        >
          <Check className="h-3 w-3 text-green-600" />
        </Button>
        <Button
          size="sm"
          variant="ghost"
          className="h-6 w-6 p-0"
          onClick={handleCancel}
        >
          <X className="h-3 w-3 text-red-600" />
        </Button>
      </div>
    );
  }

  return (
    <div 
      className={`group flex items-center gap-2 cursor-pointer hover:bg-muted/50 rounded px-2 py-1 ${className} ${
        disabled ? 'cursor-not-allowed opacity-50' : ''
      }`}
      onClick={handleStartEdit}
    >
      <span className="font-medium">
        {type === 'currency' ? formatCurrency(value) : value}
      </span>
      {!disabled && (
        <Edit3 className="h-3 w-3 opacity-0 group-hover:opacity-50 transition-opacity" />
      )}
    </div>
  );
};

export default EditableCell;
