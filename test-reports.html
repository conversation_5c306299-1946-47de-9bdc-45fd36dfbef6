<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test - Génération de Rapports BarStock Wise</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f8f9fa;
            color: #333;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 8px;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section h2 {
            color: #667eea;
            border-bottom: 2px solid #e9ecef;
            padding-bottom: 10px;
        }
        .feature-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .feature-item {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
            border-left: 4px solid #28a745;
        }
        .feature-item.warning {
            border-left-color: #ffc107;
        }
        .feature-item.info {
            border-left-color: #17a2b8;
        }
        .demo-data {
            background: #e7f3ff;
            padding: 15px;
            border-radius: 6px;
            margin: 15px 0;
        }
        .demo-data h4 {
            margin-top: 0;
            color: #0066cc;
        }
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
        }
        .data-table th, .data-table td {
            border: 1px solid #dee2e6;
            padding: 8px 12px;
            text-align: left;
        }
        .data-table th {
            background: #f8f9fa;
            font-weight: 600;
        }
        .status-normal { color: #28a745; font-weight: bold; }
        .status-warning { color: #ffc107; font-weight: bold; }
        .status-danger { color: #dc3545; font-weight: bold; }
        .btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            margin: 5px;
            text-decoration: none;
            display: inline-block;
        }
        .btn:hover {
            background: #5a6fd8;
        }
        .btn-success {
            background: #28a745;
        }
        .btn-warning {
            background: #ffc107;
            color: #333;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧪 Test - Génération de Rapports</h1>
            <p>BarStock Wise - Système de Gestion</p>
            <p>Vérification des fonctionnalités de génération de rapports avec données structurées</p>
        </div>

        <div class="test-section">
            <h2>✅ Fonctionnalités Implémentées</h2>
            <div class="feature-list">
                <div class="feature-item">
                    <strong>📊 Rapports de Ventes</strong>
                    <p>Génération avec données réelles de la base de données + fallback avec données de démonstration réalistes</p>
                </div>
                <div class="feature-item">
                    <strong>📦 Rapports de Stock</strong>
                    <p>État actuel des stocks avec statuts (Normal, Stock faible, Rupture) et valeurs calculées</p>
                </div>
                <div class="feature-item">
                    <strong>💰 Rapports de Dépenses</strong>
                    <p>Suivi des dépenses par catégorie avec approbations et calculs de moyennes</p>
                </div>
                <div class="feature-item info">
                    <strong>🖨️ Export PDF</strong>
                    <p>Génération PDF professionnelle avec jsPDF et autoTable, mise en forme complète</p>
                </div>
                <div class="feature-item info">
                    <strong>📈 Export Excel/CSV</strong>
                    <p>Export des données en format CSV pour analyse dans Excel ou autres outils</p>
                </div>
                <div class="feature-item warning">
                    <strong>👁️ Aperçu HTML</strong>
                    <p>Prévisualisation du rapport dans une nouvelle fenêtre avec impression directe</p>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h2>📋 Exemple de Données de Rapport de Ventes</h2>
            <div class="demo-data">
                <h4>Résumé Exécutif</h4>
                <p><strong>Total des ventes:</strong> 85 000 BIF | <strong>Nombre de commandes:</strong> 3 | <strong>Panier moyen:</strong> 28 333 BIF</p>
            </div>
            <table class="data-table">
                <thead>
                    <tr>
                        <th>Date</th>
                        <th>N° Commande</th>
                        <th>Table</th>
                        <th>Articles</th>
                        <th>Serveur</th>
                        <th>Montant</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>05/08/2025</td>
                        <td>#DEMO-001</td>
                        <td>Table 5</td>
                        <td>3</td>
                        <td>Jean Ndayishimiye</td>
                        <td class="status-normal">25 000 BIF</td>
                    </tr>
                    <tr>
                        <td>05/08/2025</td>
                        <td>#DEMO-002</td>
                        <td>Table 3</td>
                        <td>2</td>
                        <td>Marie Nzeyimana</td>
                        <td class="status-normal">18 000 BIF</td>
                    </tr>
                    <tr>
                        <td>05/08/2025</td>
                        <td>#DEMO-003</td>
                        <td>Emporter</td>
                        <td>5</td>
                        <td>Paul Nkurunziza</td>
                        <td class="status-normal">42 000 BIF</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="test-section">
            <h2>📦 Exemple de Données de Rapport de Stock</h2>
            <div class="demo-data">
                <h4>Résumé Exécutif</h4>
                <p><strong>Total produits:</strong> 4 | <strong>Stock faible:</strong> 1 | <strong>Ruptures:</strong> 1 | <strong>Valeur totale:</strong> 555 000 BIF</p>
            </div>
            <table class="data-table">
                <thead>
                    <tr>
                        <th>Produit</th>
                        <th>Catégorie</th>
                        <th>Stock Actuel</th>
                        <th>Seuil Min.</th>
                        <th>Valeur</th>
                        <th>Statut</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>Primus 72cl</td>
                        <td>Bières</td>
                        <td>15</td>
                        <td>20</td>
                        <td>45 000 BIF</td>
                        <td class="status-warning">Stock faible</td>
                    </tr>
                    <tr>
                        <td>Coca-Cola 50cl</td>
                        <td>Boissons gazeuses</td>
                        <td>0</td>
                        <td>10</td>
                        <td>0 BIF</td>
                        <td class="status-danger">Rupture</td>
                    </tr>
                    <tr>
                        <td>Filet de bœuf</td>
                        <td>Viandes</td>
                        <td>25</td>
                        <td>15</td>
                        <td>375 000 BIF</td>
                        <td class="status-normal">Normal</td>
                    </tr>
                    <tr>
                        <td>Amstel 50cl</td>
                        <td>Bières</td>
                        <td>45</td>
                        <td>30</td>
                        <td>135 000 BIF</td>
                        <td class="status-normal">Normal</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="test-section">
            <h2>🎯 Instructions de Test</h2>
            <div class="feature-list">
                <div class="feature-item">
                    <strong>1. Accéder à la page Reports</strong>
                    <p>Aller sur <code>http://localhost:8080/reports</code></p>
                </div>
                <div class="feature-item">
                    <strong>2. Sélectionner un type de rapport</strong>
                    <p>Choisir entre Ventes, Stock, ou Dépenses</p>
                </div>
                <div class="feature-item">
                    <strong>3. Cliquer sur "Générer le Rapport"</strong>
                    <p>Une nouvelle fenêtre s'ouvrira avec l'aperçu HTML</p>
                </div>
                <div class="feature-item">
                    <strong>4. Tester l'export PDF</strong>
                    <p>Cliquer sur le bouton "PDF" pour télécharger</p>
                </div>
                <div class="feature-item">
                    <strong>5. Tester l'export Excel</strong>
                    <p>Cliquer sur le bouton "Excel" pour télécharger le CSV</p>
                </div>
                <div class="feature-item warning">
                    <strong>6. Vérifier les données</strong>
                    <p>Les rapports doivent contenir des données réelles ou de démonstration réalistes</p>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h2>🔗 Actions de Test</h2>
            <p>Utilisez ces liens pour tester directement :</p>
            <a href="http://localhost:8080/reports" class="btn" target="_blank">🚀 Ouvrir la page Reports</a>
            <a href="http://localhost:8080/sales" class="btn btn-success" target="_blank">📊 Page Ventes (données source)</a>
            <a href="http://localhost:8080/inventory" class="btn btn-warning" target="_blank">📦 Page Inventaire (données source)</a>
        </div>

        <div class="test-section">
            <h2>✅ Critères de Validation</h2>
            <div class="feature-list">
                <div class="feature-item">
                    <strong>✓ Données structurées</strong>
                    <p>Les rapports contiennent des données bien organisées avec en-têtes, résumés et détails</p>
                </div>
                <div class="feature-item">
                    <strong>✓ Mise en forme professionnelle</strong>
                    <p>Design cohérent avec couleurs, typographie et mise en page soignées</p>
                </div>
                <div class="feature-item">
                    <strong>✓ Fonctionnalités complètes</strong>
                    <p>Aperçu, export PDF, export Excel, impression directe</p>
                </div>
                <div class="feature-item">
                    <strong>✓ Données réalistes</strong>
                    <p>Fallback avec données de démonstration crédibles si pas de données réelles</p>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
