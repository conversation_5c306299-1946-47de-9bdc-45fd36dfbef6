# 🔧 Guide de Configuration - BarStockWise

## 📋 Prérequis

### Services Requis
- **Redis** : Pour WebSockets et Celery
- **Serveur Email** : Gmail, Outlook, ou serveur SMTP local
- **Provider SMS** : Africa's Talking (recommandé pour l'Afrique) ou Twilio

### Installation Redis

#### Ubuntu/Debian
```bash
sudo apt update
sudo apt install redis-server
sudo systemctl start redis-server
sudo systemctl enable redis-server
```

#### macOS
```bash
brew install redis
brew services start redis
```

#### Windows
Télécharger depuis : https://redis.io/download

## 🔐 Configuration des Notifications

### 1. Configuration Email (Gmail)

1. **Activer l'authentification à 2 facteurs** sur votre compte Gmail
2. **Générer un mot de passe d'application** :
   - Aller dans Paramètres Google → Sécurité
   - Authentification à 2 facteurs → Mots de passe des applications
   - Générer un mot de passe pour "Mail"

3. **Modifier `settings.py`** :
```python
EMAIL_HOST_USER = '<EMAIL>'
EMAIL_HOST_PASSWORD = 'votre-mot-de-passe-application'  # 16 caractères
DEFAULT_FROM_EMAIL = 'BarStockWise <<EMAIL>>'
```

### 2. Configuration SMS - Africa's Talking (Recommandé)

1. **Créer un compte** sur https://africastalking.com
2. **Obtenir vos identifiants** :
   - Username (sandbox ou live)
   - API Key

3. **Modifier `settings.py`** :
```python
SMS_PROVIDER = 'africastalking'
AFRICASTALKING_USERNAME = 'votre-username'
AFRICASTALKING_API_KEY = 'votre-api-key'
```

### 3. Configuration SMS - Twilio (Alternative)

1. **Créer un compte** sur https://twilio.com
2. **Obtenir vos identifiants** :
   - Account SID
   - Auth Token
   - Numéro de téléphone Twilio

3. **Modifier `settings.py`** :
```python
SMS_PROVIDER = 'twilio'
TWILIO_ACCOUNT_SID = 'votre-account-sid'
TWILIO_AUTH_TOKEN = 'votre-auth-token'
TWILIO_PHONE_NUMBER = '+**********'
```

### 4. Configuration Restaurant

Modifier dans `settings.py` :
```python
RESTAURANT_NAME = 'Nom de votre restaurant'
RESTAURANT_PHONE = '+257 XX XX XX XX'
RESTAURANT_ADDRESS = 'Votre adresse complète, Bujumbura, Burundi'

STAFF_PHONE_NUMBERS = [
    '+257 XX XX XX XX',  # Manager
    '+257 XX XX XX XX',  # Chef de service
]
```

## 🚀 Démarrage des Services

### Méthode Automatique (Recommandée)
```bash
cd backend
chmod +x start-services.sh
./start-services.sh
```

### Méthode Manuelle

#### Terminal 1 - Serveur Django avec WebSockets
```bash
cd backend
pip install -r requirements-websockets.txt
daphne -b 0.0.0.0 -p 8000 barstock_api.asgi:application
```

#### Terminal 2 - Worker Celery
```bash
cd backend
celery -A barstock_api worker --loglevel=info
```

#### Terminal 3 - Scheduler Celery Beat
```bash
cd backend
celery -A barstock_api beat --loglevel=info
```

## 🧪 Tests de Configuration

### Test Email
```python
# Dans le shell Django
python manage.py shell

from core.notifications import EmailService
from sales.models import TableReservation

# Créer une réservation test ou utiliser une existante
reservation = TableReservation.objects.first()
result = EmailService.send_reservation_confirmation(reservation)
print(result)
```

### Test SMS
```python
# Dans le shell Django
from core.notifications import SMSService

result = SMSService.send_sms('+257XXXXXXXX', 'Test SMS depuis BarStockWise')
print(result)
```

### Test WebSocket
1. Ouvrir http://localhost:8000/admin/
2. Modifier le statut d'une table
3. Vérifier que les changements apparaissent en temps réel dans le frontend

## 🔍 Dépannage

### Redis non connecté
```bash
# Vérifier le statut
redis-cli ping

# Si pas de réponse, redémarrer
sudo systemctl restart redis-server
```

### Erreurs Email
- Vérifier que l'authentification 2FA est activée
- Utiliser un mot de passe d'application, pas le mot de passe principal
- Vérifier les paramètres SMTP

### Erreurs SMS
- Vérifier les identifiants API
- S'assurer que le compte a du crédit
- Vérifier le format des numéros de téléphone (+257...)

### WebSocket non connecté
- Vérifier que Daphne est utilisé au lieu de runserver
- Vérifier que Redis fonctionne
- Vérifier les CORS settings pour le frontend

## 📊 Monitoring

### Logs Celery
```bash
# Voir les tâches en cours
celery -A barstock_api inspect active

# Voir les tâches programmées
celery -A barstock_api inspect scheduled

# Statistiques
celery -A barstock_api inspect stats
```

### Logs Django
Les logs sont visibles dans le terminal où Daphne est lancé.

### Redis Monitoring
```bash
# Connexions actives
redis-cli info clients

# Mémoire utilisée
redis-cli info memory
```

## 🔒 Sécurité en Production

### Variables d'Environnement
Créer un fichier `.env` :
```env
SECRET_KEY=votre-secret-key-django
EMAIL_HOST_PASSWORD=votre-mot-de-passe-email
AFRICASTALKING_API_KEY=votre-api-key
TWILIO_AUTH_TOKEN=votre-auth-token
```

### HTTPS
- Utiliser un certificat SSL
- Configurer `SECURE_SSL_REDIRECT = True`
- Utiliser `wss://` pour les WebSockets

### Firewall
- Ouvrir seulement les ports nécessaires (80, 443, 8000)
- Restreindre l'accès à Redis (port 6379)

## 📱 Configuration Frontend

### Variables d'Environnement
Créer `.env.local` :
```env
NEXT_PUBLIC_API_URL=http://localhost:8000/api
NEXT_PUBLIC_WS_URL=ws://localhost:8000/ws
```

### Production
```env
NEXT_PUBLIC_API_URL=https://votre-domaine.com/api
NEXT_PUBLIC_WS_URL=wss://votre-domaine.com/ws
```

## 🎯 Fonctionnalités Activées

Après cette configuration, vous aurez :

✅ **Temps Réel** : Mises à jour automatiques des tables
✅ **Notifications Email** : Confirmations et rappels automatiques
✅ **Notifications SMS** : Messages aux clients et personnel
✅ **Tâches Automatiques** : Rappels programmés, vérification des retards
✅ **Monitoring** : Historique des notifications, statistiques
✅ **Interface Admin** : Gestion complète depuis l'admin Django

## 📞 Support

Pour toute question sur la configuration :
1. Vérifier les logs d'erreur
2. Tester chaque service individuellement
3. Consulter la documentation des providers (Africa's Talking, Twilio)
4. Vérifier les paramètres réseau et firewall
