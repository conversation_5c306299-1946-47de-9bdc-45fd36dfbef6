import React, { useState, useEffect } from 'react';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

interface SafeSelectProps {
  value: string;
  onValueChange: (value: string) => void;
  placeholder?: string;
  options: Array<{ value: string; label: string }>;
  disabled?: boolean;
  className?: string;
}

const SafeSelect: React.FC<SafeSelectProps> = ({
  value,
  onValueChange,
  placeholder = "Sélectionner...",
  options,
  disabled = false,
  className
}) => {
  const [isMounted, setIsMounted] = useState(false);
  const [safeValue, setSafeValue] = useState(value);

  // Gestion du montage/démontage sécurisé
  useEffect(() => {
    setIsMounted(true);
    return () => setIsMounted(false);
  }, []);

  // Synchronisation de la valeur
  useEffect(() => {
    if (isMounted) {
      setSafeValue(value);
    }
  }, [value, isMounted]);

  // Vérification que la valeur sélectionnée existe dans les options
  const validValue = options.some(option => option.value === safeValue) ? safeValue : '';

  const handleValueChange = (newValue: string) => {
    if (isMounted) {
      setSafeValue(newValue);
      onValueChange(newValue);
    }
  };

  // Si le composant n'est pas monté, retourner un placeholder
  if (!isMounted) {
    return (
      <div className={`flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm text-muted-foreground ${className}`}>
        {placeholder}
      </div>
    );
  }

  return (
    <Select 
      value={validValue} 
      onValueChange={handleValueChange}
      disabled={disabled}
    >
      <SelectTrigger className={className}>
        <SelectValue placeholder={placeholder} />
      </SelectTrigger>
      <SelectContent>
        {options.map((option) => (
          <SelectItem key={`safe-select-${option.value}`} value={option.value}>
            {option.label}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
};

export default SafeSelect; 