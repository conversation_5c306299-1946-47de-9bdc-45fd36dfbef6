from django.urls import path, include
from rest_framework.routers import DefaultRouter
from . import views

router = DefaultRouter()
router.register(r'suppliers', views.SupplierViewSet)

urlpatterns = [
    path('', include(router.urls)),
    path('suppliers/<int:supplier_id>/purchases/', views.SupplierPurchasesView.as_view(), name='supplier-purchases'),
    path('suppliers/<int:supplier_id>/statistics/', views.SupplierStatisticsView.as_view(), name='supplier-statistics'),
]
