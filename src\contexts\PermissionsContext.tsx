import React, { createContext, useContext, ReactNode } from 'react';
import { useAuth } from './AuthContextBackend';

// Types de permissions détaillées
export type Permission = 
  // Gestion des utilisateurs
  | 'users.create' | 'users.read' | 'users.update' | 'users.delete'
  // Gestion des produits
  | 'products.create' | 'products.read' | 'products.update' | 'products.delete'
  // Gestion des stocks
  | 'stocks.create' | 'stocks.read' | 'stocks.update' | 'stocks.delete'
  // Gestion des ventes
  | 'sales.create' | 'sales.read' | 'sales.update' | 'sales.delete'
  // Gestion des approvisionnements
  | 'supplies.create' | 'supplies.read' | 'supplies.update' | 'supplies.delete'
  // Gestion des fournisseurs
  | 'suppliers.create' | 'suppliers.read' | 'suppliers.update' | 'suppliers.delete'
  // Gestion des tables
  | 'tables.create' | 'tables.read' | 'tables.update' | 'tables.delete'
  // Gestion des dépenses
  | 'expenses.create' | 'expenses.read' | 'expenses.update' | 'expenses.delete'
  // Rapports et analyses
  | 'reports.create' | 'reports.read' | 'reports.export' | 'reports.advanced' | 'reports.validate'
  // Alertes
  | 'alerts.read' | 'alerts.manage' | 'alerts.configure'
  // Paramètres système
  | 'settings.read' | 'settings.update' | 'settings.system'
  // Actions spéciales
  | 'data.export' | 'data.import' | 'data.backup' | 'system.admin';

export type UserRole = 'Admin' | 'Gérant' | 'Serveur';

// Mapping des rôles backend vers frontend
const mapBackendRoleToFrontend = (backendRole: string): UserRole => {
  switch (backendRole?.toLowerCase()) {
    case 'admin':
      return 'Admin';
    case 'gerant':
      return 'Gérant';
    case 'serveur':
      return 'Serveur';
    default:
      return 'Serveur'; // Rôle par défaut
  }
};

// Matrice des permissions par rôle selon le cahier des charges
const ROLE_PERMISSIONS: Record<UserRole, Permission[]> = {
  Admin: [
    // Accès complet à tout
    'users.create', 'users.read', 'users.update', 'users.delete',
    'products.create', 'products.read', 'products.update', 'products.delete',
    'stocks.create', 'stocks.read', 'stocks.update', 'stocks.delete',
    'sales.create', 'sales.read', 'sales.update', 'sales.delete',
    'supplies.create', 'supplies.read', 'supplies.update', 'supplies.delete',
    'suppliers.create', 'suppliers.read', 'suppliers.update', 'suppliers.delete',
    'tables.create', 'tables.read', 'tables.update', 'tables.delete',
    'expenses.create', 'expenses.read', 'expenses.update', 'expenses.delete',
    'reports.create', 'reports.read', 'reports.export', 'reports.advanced', 'reports.validate',
    'alerts.read', 'alerts.manage', 'alerts.configure',
    'settings.read', 'settings.update', 'settings.system',
    'data.export', 'data.import', 'data.backup', 'system.admin'
  ],
  Gérant: [
    // Gestion complète sauf création/suppression d'utilisateurs et paramètres système
    'users.read', 'users.update', // Pas de create/delete
    'products.create', 'products.read', 'products.update', 'products.delete',
    'stocks.create', 'stocks.read', 'stocks.update', 'stocks.delete',
    'sales.create', 'sales.read', 'sales.update', // Pas de delete
    'supplies.create', 'supplies.read', 'supplies.update', 'supplies.delete',
    'suppliers.create', 'suppliers.read', 'suppliers.update', 'suppliers.delete',
    'tables.create', 'tables.read', 'tables.update', 'tables.delete',
    'expenses.create', 'expenses.read', 'expenses.update', 'expenses.delete',
    'reports.create', 'reports.read', 'reports.export', 'reports.advanced', 'reports.validate',
    'alerts.read', 'alerts.manage', 'alerts.configure',
    'settings.read', 'settings.update', // Pas de settings.system
    'data.export', 'data.import' // Pas de backup ni system.admin
  ],
  Serveur: [
    // Accès limité aux ventes et consultation
    'products.read',
    'stocks.read',
    'sales.create', 'sales.read', // Seulement création et lecture des ventes
    'tables.read', 'tables.update', // Gestion des tables (occupation)
    'reports.read', // Rapports de base seulement
    'alerts.read' // Consultation des alertes seulement
  ]
};

// Fonctions utilitaires pour les permissions
export const getPermissionsForRole = (role: UserRole): Permission[] => {
  return ROLE_PERMISSIONS[role] || [];
};

export const hasPermission = (userRole: UserRole, permission: Permission): boolean => {
  const rolePermissions = getPermissionsForRole(userRole);
  return rolePermissions.includes(permission);
};

export const hasAnyPermission = (userRole: UserRole, permissions: Permission[]): boolean => {
  return permissions.some(permission => hasPermission(userRole, permission));
};

export const hasAllPermissions = (userRole: UserRole, permissions: Permission[]): boolean => {
  return permissions.every(permission => hasPermission(userRole, permission));
};

// Fonctions de vérification par module
export const canManageUsers = (role: UserRole): boolean => {
  return hasPermission(role, 'users.create') || hasPermission(role, 'users.delete');
};

export const canManageProducts = (role: UserRole): boolean => {
  return hasPermission(role, 'products.create');
};

export const canDeleteProducts = (role: UserRole): boolean => {
  return hasPermission(role, 'products.delete');
};

export const canManageStocks = (role: UserRole): boolean => {
  return hasPermission(role, 'stocks.update');
};

export const canMakeSales = (role: UserRole): boolean => {
  return hasPermission(role, 'sales.create');
};

export const canManageSupplies = (role: UserRole): boolean => {
  return hasPermission(role, 'supplies.create');
};

export const canManageExpenses = (role: UserRole): boolean => {
  return hasPermission(role, 'expenses.create');
};

export const canExportReports = (role: UserRole): boolean => {
  return hasPermission(role, 'reports.export');
};

export const canConfigureAlerts = (role: UserRole): boolean => {
  return hasPermission(role, 'alerts.configure');
};

export const canAccessSystemSettings = (role: UserRole): boolean => {
  return hasPermission(role, 'settings.system');
};

export const canViewReports = (role: UserRole): boolean => {
  return hasPermission(role, 'reports.read');
};

export const canManageReports = (role: UserRole): boolean => {
  return hasPermission(role, 'reports.create') || hasPermission(role, 'reports.validate');
};

export const isAdmin = (role: UserRole): boolean => {
  return role === 'Admin';
};

export const isManager = (role: UserRole): boolean => {
  return role === 'Gérant';
};

export const isServer = (role: UserRole): boolean => {
  return role === 'Serveur';
};

// Context
interface PermissionsContextType {
  userRole: UserRole;
  hasPermission: (permission: Permission) => boolean;
  hasAnyPermission: (permissions: Permission[]) => boolean;
  hasAllPermissions: (permissions: Permission[]) => boolean;
  canManageUsers: () => boolean;
  canManageProducts: () => boolean;
  canManageStocks: () => boolean;
  canMakeSales: () => boolean;
  canManageSupplies: () => boolean;
  canManageExpenses: () => boolean;
  canExportReports: () => boolean;
  canConfigureAlerts: () => boolean;
  canAccessSystemSettings: () => boolean;
  canViewReports: () => boolean;
  canManageReports: () => boolean;
  isAdmin: () => boolean;
  isManager: () => boolean;
  isServer: () => boolean;
}

const PermissionsContext = createContext<PermissionsContextType | undefined>(undefined);

interface PermissionsProviderProps {
  children: ReactNode;
}

export const PermissionsProvider: React.FC<PermissionsProviderProps> = ({ children }) => {
  const { user } = useAuth();
  const userRole = user?.role ? mapBackendRoleToFrontend(user.role) : 'Serveur';

  const contextValue: PermissionsContextType = {
    userRole,
    hasPermission: (permission: Permission) => hasPermission(userRole, permission),
    hasAnyPermission: (permissions: Permission[]) => hasAnyPermission(userRole, permissions),
    hasAllPermissions: (permissions: Permission[]) => hasAllPermissions(userRole, permissions),
    canManageUsers: () => canManageUsers(userRole),
    canManageProducts: () => canManageProducts(userRole),
    canManageStocks: () => canManageStocks(userRole),
    canMakeSales: () => canMakeSales(userRole),
    canManageSupplies: () => canManageSupplies(userRole),
    canManageExpenses: () => canManageExpenses(userRole),
    canExportReports: () => canExportReports(userRole),
    canConfigureAlerts: () => canConfigureAlerts(userRole),
    canAccessSystemSettings: () => canAccessSystemSettings(userRole),
    canViewReports: () => canViewReports(userRole),
    canManageReports: () => canManageReports(userRole),
    isAdmin: () => isAdmin(userRole),
    isManager: () => isManager(userRole),
    isServer: () => isServer(userRole)
  };

  return (
    <PermissionsContext.Provider value={contextValue}>
      {children}
    </PermissionsContext.Provider>
  );
};

export const usePermissions = (): PermissionsContextType => {
  const context = useContext(PermissionsContext);
  if (context === undefined) {
    throw new Error('usePermissions must be used within a PermissionsProvider');
  }
  return context;
};

// Composant HOC pour protéger les composants selon les permissions
interface ProtectedComponentProps {
  children: ReactNode;
  permission?: Permission;
  permissions?: Permission[];
  requireAll?: boolean;
  fallback?: ReactNode;
  roles?: UserRole[];
}

export const ProtectedComponent: React.FC<ProtectedComponentProps> = ({
  children,
  permission,
  permissions = [],
  requireAll = false,
  fallback = null,
  roles = []
}) => {
  const { userRole, hasPermission, hasAnyPermission, hasAllPermissions } = usePermissions();

  // Vérification par rôle
  if (roles.length > 0 && !roles.includes(userRole)) {
    return <>{fallback}</>;
  }

  // Vérification par permission unique
  if (permission && !hasPermission(permission)) {
    return <>{fallback}</>;
  }

  // Vérification par permissions multiples
  if (permissions.length > 0) {
    const hasRequiredPermissions = requireAll 
      ? hasAllPermissions(permissions)
      : hasAnyPermission(permissions);
    
    if (!hasRequiredPermissions) {
      return <>{fallback}</>;
    }
  }

  return <>{children}</>;
};

// Hook pour vérifier les permissions de manière conditionnelle
export const useConditionalPermissions = () => {
  const permissions = usePermissions();
  
  return {
    ...permissions,
    // Fonctions utilitaires supplémentaires
    canDelete: (module: string) => {
      const permission = `${module}.delete` as Permission;
      return permissions.hasPermission(permission);
    },
    canCreate: (module: string) => {
      const permission = `${module}.create` as Permission;
      return permissions.hasPermission(permission);
    },
    canUpdate: (module: string) => {
      const permission = `${module}.update` as Permission;
      return permissions.hasPermission(permission);
    },
    canRead: (module: string) => {
      const permission = `${module}.read` as Permission;
      return permissions.hasPermission(permission);
    }
  };
};
