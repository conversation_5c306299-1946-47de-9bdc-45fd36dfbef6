import React from 'react';

const Login = () => {
  console.log('Login component rendering');
  console.log('React object:', React);
  console.log('React.useState:', React.useState);

  return (
    <div style={{ padding: '20px', textAlign: 'center' }}>
      <h1>Login Page - Simple Version</h1>
      <p>Testing basic React component without hooks</p>
      <form>
        <div style={{ margin: '10px' }}>
          <input type="text" placeholder="Username" />
        </div>
        <div style={{ margin: '10px' }}>
          <input type="password" placeholder="Password" />
        </div>
        <div style={{ margin: '10px' }}>
          <button type="submit">Login</button>
        </div>
      </form>
    </div>
  );
};

export default Login;