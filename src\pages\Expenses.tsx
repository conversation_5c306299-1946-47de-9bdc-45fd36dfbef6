import { useState, useMemo, FormEvent } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { formatCurrency } from '@/lib/currency';
import { 
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
  
} from '@/components/ui/table';
import { 
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Plus, Search, CreditCard, Calendar, TrendingDown, Edit, Trash2, Receipt, DollarSign, AlertTriangle } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { Skeleton } from '@/components/ui/skeleton';
import { usePermissions, ProtectedComponent } from '@/contexts/PermissionsContext';
import { useExpenses, useCreateExpense, useUpdateExpense, useDeleteExpense, useExpenseCategories } from '@/hooks/useApi';

// Interface pour les dépenses
interface Expense {
  id: string;
  title: string;
  amount: number;
  description: string;
  date: string;
  category: string;
  type?: string;
  created_by: string;
  created_at: string;
}

const Expenses = () => {
  // Hooks pour les données API
  const { data: expenses, isLoading: expensesLoading, error: expensesError } = useExpenses();
  const { data: categories } = useExpenseCategories();
  const createExpense = useCreateExpense();
  const updateExpense = useUpdateExpense();
  const deleteExpense = useDeleteExpense();

  // État local
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('Toutes');
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editingExpense, setEditingExpense] = useState<Expense | null>(null);

  const { toast } = useToast();
  const { canManageExpenses, hasPermission } = usePermissions();

  const [formData, setFormData] = useState({
    title: '',
    type: '',
    amount: '',
    description: '',
    date: new Date().toISOString().split('T')[0],
    category: ''
  });

  // Catégories dynamiques
  const expenseCategories = useMemo(() => {
    const cats = ['Toutes'];
    if (categories) {
      // Gérer différents formats de réponse de l'API
      let categoriesList = [];
      if (Array.isArray(categories)) {
        categoriesList = categories;
      } else if (categories.results && Array.isArray(categories.results)) {
        categoriesList = categories.results;
      } else if (categories.categories && Array.isArray(categories.categories)) {
        categoriesList = categories.categories;
      }

      cats.push(...categoriesList.map((cat: any) => cat.name || cat));
    }
    return cats;
  }, [categories]);

  // Filtrage des dépenses
  const filteredExpenses = useMemo(() => {
    if (!expenses) return [];

    // Gérer différents formats de réponse de l'API
    let expensesList = [];
    if (Array.isArray(expenses)) {
      expensesList = expenses;
    } else if (expenses.results && Array.isArray(expenses.results)) {
      expensesList = expenses.results;
    } else if (expenses.expenses && Array.isArray(expenses.expenses)) {
      expensesList = expenses.expenses;
    }

    return expensesList.filter((expense: Expense) => {
      const matchesSearch =
        expense.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        expense.description.toLowerCase().includes(searchTerm.toLowerCase());
      const matchesCategory = selectedCategory === 'Toutes' || expense.category === selectedCategory;
      return matchesSearch && matchesCategory;
    });
  }, [expenses, searchTerm, selectedCategory]);

  // Types de dépenses disponibles
  const expenseTypes = [
    'Achat de marchandises',
    'Salaires',
    'Loyer',
    'Électricité',
    'Eau',
    'Internet',
    'Téléphone',
    'Transport',
    'Marketing',
    'Maintenance',
    'Assurance',
    'Taxes',
    'Autres'
  ];

  const handleSubmit = async (e: FormEvent) => {
    e.preventDefault();

    try {
      const expenseData = {
        title: formData.title,
        type: formData.type,
        amount: Number(formData.amount),
        description: formData.description,
        date: formData.date,
        category: formData.category
      };

      if (editingExpense) {
        await updateExpense.mutateAsync({ id: editingExpense.id, data: expenseData });
        toast({
          title: "Dépense mise à jour",
          description: `Dépense de ${formatCurrency(Number(formData.amount))} mise à jour.`,
        });
      } else {
        await createExpense.mutateAsync(expenseData);
        toast({
          title: "Dépense ajoutée",
          description: `Dépense de ${formatCurrency(Number(formData.amount))} enregistrée.`,
        });
      }

      // Reset form
      setFormData({
        title: '',
        type: '',
        amount: '',
        description: '',
        date: new Date().toISOString().split('T')[0],
        category: ''
      });
      setEditingExpense(null);
      setIsDialogOpen(false);
    } catch (error) {
      console.error('Erreur lors de la sauvegarde:', error);
      toast({
        title: "Erreur",
        description: "Erreur lors de la sauvegarde de la dépense.",
        variant: "destructive",
      });
    }
  };

  const handleEdit = (expense: Expense) => {
    setFormData({
      title: expense.title,
      type: expense.type || '',
      amount: expense.amount.toString(),
      description: expense.description,
      date: expense.date,
      category: expense.category
    });
    setEditingExpense(expense);
    setIsDialogOpen(true);
  };

  const handleDelete = async (expenseId: string) => {
    try {
      await deleteExpense.mutateAsync(expenseId);
    } catch (error) {
      console.error('Erreur lors de la suppression:', error);
    }
  };

  const getTotalExpenses = () => filteredExpenses.reduce((sum, expense) => sum + expense.amount, 0);

  const getExpensesByCategory = () => {
    const categoryTotals: { [key: string]: number } = {};
    filteredExpenses.forEach(expense => {
      categoryTotals[expense.category] = (categoryTotals[expense.category] || 0) + expense.amount;
    });
    return Object.entries(categoryTotals).sort((a, b) => b[1] - a[1]);
  };

  const getAverageExpense = () => {
    return filteredExpenses.length > 0 ? getTotalExpenses() / filteredExpenses.length : 0;
  };

  const getCategoryBadge = (category: string) => {
    const colors: { [key: string]: string } = {
      'Personnel': 'destructive',
      'Utilities': 'secondary',
      'Cuisine': 'default',
      'Équipement': 'outline',
      'Logistique': 'secondary',
      'Hygiène': 'default'
    };
    return colors[category] || 'outline';
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Gestion des Dépenses</h1>
          <p className="text-muted-foreground">
            Enregistrez et suivez toutes les dépenses de l'établissement
          </p>
        </div>
        
        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogTrigger asChild>
            <Button className="bg-gradient-to-r from-primary to-accent">
              <Plus className="w-4 h-4 mr-2" />
              Nouvelle dépense
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[425px]">
            <DialogHeader>
              <DialogTitle>Ajouter une dépense</DialogTitle>
              <DialogDescription>
                Enregistrez une nouvelle dépense ci-dessous.
              </DialogDescription>
            </DialogHeader>
            
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="type">Type de dépense</Label>
                <Select 
                  value={formData.type} 
                  onValueChange={(value) => setFormData({...formData, type: value})}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Sélectionnez un type" />
                  </SelectTrigger>
                  <SelectContent>
                    {expenseTypes.map((type) => (
                      <SelectItem key={type} value={type}>
                        {type}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="category">Catégorie</Label>
                <Select 
                  value={formData.category} 
                  onValueChange={(value) => setFormData({...formData, category: value})}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Sélectionnez une catégorie" />
                  </SelectTrigger>
                  <SelectContent>
                    {expenseCategories.map((category) => (
                      <SelectItem key={category} value={category}>
                        {category}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="amount">Montant (BIF)</Label>
                <Input
                  id="amount"
                  type="number"
                  value={formData.amount}
                  onChange={(e) => setFormData({...formData, amount: e.target.value})}
                  placeholder="25000"
                  required
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="description">Description</Label>
                <Input
                  id="description"
                  value={formData.description}
                  onChange={(e) => setFormData({...formData, description: e.target.value})}
                  placeholder="Description de la dépense"
                  required
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="date">Date</Label>
                <Input
                  id="date"
                  type="date"
                  value={formData.date}
                  onChange={(e) => setFormData({...formData, date: e.target.value})}
                  required
                />
              </div>
              
              <Button type="submit" className="w-full">
                Enregistrer la dépense
              </Button>
            </form>
          </DialogContent>
        </Dialog>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Dépenses</CardTitle>
            <TrendingDown className="h-4 w-4 text-destructive" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-destructive">
              {formatCurrency(getTotalExpenses())}
            </div>
            <p className="text-xs text-muted-foreground">période filtrée</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Nombre de Dépenses</CardTitle>
            <CreditCard className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{filteredExpenses.length}</div>
            <p className="text-xs text-muted-foreground">transactions</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Dépense Moyenne</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatCurrency(getAverageExpense())}
            </div>
            <p className="text-xs text-muted-foreground">par transaction</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Catégorie Principale</CardTitle>
            <TrendingDown className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {getExpensesByCategory()[0]?.[0] || 'N/A'}
            </div>
            <p className="text-xs text-muted-foreground">plus coûteuse</p>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Rechercher une dépense..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <Select value={selectedCategory} onValueChange={setSelectedCategory}>
              <SelectTrigger className="w-full sm:w-[200px]">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="Toutes">Toutes les catégories</SelectItem>
                {expenseCategories.map((category) => (
                  <SelectItem key={category} value={category}>
                    {category}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Top Categories */}
      <Card>
        <CardHeader>
          <CardTitle>Dépenses par Catégorie</CardTitle>
          <CardDescription>Répartition des coûts par catégorie</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {getExpensesByCategory().slice(0, 5).map(([category, amount]) => (
              <div key={category} className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Badge variant={getCategoryBadge(category) as any}>
                    {category}
                  </Badge>
                </div>
                <div className="text-right">
                  <span className="font-bold">{formatCurrency(amount)}</span>
                  <div className="w-32 bg-muted rounded-full h-2 ml-2">
                    <div 
                      className="bg-primary h-2 rounded-full" 
                      style={{ width: `${(amount / getTotalExpenses()) * 100}%` }}
                    />
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Expenses Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CreditCard className="w-5 h-5" />
            Liste des Dépenses
          </CardTitle>
          <CardDescription>
            {filteredExpenses.length} dépense(s) trouvée(s)
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Date</TableHead>
                <TableHead>Type</TableHead>
                <TableHead>Description</TableHead>
                <TableHead>Catégorie</TableHead>
                <TableHead>Montant</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredExpenses.map((expense) => (
                <TableRow key={expense.id}>
                  <TableCell>
                    {new Date(expense.date).toLocaleDateString('fr-FR')}
                  </TableCell>
                  <TableCell className="font-medium">{expense.title}</TableCell>
                  <TableCell>{expense.description}</TableCell>
                  <TableCell>
                    <Badge variant={getCategoryBadge(expense.category) as any}>
                      {expense.category}
                    </Badge>
                  </TableCell>
                  <TableCell className="font-bold text-destructive">
                    -{formatCurrency(expense.amount)}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
};

export default Expenses;