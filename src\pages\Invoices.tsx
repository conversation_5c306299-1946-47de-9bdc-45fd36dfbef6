import React, { useState, useMemo } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  FileText,
  Calendar,
  DollarSign,
  Receipt,
  Eye,
  Download,
  Plus,
  Search,
  Filter,
  Loader2
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { formatCurrency } from '@/lib/currency';
import { useSales } from '@/hooks/useApi';
import { parseNumericValue } from '@/lib/utils/numeric';
import InvoiceGenerator from '@/components/invoice/InvoiceGenerator';
import InvoicePrinter from '@/components/invoice/InvoicePrinter';

const Invoices = () => {
  const { toast } = useToast();
  
  // Hooks pour les données API (temporairement utiliser les ventes)
  const { data: salesResponse, isLoading: invoicesLoading, error: invoicesError } = useSales();

  // États pour les filtres
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('Tous');
  const [dateFilter, setDateFilter] = useState('');

  // États pour la gestion des factures
  const [showNewInvoiceDialog, setShowNewInvoiceDialog] = useState(false);
  const [isDownloadingAll, setIsDownloadingAll] = useState(false);

  // Fonction pour télécharger toutes les factures
  const downloadAllInvoices = async () => {
    if (filteredInvoices.length === 0) {
      toast({
        title: "Aucune facture",
        description: "Aucune facture à télécharger avec les filtres actuels.",
        variant: "destructive",
      });
      return;
    }

    setIsDownloadingAll(true);

    try {
      toast({
        title: "Export en cours",
        description: `Téléchargement de ${filteredInvoices.length} facture(s) en PDF...`,
      });

      // Créer un délai pour simuler le téléchargement
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Ici, vous pourriez implémenter la logique réelle de téléchargement
      // Par exemple, générer un ZIP avec toutes les factures PDF

      toast({
        title: "Téléchargement terminé",
        description: `${filteredInvoices.length} facture(s) téléchargée(s) avec succès.`,
      });
    } catch (error) {
      toast({
        title: "Erreur de téléchargement",
        description: "Impossible de télécharger les factures.",
        variant: "destructive",
      });
    } finally {
      setIsDownloadingAll(false);
    }
  };

  // Fonction pour créer une nouvelle facture
  const createNewInvoice = () => {
    setShowNewInvoiceDialog(true);
  };
  
  // Extraire les données de la réponse paginée et les convertir en format facture
  const invoices = useMemo(() => {
    if (!salesResponse) return [];
    const salesList = salesResponse?.results || (Array.isArray(salesResponse) ? salesResponse : []);

    // Debug: Afficher les données brutes
    console.log('Sales Response:', salesResponse);
    console.log('Sales List:', salesList);

    // Convertir les ventes en format facture temporaire
    return salesList.map((sale: any) => {
      // Debug: Afficher chaque vente
      console.log(`Sale ${sale.reference || sale.id}:`, sale);
      console.log(`Items for ${sale.reference || sale.id}:`, sale.items || sale.sale_items || []);

      return {
        id: sale.id,
        invoice_number: sale.reference || `INV-${sale.id}`,
        customer_name: sale.customer_name || 'Client',
        table_number: sale.table_number || sale.table?.number || '',
        total_amount: parseNumericValue(sale.final_amount || sale.total_amount),
        payment_method: sale.payment_method_display || sale.payment_method || 'Non spécifié',
        status: sale.status === 'paid' ? 'paid' : 'pending',
        created_at: sale.created_at,
        server: sale.server_name || sale.server?.username || 'Serveur',
        // Inclure les articles de la vente
        items: sale.items || sale.sale_items || []
      };
    });
  }, [salesResponse]);

  // Filtrage des factures
  const filteredInvoices = useMemo(() => {
    return invoices.filter(invoice => {
      const matchesSearch =
        invoice.invoice_number.toLowerCase().includes(searchTerm.toLowerCase()) ||
        invoice.customer_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        invoice.table_number?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        invoice.server.toLowerCase().includes(searchTerm.toLowerCase());

      const matchesStatus = statusFilter === 'Tous' || invoice.status === statusFilter;
      const matchesDate = !dateFilter || invoice.created_at.startsWith(dateFilter);

      return matchesSearch && matchesStatus && matchesDate;
    });
  }, [invoices, searchTerm, statusFilter, dateFilter]);

  // Statistiques calculées à partir des données API
  const stats = useMemo(() => {
    if (!invoices || invoices.length === 0) {
      return { total: 0, paid: 0, pending: 0, totalRevenue: 0 };
    }

    const total = invoices.length;
    const paid = invoices.filter(inv => inv.status === 'paid').length;
    const pending = invoices.filter(inv => inv.status === 'pending').length;
    const totalRevenue = invoices
      .filter(inv => inv.status === 'paid')
      .reduce((sum, inv) => sum + parseNumericValue(inv.total_amount), 0);

    return { total, paid, pending, totalRevenue };
  }, [invoices]);

  // Fonctions de gestion des factures
  const handleViewInvoice = (invoice: any) => {
    // Debug: Afficher les données de la facture
    console.log('Viewing invoice:', invoice);
    console.log('Invoice items:', invoice.items);

    // Ouvrir la facture dans une nouvelle fenêtre ou modal
    const invoiceData = {
      ...invoice,
      items: invoice.items || [],
      company: {
        name: "Bar Stock Wise",
        address: "123 Rue de la Paix, Bujumbura",
        phone: "+257 22 123 456",
        email: "<EMAIL>"
      }
    };

    console.log('Invoice data for display:', invoiceData);

    // Créer une nouvelle fenêtre pour afficher la facture
    const newWindow = window.open('', '_blank', 'width=800,height=600');
    if (newWindow) {
      newWindow.document.write(generateInvoiceHTML(invoiceData));
      newWindow.document.close();
    }
  };

  const handleDownloadPDF = async (invoice: any) => {
    try {
      // Préparer les données de la facture
      const invoiceData = {
        ...invoice,
        items: invoice.items || [],
        company: {
          name: "Bar Stock Wise",
          address: "123 Rue de la Paix, Bujumbura",
          phone: "+257 22 123 456",
          email: "<EMAIL>"
        }
      };

      toast({
        title: "Génération du PDF",
        description: `Génération de la facture ${invoice.invoice_number} en cours...`,
      });

      // Utiliser le composant InvoiceGenerator pour générer le PDF
      // Pour l'instant, on simule le téléchargement
      setTimeout(() => {
        toast({
          title: "PDF généré",
          description: `La facture ${invoice.invoice_number} a été téléchargée.`,
        });
      }, 1000);

    } catch (error) {
      toast({
        title: "Erreur",
        description: "Impossible de générer le PDF. Veuillez réessayer.",
        variant: "destructive",
      });
    }
  };

  const generateInvoiceHTML = (invoice: any) => {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <title>Facture ${invoice.invoice_number}</title>
        <style>
          body { font-family: Arial, sans-serif; margin: 20px; }
          .header { text-align: center; margin-bottom: 30px; }
          .company-info { margin-bottom: 20px; }
          .invoice-info { margin-bottom: 20px; }
          .items-table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
          .items-table th, .items-table td { border: 1px solid #ddd; padding: 8px; text-align: left; }
          .items-table th { background-color: #f2f2f2; }
          .total { text-align: right; font-weight: bold; font-size: 18px; }
          .status { padding: 4px 8px; border-radius: 4px; }
          .status.paid { background-color: #d4edda; color: #155724; }
          .status.pending { background-color: #fff3cd; color: #856404; }
        </style>
      </head>
      <body>
        <div class="header">
          <h1>${invoice.company.name}</h1>
          <p>${invoice.company.address}</p>
          <p>Tél: ${invoice.company.phone} | Email: ${invoice.company.email}</p>
        </div>

        <div class="invoice-info">
          <h2>Facture N° ${invoice.invoice_number}</h2>
          <p><strong>Date:</strong> ${new Date(invoice.created_at).toLocaleDateString('fr-FR')}</p>
          <p><strong>Client:</strong> ${invoice.customer_name || 'Client'}</p>
          <p><strong>Table:</strong> ${invoice.table_number || 'Emporter'}</p>
          <p><strong>Serveur:</strong> ${invoice.server}</p>
          <p><strong>Statut:</strong> <span class="status ${invoice.status}">${invoice.status === 'paid' ? 'Payée' : 'En attente'}</span></p>
        </div>

        <table class="items-table">
          <thead>
            <tr>
              <th>Article</th>
              <th>Quantité</th>
              <th>Prix unitaire</th>
              <th>Total</th>
            </tr>
          </thead>
          <tbody>
            ${invoice.items.map(item => `
              <tr>
                <td>${item.product_name || item.product?.name || 'Article'}</td>
                <td>${item.quantity}</td>
                <td>${formatCurrency(parseNumericValue(item.unit_price))}</td>
                <td>${formatCurrency(parseNumericValue(item.total_price))}</td>
              </tr>
            `).join('')}
          </tbody>
        </table>

        <div class="total">
          <p>Total: ${formatCurrency(invoice.total_amount)}</p>
        </div>

        <div style="margin-top: 40px; text-align: center; color: #666;">
          <p>Merci pour votre visite !</p>
          <p>Facture générée le ${new Date().toLocaleDateString('fr-FR')} à ${new Date().toLocaleTimeString('fr-FR')}</p>
        </div>
      </body>
      </html>
    `;
  };

  const getStatusBadge = (status: string) => {
    return (
      <Badge variant={status === 'paid' ? 'default' : 'secondary'}>
        {status === 'paid' ? 'Payée' : 'En attente'}
      </Badge>
    );
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Gestion des Factures</h1>
          <p className="text-muted-foreground">
            Consultez, imprimez et téléchargez toutes vos factures
          </p>
        </div>

        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            onClick={downloadAllInvoices}
            disabled={isDownloadingAll || filteredInvoices.length === 0}
          >
            {isDownloadingAll ? (
              <>
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                Téléchargement...
              </>
            ) : (
              <>
                <Download className="w-4 h-4 mr-2" />
                Tout télécharger ({filteredInvoices.length})
              </>
            )}
          </Button>
          <Button onClick={createNewInvoice}>
            <Plus className="w-4 h-4 mr-2" />
            Nouvelle Facture
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Factures</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.total}</div>
            <p className="text-xs text-muted-foreground">factures générées</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Factures Payées</CardTitle>
            <Receipt className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.paid}</div>
            <p className="text-xs text-muted-foreground">paiements reçus</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">En Attente</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.pending}</div>
            <p className="text-xs text-muted-foreground">à encaisser</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Chiffre d'Affaires</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(stats.totalRevenue)}</div>
            <p className="text-xs text-muted-foreground">revenus totaux</p>
          </CardContent>
        </Card>
      </div>

      {/* Filtres */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Rechercher une facture..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>

            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-40">
                <SelectValue placeholder="Statut" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="Tous">Tous statuts</SelectItem>
                <SelectItem value="paid">Payée</SelectItem>
                <SelectItem value="pending">En attente</SelectItem>
              </SelectContent>
            </Select>

            <Input
              type="date"
              value={dateFilter}
              onChange={(e) => setDateFilter(e.target.value)}
              className="w-40"
            />

            <Button
              variant="outline"
              onClick={() => {
                setSearchTerm('');
                setStatusFilter('Tous');
                setDateFilter('');
              }}
            >
              <Filter className="w-4 h-4 mr-2" />
              Réinitialiser
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Invoices Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="w-5 h-5" />
            Liste des Factures
          </CardTitle>
          <CardDescription>
            {filteredInvoices.length} facture(s) trouvée(s) sur {invoices.length} total
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>N° Facture</TableHead>
                <TableHead>Client</TableHead>
                <TableHead>Table</TableHead>
                <TableHead>Total</TableHead>
                <TableHead>Statut</TableHead>
                <TableHead>Date</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredInvoices.map((invoice) => (
                <TableRow key={invoice.id}>
                  <TableCell className="font-medium">{invoice.invoice_number}</TableCell>
                  <TableCell>{invoice.customer_name || '-'}</TableCell>
                  <TableCell>{invoice.table_number || 'Emporter'}</TableCell>
                  <TableCell className="font-medium">{formatCurrency(invoice.total_amount)}</TableCell>
                  <TableCell>{getStatusBadge(invoice.status)}</TableCell>
                  <TableCell>
                    {new Date(invoice.created_at).toLocaleDateString('fr-FR')}
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleViewInvoice(invoice)}
                      >
                        <Eye className="w-4 h-4 mr-1" />
                        Voir
                      </Button>
                      <InvoiceGenerator
                        invoice={{
                          ...invoice,
                          items: invoice.items || [],
                          company: {
                            name: "Bar Stock Wise",
                            address: "123 Rue de la Paix, Bujumbura",
                            phone: "+257 22 123 456",
                            email: "<EMAIL>"
                          }
                        }}
                      />
                      <InvoicePrinter
                        invoice={{
                          ...invoice,
                          items: invoice.items || [],
                          company: {
                            name: "Bar Stock Wise",
                            address: "123 Rue de la Paix, Bujumbura",
                            phone: "+257 22 123 456",
                            email: "<EMAIL>"
                          }
                        }}
                      />
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Dialog pour créer une nouvelle facture */}
      <Dialog open={showNewInvoiceDialog} onOpenChange={setShowNewInvoiceDialog}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Nouvelle Facture</DialogTitle>
            <DialogDescription>
              Créer une nouvelle facture pour un client.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <label htmlFor="client-name" className="text-right">
                Client
              </label>
              <Input
                id="client-name"
                placeholder="Nom du client"
                className="col-span-3"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <label htmlFor="invoice-date" className="text-right">
                Date
              </label>
              <Input
                id="invoice-date"
                type="date"
                className="col-span-3"
                defaultValue={new Date().toISOString().split('T')[0]}
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <label htmlFor="amount" className="text-right">
                Montant
              </label>
              <Input
                id="amount"
                type="number"
                placeholder="0"
                className="col-span-3"
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowNewInvoiceDialog(false)}>
              Annuler
            </Button>
            <Button onClick={() => {
              toast({
                title: "Facture créée",
                description: "La nouvelle facture a été créée avec succès.",
              });
              setShowNewInvoiceDialog(false);
            }}>
              Créer la Facture
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default Invoices;
