import React, { useState, useMemo } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { 
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { 
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { 
  AlertTriangle, 
  AlertCircle, 
  CheckCircle, 
  Settings, 
  Bell,
  Package,
  Calendar,
  TrendingDown,
  RefreshCw,
  Filter
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { useInventory, useLowStockAlerts, useStockAlerts, useProducts } from '@/hooks/useApi';
import { formatCurrency } from '@/lib/currency';
import { Skeleton } from '@/components/ui/skeleton';

// Types
interface StockAlert {
  id: string;
  productId: string;
  productName: string;
  category: string;
  currentStock: number;
  minThreshold: number;
  maxThreshold: number;
  alertType: 'Stock faible' | 'Rupture' | 'Surstockage' | 'Expiration proche';
  severity: 'Critique' | 'Élevée' | 'Moyenne' | 'Faible';
  createdAt: string;
  status: 'Active' | 'Résolue' | 'Ignorée';
  expirationDate?: string;
  supplier?: string;
  lastOrderDate?: string;
  estimatedDaysLeft?: number;
}

interface AlertSettings {
  id: string;
  productId: string;
  productName: string;
  minThreshold: number;
  maxThreshold: number;
  expirationWarningDays: number;
  autoReorder: boolean;
  reorderQuantity: number;
}

// Données mock pour les paramètres d'alertes
const mockSettings: AlertSettings[] = [
  {
    id: '1',
    productId: 'prod-1',
    productName: 'PRIMUS',
    minThreshold: 10,
    maxThreshold: 100,
    expirationWarningDays: 7,
    autoReorder: false,
    reorderQuantity: 50
  },
  {
    id: '2',
    productId: 'prod-2',
    productName: 'FANTA',
    minThreshold: 15,
    maxThreshold: 80,
    expirationWarningDays: 5,
    autoReorder: true,
    reorderQuantity: 40
  }
];

const Alerts = () => {
  // Hooks pour les données API
  const { data: lowStockAlertsResponse, isLoading: lowStockLoading, error: lowStockError } = useLowStockAlerts();
  const { data: stockAlertsResponse, isLoading: stockAlertsLoading, error: stockAlertsError } = useStockAlerts();
  const { data: inventoryResponse, isLoading: inventoryLoading } = useInventory();
  const { data: productsResponse, isLoading: productsLoading } = useProducts();

  // Combiner toutes les alertes
  const allAlerts = useMemo(() => {
    const alerts: StockAlert[] = [];

    // Alertes de stock faible depuis l'API products/low-stock/
    if (lowStockAlertsResponse?.products && Array.isArray(lowStockAlertsResponse.products)) {
      const lowStockAlerts = lowStockAlertsResponse.products.map((item: any) => ({
        id: `low-${item.id}`,
        productId: item.id,
        productName: item.name,
        category: item.category?.name || 'Non définie',
        currentStock: item.current_stock || 0,
        minThreshold: item.minimum_stock || 0,
        maxThreshold: item.maximum_stock || 100,
        alertType: item.current_stock === 0 ? 'Rupture de stock' : 'Stock faible',
        severity: item.current_stock === 0 ? 'Critique' : 'Élevée',
        createdAt: new Date().toISOString(),
        status: 'Active',
        supplier: 'Non défini',
        estimatedDaysLeft: Math.max(0, Math.floor(item.current_stock / 2)) // Estimation simple
      }));
      alerts.push(...lowStockAlerts);
    }

    // Alertes générales depuis l'API reports/alerts/
    if (stockAlertsResponse) {
      const alertsData = Array.isArray(stockAlertsResponse)
        ? stockAlertsResponse
        : stockAlertsResponse?.results || [];

      if (Array.isArray(alertsData)) {
        const generalAlerts = alertsData.map((item: any) => ({
          id: item.id,
          productId: item.product?.id || item.product_id,
          productName: item.product?.name || item.product_name,
          category: item.product?.category?.name || item.category || 'Non définie',
          currentStock: item.product?.current_stock || 0,
          minThreshold: item.product?.minimum_stock || 0,
          maxThreshold: item.product?.maximum_stock || 100,
          alertType: item.alert_type === 'low_stock' ? 'Stock faible' :
                    item.alert_type === 'out_of_stock' ? 'Rupture de stock' :
                    item.alert_type === 'expiry_soon' ? 'Expiration proche' : 'Autre',
          severity: item.alert_type === 'out_of_stock' ? 'Critique' : 'Élevée',
          createdAt: item.created_at || new Date().toISOString(),
          status: item.status === 'active' ? 'Active' :
                 item.status === 'resolved' ? 'Résolue' : 'Ignorée',
          supplier: 'Non défini',
          estimatedDaysLeft: 0
        }));
        alerts.push(...generalAlerts);
      }
    }

    return alerts;
  }, [lowStockAlertsResponse, stockAlertsResponse]);

  // État local pour les filtres et la gestion
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedAlert, setSelectedAlert] = useState<StockAlert | null>(null);
  const [isSettingsOpen, setIsSettingsOpen] = useState(false);
  const [selectedSeverity, setSelectedSeverity] = useState('Toutes');
  const [selectedStatus, setSelectedStatus] = useState('Toutes');
  const [selectedType, setSelectedType] = useState('Tous');
  const { toast } = useToast();

  // Gestion locale des alertes pour les actions
  const [localAlerts, setLocalAlerts] = useState<StockAlert[]>([]);

  // Synchroniser les alertes locales avec les alertes de l'API
  React.useEffect(() => {
    setLocalAlerts(allAlerts);
  }, [allAlerts]);

  // Filtrage des alertes unifié
  const filteredAlerts = useMemo(() => {
    return localAlerts.filter(alert => {
      const matchesSearch =
        alert.productName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        alert.category.toLowerCase().includes(searchTerm.toLowerCase()) ||
        alert.supplier?.toLowerCase().includes(searchTerm.toLowerCase());

      const matchesSeverity = selectedSeverity === 'Toutes' || alert.severity === selectedSeverity;
      const matchesType = selectedType === 'Tous' || alert.alertType === selectedType;
      const matchesStatus = selectedStatus === 'Toutes' || alert.status === selectedStatus;

      return matchesSearch && matchesSeverity && matchesType && matchesStatus;
    });
  }, [localAlerts, searchTerm, selectedSeverity, selectedType, selectedStatus]);

  // Statistiques des alertes
  const alertStats = useMemo(() => {
    const total = allAlerts.length;
    const critical = allAlerts.filter(a => a.severity === 'Critique').length;
    const high = allAlerts.filter(a => a.severity === 'Élevée').length;
    const medium = allAlerts.filter(a => a.severity === 'Moyenne').length;
    const low = allAlerts.filter(a => a.severity === 'Faible').length;
    const active = allAlerts.filter(a => a.status === 'Active').length;

    return { total, critical, high, medium, low, active };
  }, [allAlerts]);

  // Générer les paramètres dynamiquement à partir des produits
  const dynamicSettings = useMemo(() => {
    if (!productsResponse) return mockSettings;

    const productsList = Array.isArray(productsResponse)
      ? productsResponse
      : productsResponse?.results || [];

    return productsList.map((product: any) => ({
      id: product.id.toString(),
      productId: product.id.toString(),
      productName: product.name,
      minThreshold: product.minimum_stock || 10,
      maxThreshold: product.maximum_stock || 100,
      expirationWarningDays: 7, // Valeur par défaut
      autoReorder: false, // Valeur par défaut
      reorderQuantity: Math.max(50, product.minimum_stock * 2) // Suggestion intelligente
    }));
  }, [productsResponse]);

  const [settings, setSettings] = useState<AlertSettings[]>(dynamicSettings);

  // Synchroniser les paramètres quand les produits changent
  React.useEffect(() => {
    setSettings(dynamicSettings);
  }, [dynamicSettings]);

  const handleResolveAlert = (alertId: string) => {
    setLocalAlerts(prev => prev.map(alert =>
      alert.id === alertId
        ? { ...alert, status: 'Résolue' as const }
        : alert
    ));
    toast({
      title: "Alerte résolue",
      description: "L'alerte a été marquée comme résolue.",
    });
  };

  const handleIgnoreAlert = (alertId: string) => {
    setLocalAlerts(prev => prev.map(alert =>
      alert.id === alertId
        ? { ...alert, status: 'Ignorée' as const }
        : alert
    ));
    toast({
      title: "Alerte ignorée",
      description: "L'alerte a été ignorée.",
      variant: "destructive",
    });
  };

  const getSeverityBadge = (severity: string) => {
    const variants = {
      'Critique': { variant: 'destructive' as const, icon: AlertTriangle },
      'Élevée': { variant: 'destructive' as const, icon: AlertCircle },
      'Moyenne': { variant: 'default' as const, icon: AlertCircle },
      'Faible': { variant: 'secondary' as const, icon: AlertCircle }
    };
    
    const { variant, icon: Icon } = variants[severity as keyof typeof variants];
    
    return (
      <Badge variant={variant} className="flex items-center gap-1 w-fit">
        <Icon className="w-3 h-3" />
        {severity}
      </Badge>
    );
  };

  const getStatusBadge = (status: string) => {
    const variants = {
      'Active': { variant: 'destructive' as const, icon: AlertTriangle },
      'Résolue': { variant: 'default' as const, icon: CheckCircle },
      'Ignorée': { variant: 'secondary' as const, icon: AlertCircle }
    };
    
    const { variant, icon: Icon } = variants[status as keyof typeof variants];
    
    return (
      <Badge variant={variant} className="flex items-center gap-1 w-fit">
        <Icon className="w-3 h-3" />
        {status}
      </Badge>
    );
  };

  const getAlertTypeIcon = (type: string) => {
    const icons = {
      'Stock faible': TrendingDown,
      'Rupture': AlertTriangle,
      'Surstockage': Package,
      'Expiration proche': Calendar
    };
    
    return icons[type as keyof typeof icons] || AlertCircle;
  };

  const stats = {
    total: localAlerts.length,
    active: localAlerts.filter(a => a.status === 'Active').length,
    critical: localAlerts.filter(a => a.severity === 'Critique' && a.status === 'Active').length,
    resolved: localAlerts.filter(a => a.status === 'Résolue').length,
    stockOut: localAlerts.filter(a => a.alertType === 'Rupture' && a.status === 'Active').length
  };

  const refreshAlerts = () => {
    toast({
      title: "Alertes actualisées",
      description: "Les alertes de stock ont été mises à jour.",
    });
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Alertes de Stock</h1>
          <p className="text-muted-foreground">
            Surveillez et gérez les alertes de votre inventaire
          </p>
        </div>
        
        <div className="flex gap-2">
          <Button variant="outline" onClick={refreshAlerts}>
            <RefreshCw className="w-4 h-4 mr-2" />
            Actualiser
          </Button>
          
          <Dialog open={isSettingsOpen} onOpenChange={setIsSettingsOpen}>
            <DialogTrigger asChild>
              <Button>
                <Settings className="w-4 h-4 mr-2" />
                Paramètres
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-2xl">
              <DialogHeader>
                <DialogTitle>Paramètres des Alertes</DialogTitle>
                <DialogDescription>
                  Configurez les seuils d'alerte pour chaque produit
                </DialogDescription>
              </DialogHeader>
              
              <div className="space-y-4 max-h-96 overflow-y-auto">
                {settings.map((setting) => (
                  <Card key={setting.id}>
                    <CardHeader className="pb-3">
                      <CardTitle className="text-base">{setting.productName}</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="grid grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label>Seuil minimum</Label>
                          <Input
                            type="number"
                            value={setting.minThreshold}
                            onChange={(e) => {
                              const newValue = parseInt(e.target.value);
                              setSettings(prev => prev.map(s => 
                                s.id === setting.id 
                                  ? { ...s, minThreshold: newValue }
                                  : s
                              ));
                            }}
                          />
                        </div>
                        <div className="space-y-2">
                          <Label>Seuil maximum</Label>
                          <Input
                            type="number"
                            value={setting.maxThreshold}
                            onChange={(e) => {
                              const newValue = parseInt(e.target.value);
                              setSettings(prev => prev.map(s => 
                                s.id === setting.id 
                                  ? { ...s, maxThreshold: newValue }
                                  : s
                              ));
                            }}
                          />
                        </div>
                      </div>
                      
                      <div className="grid grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label>Alerte expiration (jours)</Label>
                          <Input
                            type="number"
                            value={setting.expirationWarningDays}
                            onChange={(e) => {
                              const newValue = parseInt(e.target.value);
                              setSettings(prev => prev.map(s => 
                                s.id === setting.id 
                                  ? { ...s, expirationWarningDays: newValue }
                                  : s
                              ));
                            }}
                          />
                        </div>
                        <div className="space-y-2">
                          <Label>Quantité de réapprovisionnement</Label>
                          <Input
                            type="number"
                            value={setting.reorderQuantity}
                            onChange={(e) => {
                              const newValue = parseInt(e.target.value);
                              setSettings(prev => prev.map(s => 
                                s.id === setting.id 
                                  ? { ...s, reorderQuantity: newValue }
                                  : s
                              ));
                            }}
                          />
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
              
              <div className="flex justify-end gap-2 pt-4">
                <Button variant="outline" onClick={() => setIsSettingsOpen(false)}>
                  Annuler
                </Button>
                <Button onClick={() => {
                  setIsSettingsOpen(false);
                  toast({
                    title: "Paramètres sauvegardés",
                    description: "Les seuils d'alerte ont été mis à jour.",
                  });
                }}>
                  Sauvegarder
                </Button>
              </div>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-5">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Alertes</CardTitle>
            <Bell className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.total}</div>
            <p className="text-xs text-muted-foreground">
              {stats.active} actives
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Critiques</CardTitle>
            <AlertTriangle className="h-4 w-4 text-destructive" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-destructive">{stats.critical}</div>
            <p className="text-xs text-muted-foreground">action immédiate</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Ruptures</CardTitle>
            <Package className="h-4 w-4 text-destructive" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-destructive">{stats.stockOut}</div>
            <p className="text-xs text-muted-foreground">produits épuisés</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Résolues</CardTitle>
            <CheckCircle className="h-4 w-4 text-success" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-success">{stats.resolved}</div>
            <p className="text-xs text-muted-foreground">ce mois</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Taux de résolution</CardTitle>
            <TrendingDown className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {stats.total > 0 ? Math.round((stats.resolved / stats.total) * 100) : 0}%
            </div>
            <p className="text-xs text-muted-foreground">efficacité</p>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex items-center gap-2">
              <Filter className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm font-medium">Filtres:</span>
            </div>
            
            <Select value={selectedSeverity} onValueChange={setSelectedSeverity}>
              <SelectTrigger className="w-full sm:w-[150px]">
                <SelectValue placeholder="Sévérité" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="Toutes">Toutes</SelectItem>
                <SelectItem value="Critique">Critique</SelectItem>
                <SelectItem value="Élevée">Élevée</SelectItem>
                <SelectItem value="Moyenne">Moyenne</SelectItem>
                <SelectItem value="Faible">Faible</SelectItem>
              </SelectContent>
            </Select>
            
            <Select value={selectedStatus} onValueChange={setSelectedStatus}>
              <SelectTrigger className="w-full sm:w-[150px]">
                <SelectValue placeholder="Statut" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="Toutes">Toutes</SelectItem>
                <SelectItem value="Active">Active</SelectItem>
                <SelectItem value="Résolue">Résolue</SelectItem>
                <SelectItem value="Ignorée">Ignorée</SelectItem>
              </SelectContent>
            </Select>
            
            <Select value={selectedType} onValueChange={setSelectedType}>
              <SelectTrigger className="w-full sm:w-[180px]">
                <SelectValue placeholder="Type d'alerte" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="Tous">Tous</SelectItem>
                <SelectItem value="Stock faible">Stock faible</SelectItem>
                <SelectItem value="Rupture">Rupture</SelectItem>
                <SelectItem value="Surstockage">Surstockage</SelectItem>
                <SelectItem value="Expiration proche">Expiration proche</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Alerts Table */}
      <Card>
        <CardHeader>
          <CardTitle>Alertes de Stock</CardTitle>
          <CardDescription>
            {filteredAlerts.length} alerte(s) trouvée(s)
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Produit</TableHead>
                <TableHead>Type</TableHead>
                <TableHead>Stock Actuel</TableHead>
                <TableHead>Seuils</TableHead>
                <TableHead>Sévérité</TableHead>
                <TableHead>Statut</TableHead>
                <TableHead>Créée le</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredAlerts.map((alert) => {
                const AlertIcon = getAlertTypeIcon(alert.alertType);
                return (
                  <TableRow key={alert.id}>
                    <TableCell>
                      <div>
                        <div className="font-medium">{alert.productName}</div>
                        <div className="text-sm text-muted-foreground">{alert.category}</div>
                        {alert.supplier && (
                          <div className="text-xs text-muted-foreground">
                            Fournisseur: {alert.supplier}
                          </div>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <AlertIcon className="w-4 h-4" />
                        <span className="text-sm">{alert.alertType}</span>
                      </div>
                      {alert.estimatedDaysLeft !== undefined && (
                        <div className="text-xs text-muted-foreground">
                          {alert.estimatedDaysLeft === 0 
                            ? 'Épuisé' 
                            : `${alert.estimatedDaysLeft} jour(s) restant(s)`
                          }
                        </div>
                      )}
                    </TableCell>
                    <TableCell>
                      <div className="font-medium">{alert.currentStock}</div>
                      {alert.expirationDate && (
                        <div className="text-xs text-muted-foreground">
                          Expire: {new Date(alert.expirationDate).toLocaleDateString('fr-FR')}
                        </div>
                      )}
                    </TableCell>
                    <TableCell>
                      <div className="text-sm">
                        Min: {alert.minThreshold}
                      </div>
                      <div className="text-sm">
                        Max: {alert.maxThreshold}
                      </div>
                    </TableCell>
                    <TableCell>
                      {getSeverityBadge(alert.severity)}
                    </TableCell>
                    <TableCell>
                      {getStatusBadge(alert.status)}
                    </TableCell>
                    <TableCell>
                      <div className="text-sm">
                        {new Date(alert.createdAt).toLocaleDateString('fr-FR')}
                      </div>
                      <div className="text-xs text-muted-foreground">
                        {new Date(alert.createdAt).toLocaleTimeString('fr-FR', { 
                          hour: '2-digit', 
                          minute: '2-digit' 
                        })}
                      </div>
                    </TableCell>
                    <TableCell>
                      {alert.status === 'Active' && (
                        <div className="flex gap-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleResolveAlert(alert.id)}
                          >
                            <CheckCircle className="w-4 h-4" />
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleIgnoreAlert(alert.id)}
                          >
                            <AlertCircle className="w-4 h-4" />
                          </Button>
                        </div>
                      )}
                    </TableCell>
                  </TableRow>
                );
              })}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
};

export default Alerts;
