import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { useToast } from '@/hooks/use-toast';
import {
  Bell,
  CheckCircle,
  AlertTriangle,
  XCircle,
  Info,
  Volume2,
  VolumeX,
  Smartphone,
  Monitor,
  Settings,
  Play,
  Pause,
  RotateCcw,
} from 'lucide-react';
import { useNotifications } from '@/lib/notificationService';
import NotificationDebug from '@/components/NotificationDebug';

const NotificationTest = () => {
  const { toast } = useToast();
  const notifications = useNotifications();
  const [testConfig, setTestConfig] = useState({
    title: 'Test BarStock Wise',
    body: 'Ceci est une notification de test',
    icon: '/icon-192x192.png',
    requireInteraction: false,
    silent: false,
  });
  const [isTestingSequence, setIsTestingSequence] = useState(false);

  // Test de notification simple
  const testBasicNotification = async () => {
    try {
      const success = await notifications.sendNotification(testConfig);
      if (success) {
        toast({
          title: "✅ Test réussi",
          description: "Notification basique envoyée avec succès",
        });
      } else {
        toast({
          title: "❌ Test échoué",
          description: "Impossible d'envoyer la notification",
          variant: "destructive",
        });
      }
    } catch (error) {
      toast({
        title: "❌ Erreur",
        description: `Erreur lors du test: ${error}`,
        variant: "destructive",
      });
    }
  };

  // Test des notifications spécialisées BarStock
  const testStockAlert = async () => {
    await notifications.notifyLowStock('Primus 72cl', 5, 20);
    toast({
      title: "🔔 Alerte stock testée",
      description: "Notification d'alerte de stock faible envoyée",
    });
  };

  const testSaleNotification = async () => {
    await notifications.notifyNewSale(25000, 'Table 5');
    toast({
      title: "💰 Vente testée",
      description: "Notification de nouvelle vente envoyée",
    });
  };

  const testSystemAlert = async () => {
    await notifications.notifySystemAlert('Test d\'alerte système', 'warning');
    toast({
      title: "⚠️ Alerte système testée",
      description: "Notification d'alerte système envoyée",
    });
  };

  // Séquence de test complète
  const runTestSequence = async () => {
    setIsTestingSequence(true);
    
    const tests = [
      { name: 'Notification basique', fn: testBasicNotification },
      { name: 'Alerte stock faible', fn: testStockAlert },
      { name: 'Nouvelle vente', fn: testSaleNotification },
      { name: 'Alerte système', fn: testSystemAlert },
    ];

    for (let i = 0; i < tests.length; i++) {
      const test = tests[i];
      toast({
        title: `🧪 Test ${i + 1}/${tests.length}`,
        description: `Exécution: ${test.name}`,
      });
      
      await test.fn();
      await new Promise(resolve => setTimeout(resolve, 2000)); // Pause entre les tests
    }

    setIsTestingSequence(false);
    toast({
      title: "🎉 Séquence terminée",
      description: "Tous les tests de notifications ont été exécutés",
    });
  };

  // Demander la permission
  const requestPermission = async () => {
    try {
      if (!notifications.requestPermission) {
        toast({
          title: "❌ Erreur",
          description: "La méthode requestPermission n'est pas disponible",
          variant: "destructive",
        });
        return;
      }

      const granted = await notifications.requestPermission();
      if (granted) {
        toast({
          title: "✅ Permission accordée",
          description: "Les notifications sont maintenant autorisées",
        });
      } else {
        toast({
          title: "❌ Permission refusée",
          description: "Veuillez autoriser les notifications dans les paramètres du navigateur",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error('Erreur lors de la demande de permission:', error);
      toast({
        title: "❌ Erreur",
        description: `Erreur: ${error instanceof Error ? error.message : 'Erreur inconnue'}`,
        variant: "destructive",
      });
    }
  };

  // Réinitialiser les préférences
  const resetPreferences = () => {
    notifications.updatePreferences({
      enabled: true,
      stockAlerts: true,
      salesNotifications: true,
      systemAlerts: true,
      soundEnabled: true,
      vibrationEnabled: true,
    });
    toast({
      title: "🔄 Préférences réinitialisées",
      description: "Les préférences de notifications ont été restaurées",
    });
  };

  const getPermissionBadge = () => {
    const permission = notifications.status.permission;
    switch (permission) {
      case 'granted':
        return <Badge variant="default" className="bg-green-500">Autorisées</Badge>;
      case 'denied':
        return <Badge variant="destructive">Refusées</Badge>;
      default:
        return <Badge variant="secondary">En attente</Badge>;
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Test des Notifications</h1>
          <p className="text-muted-foreground">
            Interface de test complète pour le système de notifications BarStock Wise
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button onClick={resetPreferences} variant="outline">
            <RotateCcw className="w-4 h-4 mr-2" />
            Réinitialiser
          </Button>
          <Button onClick={runTestSequence} disabled={isTestingSequence}>
            {isTestingSequence ? (
              <>
                <Pause className="w-4 h-4 mr-2" />
                Test en cours...
              </>
            ) : (
              <>
                <Play className="w-4 h-4 mr-2" />
                Séquence Complète
              </>
            )}
          </Button>
        </div>
      </div>

      {/* Statut des notifications */}
      <Card className="border-2 border-blue-200 bg-blue-50">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-blue-700">
            <Bell className="h-5 w-5" />
            Statut du Système de Notifications
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2">
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="font-medium">Permission navigateur:</span>
                {getPermissionBadge()}
              </div>
              <div className="flex justify-between">
                <span className="font-medium">Service Worker:</span>
                <Badge variant={notifications.status.serviceWorkerAvailable ? 'default' : 'secondary'}>
                  {notifications.status.serviceWorkerAvailable ? 'Disponible' : 'Indisponible'}
                </Badge>
              </div>
              <div className="flex justify-between">
                <span className="font-medium">Support navigateur:</span>
                <Badge variant={notifications.status.browserSupport ? 'default' : 'destructive'}>
                  {notifications.status.browserSupport ? 'Supporté' : 'Non supporté'}
                </Badge>
              </div>
            </div>
            
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="font-medium">Notifications activées:</span>
                <Badge variant={notifications.status.preferences.enabled ? 'default' : 'secondary'}>
                  {notifications.status.preferences.enabled ? 'Oui' : 'Non'}
                </Badge>
              </div>
              <div className="flex justify-between">
                <span className="font-medium">Alertes stock:</span>
                <Badge variant={notifications.status.preferences.stockAlerts ? 'default' : 'secondary'}>
                  {notifications.status.preferences.stockAlerts ? 'Oui' : 'Non'}
                </Badge>
              </div>
              <div className="flex justify-between">
                <span className="font-medium">Son activé:</span>
                <Badge variant={notifications.status.preferences.soundEnabled ? 'default' : 'secondary'}>
                  {notifications.status.preferences.soundEnabled ? 'Oui' : 'Non'}
                </Badge>
              </div>
            </div>
          </div>

          {notifications.status.permission !== 'granted' && (
            <div className="mt-4 p-4 bg-yellow-100 border border-yellow-300 rounded-lg">
              <div className="flex items-center gap-2 text-yellow-800">
                <AlertTriangle className="h-4 w-4" />
                <span className="font-medium">Permission requise</span>
              </div>
              <p className="text-sm text-yellow-700 mt-1">
                Les notifications ne sont pas autorisées. Cliquez sur le bouton ci-dessous pour demander la permission.
              </p>
              <Button onClick={requestPermission} className="mt-2" size="sm">
                Autoriser les Notifications
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Configuration de test */}
      <Card>
        <CardHeader>
          <CardTitle>Configuration de Test</CardTitle>
          <CardDescription>
            Personnalisez les paramètres de la notification de test
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <div className="space-y-2">
              <Label htmlFor="test-title">Titre</Label>
              <Input
                id="test-title"
                value={testConfig.title}
                onChange={(e) => setTestConfig(prev => ({ ...prev, title: e.target.value }))}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="test-icon">Icône (URL)</Label>
              <Input
                id="test-icon"
                value={testConfig.icon}
                onChange={(e) => setTestConfig(prev => ({ ...prev, icon: e.target.value }))}
              />
            </div>
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="test-body">Message</Label>
            <Textarea
              id="test-body"
              value={testConfig.body}
              onChange={(e) => setTestConfig(prev => ({ ...prev, body: e.target.value }))}
              rows={3}
            />
          </div>

          <div className="flex items-center gap-4">
            <div className="flex items-center space-x-2">
              <Switch
                id="require-interaction"
                checked={testConfig.requireInteraction}
                onCheckedChange={(checked) => 
                  setTestConfig(prev => ({ ...prev, requireInteraction: checked }))
                }
              />
              <Label htmlFor="require-interaction">Interaction requise</Label>
            </div>
            
            <div className="flex items-center space-x-2">
              <Switch
                id="silent"
                checked={testConfig.silent}
                onCheckedChange={(checked) => 
                  setTestConfig(prev => ({ ...prev, silent: checked }))
                }
              />
              <Label htmlFor="silent">Silencieux</Label>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Tests individuels */}
      <div className="grid gap-4 md:grid-cols-3">
        <NotificationDebug />

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Bell className="h-5 w-5" />
              Tests Basiques
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <Button onClick={testBasicNotification} className="w-full" variant="outline">
              <Bell className="w-4 h-4 mr-2" />
              Test Notification Personnalisée
            </Button>
            
            <Button onClick={requestPermission} className="w-full" variant="outline">
              <Settings className="w-4 h-4 mr-2" />
              Demander Permission
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5" />
              Tests BarStock Spécialisés
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <Button onClick={testStockAlert} className="w-full" variant="outline">
              <AlertTriangle className="w-4 h-4 mr-2" />
              Alerte Stock Faible
            </Button>
            
            <Button onClick={testSaleNotification} className="w-full" variant="outline">
              <CheckCircle className="w-4 h-4 mr-2" />
              Notification Vente
            </Button>
            
            <Button onClick={testSystemAlert} className="w-full" variant="outline">
              <XCircle className="w-4 h-4 mr-2" />
              Alerte Système
            </Button>
          </CardContent>
        </Card>
      </div>

      {/* Informations de débogage */}
      <Card>
        <CardHeader>
          <CardTitle>Informations de Débogage</CardTitle>
        </CardHeader>
        <CardContent>
          <pre className="bg-gray-100 p-4 rounded-lg text-sm overflow-auto">
            {JSON.stringify(notifications.status, null, 2)}
          </pre>
        </CardContent>
      </Card>
    </div>
  );
};

export default NotificationTest;
