// Système de notifications push pour BarStockWise
import React from 'react';
export interface NotificationConfig {
  title: string;
  body: string;
  icon?: string;
  badge?: string;
  tag?: string;
  requireInteraction?: boolean;
  actions?: NotificationAction[];
  data?: any;
}

export interface NotificationAction {
  action: string;
  title: string;
  icon?: string;
}

export class NotificationManager {
  private static instance: NotificationManager;
  private permission: NotificationPermission = 'default';
  private isSupported: boolean;

  constructor() {
    this.isSupported = 'Notification' in window;
    if (this.isSupported) {
      this.permission = Notification.permission;
    }
  }

  static getInstance(): NotificationManager {
    if (!NotificationManager.instance) {
      NotificationManager.instance = new NotificationManager();
    }
    return NotificationManager.instance;
  }

  // Demander la permission pour les notifications avec gestion intelligente
  async requestPermission(): Promise<boolean> {
    if (!this.isSupported) {
      console.warn('Les notifications ne sont pas supportées par ce navigateur');
      return false;
    }

    if (this.permission === 'granted') {
      return true;
    }

    // Si la permission a été refusée, ne pas redemander
    if (this.permission === 'denied') {
      console.warn('Les notifications ont été refusées par l\'utilisateur');
      this.showPermissionHelp();
      return false;
    }

    try {
      this.permission = await Notification.requestPermission();

      if (this.permission === 'denied') {
        this.showPermissionHelp();
      }

      return this.permission === 'granted';
    } catch (error) {
      console.error('Erreur lors de la demande de permission:', error);
      this.showPermissionHelp();
      return false;
    }
  }

  // Afficher l'aide pour réactiver les notifications
  private showPermissionHelp(): void {
    const helpMessage = `
🔔 Notifications bloquées

Pour réactiver les notifications :
1. Cliquez sur l'icône 🔒 ou ⚙️ à côté de l'URL
2. Changez "Notifications" de "Bloquer" à "Autoriser"
3. Rechargez la page

Les notifications vous aident à rester informé des alertes de stock importantes.
    `;

    console.warn(helpMessage);

    // Afficher une notification toast si disponible
    if (typeof window !== 'undefined' && (window as any).showToast) {
      (window as any).showToast({
        title: "Notifications désactivées",
        description: "Cliquez sur l'icône à côté de l'URL pour les réactiver",
        variant: "warning",
        duration: 10000
      });
    }
  }

  // Envoyer une notification
  async sendNotification(config: NotificationConfig): Promise<Notification | null> {
    if (!this.isSupported || this.permission !== 'granted') {
      console.warn('Notifications non autorisées');
      return null;
    }

    try {
      const notification = new Notification(config.title, {
        body: config.body,
        icon: config.icon || '/favicon.ico',
        badge: config.badge || '/favicon.ico',
        tag: config.tag,
        requireInteraction: config.requireInteraction || false,
        data: config.data
      });

      // Auto-fermeture après 5 secondes si pas d'interaction requise
      if (!config.requireInteraction) {
        setTimeout(() => {
          notification.close();
        }, 5000);
      }

      return notification;
    } catch (error) {
      console.error('Erreur lors de l\'envoi de la notification:', error);
      return null;
    }
  }

  // Notifications spécifiques pour BarStockWise
  async notifyStockAlert(productName: string, currentStock: number, threshold: number) {
    return this.sendNotification({
      title: '🚨 Alerte Stock Faible',
      body: `${productName}: ${currentStock} unités restantes (seuil: ${threshold})`,
      icon: '/icons/alert.png',
      tag: `stock-alert-${productName}`,
      requireInteraction: true,
      actions: [
        { action: 'view', title: 'Voir le rapport', icon: '/icons/view.png' },
        { action: 'order', title: 'Commander', icon: '/icons/order.png' }
      ],
      data: { type: 'stock-alert', product: productName, stock: currentStock }
    });
  }

  async notifyStockOut(productName: string) {
    return this.sendNotification({
      title: '🔴 RUPTURE DE STOCK',
      body: `${productName} est en rupture de stock ! Action immédiate requise.`,
      icon: '/icons/critical.png',
      tag: `stock-out-${productName}`,
      requireInteraction: true,
      actions: [
        { action: 'urgent-order', title: 'Commande urgente', icon: '/icons/urgent.png' },
        { action: 'view-alternatives', title: 'Voir alternatives', icon: '/icons/alternatives.png' }
      ],
      data: { type: 'stock-out', product: productName, priority: 'critical' }
    });
  }

  async notifyDailyReportReady(date: string, totalRevenue: number) {
    return this.sendNotification({
      title: '📊 Rapport Journalier Prêt',
      body: `Rapport du ${date} généré. Recette: ${totalRevenue.toLocaleString()} BIF`,
      icon: '/icons/report.png',
      tag: `daily-report-${date}`,
      actions: [
        { action: 'view-report', title: 'Voir le rapport', icon: '/icons/view.png' },
        { action: 'export-pdf', title: 'Exporter PDF', icon: '/icons/pdf.png' }
      ],
      data: { type: 'daily-report', date, revenue: totalRevenue }
    });
  }

  async notifyHighProfit(productName: string, profit: number, date: string) {
    return this.sendNotification({
      title: '💰 Excellent Bénéfice',
      body: `${productName} a généré ${profit.toLocaleString()} BIF aujourd'hui !`,
      icon: '/icons/profit.png',
      tag: `high-profit-${productName}-${date}`,
      data: { type: 'high-profit', product: productName, profit, date }
    });
  }

  async notifyCalculationError(productName: string, errorType: string) {
    return this.sendNotification({
      title: '⚠️ Erreur de Calcul Détectée',
      body: `${productName}: ${errorType}. Correction automatique appliquée.`,
      icon: '/icons/warning.png',
      tag: `calc-error-${productName}`,
      actions: [
        { action: 'view-correction', title: 'Voir la correction', icon: '/icons/fix.png' }
      ],
      data: { type: 'calculation-error', product: productName, error: errorType }
    });
  }

  // Notification de sauvegarde automatique
  async notifyAutoSave(itemCount: number) {
    return this.sendNotification({
      title: '💾 Sauvegarde Automatique',
      body: `${itemCount} éléments sauvegardés automatiquement`,
      icon: '/icons/save.png',
      tag: 'auto-save',
      data: { type: 'auto-save', count: itemCount }
    });
  }

  // Notification de fin de journée
  async notifyEndOfDay(totalRevenue: number, totalProfit: number, alertCount: number) {
    return this.sendNotification({
      title: '🌅 Résumé de la Journée',
      body: `Recette: ${totalRevenue.toLocaleString()} BIF | Bénéfice: ${totalProfit.toLocaleString()} BIF | ${alertCount} alerte(s)`,
      icon: '/icons/summary.png',
      tag: 'end-of-day',
      requireInteraction: true,
      actions: [
        { action: 'view-summary', title: 'Voir le résumé', icon: '/icons/summary.png' },
        { action: 'export-report', title: 'Exporter rapport', icon: '/icons/export.png' }
      ],
      data: { type: 'end-of-day', revenue: totalRevenue, profit: totalProfit, alerts: alertCount }
    });
  }

  // Planifier des notifications récurrentes
  scheduleRecurringNotifications() {
    // Notification de rappel quotidien à 8h00
    const now = new Date();
    const tomorrow8AM = new Date(now);
    tomorrow8AM.setDate(now.getDate() + 1);
    tomorrow8AM.setHours(8, 0, 0, 0);

    const timeUntil8AM = tomorrow8AM.getTime() - now.getTime();

    setTimeout(() => {
      this.sendNotification({
        title: '☀️ Nouveau Jour',
        body: 'N\'oubliez pas de créer votre rapport journalier !',
        icon: '/icons/morning.png',
        tag: 'daily-reminder',
        actions: [
          { action: 'create-report', title: 'Créer rapport', icon: '/icons/create.png' }
        ]
      });

      // Répéter chaque jour
      setInterval(() => {
        this.sendNotification({
          title: '☀️ Nouveau Jour',
          body: 'N\'oubliez pas de créer votre rapport journalier !',
          icon: '/icons/morning.png',
          tag: 'daily-reminder'
        });
      }, 24 * 60 * 60 * 1000); // 24 heures

    }, timeUntil8AM);
  }

  // Gérer les clics sur les notifications
  handleNotificationClick(event: any) {
    const notification = event.notification;
    const action = event.action;
    const data = notification?.data;

    notification.close();

    // Router vers la bonne page selon l'action
    switch (action) {
      case 'view':
      case 'view-report':
        window.focus();
        window.location.hash = '/daily-report';
        break;
      case 'order':
      case 'urgent-order':
        window.focus();
        window.location.hash = '/suppliers';
        break;
      case 'export-pdf':
        window.focus();
        // Déclencher l'export PDF
        window.dispatchEvent(new CustomEvent('export-pdf', { detail: data }));
        break;
      case 'create-report':
        window.focus();
        window.location.hash = '/daily-report';
        break;
      default:
        window.focus();
    }
  }
}

// Instance globale
export const notificationManager = NotificationManager.getInstance();

// Hook React pour les notifications
export const useNotifications = () => {
  const [permission, setPermission] = React.useState<NotificationPermission>('default');
  const [isSupported, setIsSupported] = React.useState(false);

  React.useEffect(() => {
    setIsSupported('Notification' in window);
    if ('Notification' in window) {
      setPermission(Notification.permission);
    }
  }, []);

  const requestPermission = async () => {
    const granted = await notificationManager.requestPermission();
    setPermission(Notification.permission);
    return granted;
  };

  return {
    permission,
    isSupported,
    requestPermission,
    sendNotification: notificationManager.sendNotification.bind(notificationManager),
    notifyStockAlert: notificationManager.notifyStockAlert.bind(notificationManager),
    notifyStockOut: notificationManager.notifyStockOut.bind(notificationManager),
    notifyDailyReportReady: notificationManager.notifyDailyReportReady.bind(notificationManager),
    notifyHighProfit: notificationManager.notifyHighProfit.bind(notificationManager),
    notifyCalculationError: notificationManager.notifyCalculationError.bind(notificationManager)
  };
};
