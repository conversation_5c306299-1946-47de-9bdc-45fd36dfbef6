import React from 'react';

/**
 * Service d'analytics avancé pour BarStock Wise
 */

export interface AnalyticsEvent {
  category: string;
  action: string;
  label?: string;
  value?: number;
  timestamp: number;
  userId?: string;
  sessionId: string;
  metadata?: Record<string, any>;
}

export interface UserBehavior {
  pageViews: Record<string, number>;
  timeSpent: Record<string, number>;
  actions: Record<string, number>;
  errors: Array<{ error: string; timestamp: number; page: string }>;
}

export interface BusinessMetrics {
  salesTrends: Array<{ date: string; amount: number; count: number }>;
  stockMovements: Array<{ product: string; movement: number; timestamp: number }>;
  userActivity: Array<{ user: string; actions: number; lastSeen: number }>;
  systemPerformance: Array<{ metric: string; value: number; timestamp: number }>;
}

export class AnalyticsService {
  private static instance: AnalyticsService;
  private events: AnalyticsEvent[] = [];
  private sessionId: string;
  private userId?: string;
  private startTime: number;
  private pageStartTime: number = Date.now();
  private currentPage: string = '';

  constructor() {
    this.sessionId = this.generateSessionId();
    this.startTime = Date.now();
    this.initializeTracking();
  }

  static getInstance(): AnalyticsService {
    if (!AnalyticsService.instance) {
      AnalyticsService.instance = new AnalyticsService();
    }
    return AnalyticsService.instance;
  }

  /**
   * Initialiser le tracking automatique
   */
  private initializeTracking() {
    // Tracking des changements de page
    if (typeof window !== 'undefined') {
      // Tracking du temps passé sur la page
      window.addEventListener('beforeunload', () => {
        this.trackTimeSpent();
      });

      // Tracking des erreurs JavaScript
      window.addEventListener('error', (event) => {
        this.trackError(event.error?.message || 'JavaScript Error', {
          filename: event.filename,
          lineno: event.lineno,
          colno: event.colno,
        });
      });

      // Tracking des promesses rejetées
      window.addEventListener('unhandledrejection', (event) => {
        this.trackError('Unhandled Promise Rejection', {
          reason: event.reason?.toString(),
        });
      });

      // Tracking de la performance
      this.trackPerformanceMetrics();
    }
  }

  /**
   * Définir l'utilisateur actuel
   */
  setUser(userId: string, metadata?: Record<string, any>) {
    this.userId = userId;
    this.trackEvent('user', 'login', userId, undefined, metadata);
  }

  /**
   * Tracking d'événements personnalisés
   */
  trackEvent(
    category: string,
    action: string,
    label?: string,
    value?: number,
    metadata?: Record<string, any>
  ) {
    const event: AnalyticsEvent = {
      category,
      action,
      label,
      value,
      timestamp: Date.now(),
      userId: this.userId,
      sessionId: this.sessionId,
      metadata,
    };

    this.events.push(event);
    this.persistEvents();

    console.log('📊 Analytics Event:', event);
  }

  /**
   * Tracking spécialisé pour BarStock Wise
   */
  trackPageView(page: string, metadata?: Record<string, any>) {
    // Enregistrer le temps passé sur la page précédente
    if (this.currentPage) {
      this.trackTimeSpent();
    }

    this.currentPage = page;
    this.pageStartTime = Date.now();
    
    this.trackEvent('navigation', 'page_view', page, undefined, {
      referrer: document.referrer,
      userAgent: navigator.userAgent,
      ...metadata,
    });
  }

  trackStockAction(action: 'view' | 'adjust' | 'sync', productId?: string, metadata?: Record<string, any>) {
    this.trackEvent('stock', action, productId, undefined, metadata);
  }

  trackSale(amount: number, productCount: number, metadata?: Record<string, any>) {
    this.trackEvent('sales', 'completed', undefined, amount, {
      productCount,
      ...metadata,
    });
  }

  trackExport(type: 'pdf' | 'csv' | 'html', category: string, recordCount?: number) {
    this.trackEvent('export', type, category, recordCount);
  }

  trackError(error: string, metadata?: Record<string, any>) {
    this.trackEvent('error', 'occurred', error, undefined, {
      page: this.currentPage,
      timestamp: Date.now(),
      ...metadata,
    });
  }

  trackPerformance(metric: string, value: number, metadata?: Record<string, any>) {
    this.trackEvent('performance', metric, undefined, value, metadata);
  }

  /**
   * Tracking du temps passé
   */
  private trackTimeSpent() {
    if (this.currentPage && this.pageStartTime) {
      const timeSpent = Date.now() - this.pageStartTime;
      this.trackEvent('engagement', 'time_spent', this.currentPage, timeSpent);
    }
  }

  /**
   * Métriques de performance automatiques
   */
  private trackPerformanceMetrics() {
    if (typeof window !== 'undefined' && 'performance' in window) {
      // Attendre que les métriques soient disponibles
      setTimeout(() => {
        const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
        
        if (navigation) {
          this.trackPerformance('page_load_time', navigation.loadEventEnd - navigation.fetchStart);
          this.trackPerformance('dom_content_loaded', navigation.domContentLoadedEventEnd - navigation.fetchStart);
          this.trackPerformance('first_paint', navigation.responseEnd - navigation.fetchStart);
        }

        // Métriques de mémoire (si disponibles)
        if ('memory' in performance) {
          const memory = (performance as any).memory;
          this.trackPerformance('memory_used', memory.usedJSHeapSize);
          this.trackPerformance('memory_total', memory.totalJSHeapSize);
        }
      }, 1000);
    }
  }

  /**
   * Analyse des données collectées
   */
  getAnalytics(): {
    userBehavior: UserBehavior;
    businessMetrics: BusinessMetrics;
    sessionInfo: any;
  } {
    const userBehavior = this.analyzeUserBehavior();
    const businessMetrics = this.analyzeBusinessMetrics();
    const sessionInfo = this.getSessionInfo();

    return {
      userBehavior,
      businessMetrics,
      sessionInfo,
    };
  }

  private analyzeUserBehavior(): UserBehavior {
    const pageViews: Record<string, number> = {};
    const timeSpent: Record<string, number> = {};
    const actions: Record<string, number> = {};
    const errors: Array<{ error: string; timestamp: number; page: string }> = [];

    this.events.forEach(event => {
      // Page views
      if (event.category === 'navigation' && event.action === 'page_view' && event.label) {
        pageViews[event.label] = (pageViews[event.label] || 0) + 1;
      }

      // Time spent
      if (event.category === 'engagement' && event.action === 'time_spent' && event.label && event.value) {
        timeSpent[event.label] = (timeSpent[event.label] || 0) + event.value;
      }

      // Actions
      const actionKey = `${event.category}:${event.action}`;
      actions[actionKey] = (actions[actionKey] || 0) + 1;

      // Errors
      if (event.category === 'error') {
        errors.push({
          error: event.label || 'Unknown error',
          timestamp: event.timestamp,
          page: event.metadata?.page || 'Unknown',
        });
      }
    });

    return { pageViews, timeSpent, actions, errors };
  }

  private analyzeBusinessMetrics(): BusinessMetrics {
    // Analyser les tendances de ventes
    const salesTrends = this.events
      .filter(e => e.category === 'sales' && e.action === 'completed')
      .reduce((acc, event) => {
        const date = new Date(event.timestamp).toISOString().split('T')[0];
        const existing = acc.find(item => item.date === date);
        
        if (existing) {
          existing.amount += event.value || 0;
          existing.count += 1;
        } else {
          acc.push({
            date,
            amount: event.value || 0,
            count: 1,
          });
        }
        
        return acc;
      }, [] as Array<{ date: string; amount: number; count: number }>);

    // Analyser les mouvements de stock
    const stockMovements = this.events
      .filter(e => e.category === 'stock')
      .map(event => ({
        product: event.label || 'Unknown',
        movement: event.value || 0,
        timestamp: event.timestamp,
      }));

    // Analyser l'activité des utilisateurs
    const userActivity = this.events
      .reduce((acc, event) => {
        if (event.userId) {
          const existing = acc.find(item => item.user === event.userId);
          if (existing) {
            existing.actions += 1;
            existing.lastSeen = Math.max(existing.lastSeen, event.timestamp);
          } else {
            acc.push({
              user: event.userId,
              actions: 1,
              lastSeen: event.timestamp,
            });
          }
        }
        return acc;
      }, [] as Array<{ user: string; actions: number; lastSeen: number }>);

    // Métriques de performance système
    const systemPerformance = this.events
      .filter(e => e.category === 'performance')
      .map(event => ({
        metric: event.action,
        value: event.value || 0,
        timestamp: event.timestamp,
      }));

    return {
      salesTrends,
      stockMovements,
      userActivity,
      systemPerformance,
    };
  }

  private getSessionInfo() {
    return {
      sessionId: this.sessionId,
      userId: this.userId,
      startTime: this.startTime,
      duration: Date.now() - this.startTime,
      eventCount: this.events.length,
      currentPage: this.currentPage,
    };
  }

  /**
   * Persistance des données
   */
  private persistEvents() {
    try {
      // Garder seulement les 1000 derniers événements
      if (this.events.length > 1000) {
        this.events = this.events.slice(-1000);
      }

      localStorage.setItem('analytics_events', JSON.stringify(this.events));
    } catch (error) {
      console.warn('Erreur sauvegarde analytics:', error);
    }
  }

  private loadEvents() {
    try {
      const stored = localStorage.getItem('analytics_events');
      if (stored) {
        this.events = JSON.parse(stored);
      }
    } catch (error) {
      console.warn('Erreur chargement analytics:', error);
      this.events = [];
    }
  }

  /**
   * Export des données analytics
   */
  exportData(format: 'json' | 'csv' = 'json') {
    const analytics = this.getAnalytics();
    
    if (format === 'csv') {
      return this.convertToCSV(this.events);
    }
    
    return JSON.stringify(analytics, null, 2);
  }

  private convertToCSV(events: AnalyticsEvent[]): string {
    const headers = ['timestamp', 'category', 'action', 'label', 'value', 'userId', 'sessionId'];
    const rows = events.map(event => [
      new Date(event.timestamp).toISOString(),
      event.category,
      event.action,
      event.label || '',
      event.value || '',
      event.userId || '',
      event.sessionId,
    ]);

    return [headers, ...rows].map(row => row.join(',')).join('\n');
  }

  /**
   * Utilitaires
   */
  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Nettoyage des données anciennes
   */
  cleanOldData(daysToKeep: number = 30) {
    const cutoffTime = Date.now() - (daysToKeep * 24 * 60 * 60 * 1000);
    this.events = this.events.filter(event => event.timestamp > cutoffTime);
    this.persistEvents();
  }
}

// Instance singleton
export const analyticsService = AnalyticsService.getInstance();

// Hook React pour les analytics
export const useAnalytics = () => {
  React.useEffect(() => {
    // Nettoyer les anciennes données au démarrage
    analyticsService.cleanOldData();
  }, []);

  return {
    trackEvent: analyticsService.trackEvent.bind(analyticsService),
    trackPageView: analyticsService.trackPageView.bind(analyticsService),
    trackStockAction: analyticsService.trackStockAction.bind(analyticsService),
    trackSale: analyticsService.trackSale.bind(analyticsService),
    trackExport: analyticsService.trackExport.bind(analyticsService),
    trackError: analyticsService.trackError.bind(analyticsService),
    trackPerformance: analyticsService.trackPerformance.bind(analyticsService),
    getAnalytics: analyticsService.getAnalytics.bind(analyticsService),
    exportData: analyticsService.exportData.bind(analyticsService),
    setUser: analyticsService.setUser.bind(analyticsService),
  };
};

export default analyticsService;
