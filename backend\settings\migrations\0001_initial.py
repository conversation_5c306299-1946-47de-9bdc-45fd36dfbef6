# Generated by Django 4.2.7 on 2025-08-05 23:31

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='SystemInfo',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('version', models.CharField(default='1.0.0', max_length=20)),
                ('database_version', models.Char<PERSON>ield(default='PostgreSQL 14', max_length=50)),
                ('server_info', models.CharField(default='Django 4.2', max_length=100)),
                ('last_backup', models.DateTimeField(blank=True, null=True)),
                ('storage_used', models.CharField(default='0 GB', max_length=20)),
                ('memory_usage', models.Char<PERSON><PERSON>(default='0%', max_length=20)),
                ('cpu_usage', models.CharField(default='0%', max_length=20)),
                ('uptime_start', models.DateTimeField(auto_now_add=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Informations Système',
                'verbose_name_plural': 'Informations Système',
            },
        ),
        migrations.CreateModel(
            name='UserPreferences',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('theme', models.CharField(choices=[('light', 'Clair'), ('dark', 'Sombre'), ('auto', 'Automatique')], default='light', max_length=20)),
                ('language', models.CharField(default='fr', max_length=10)),
                ('email_notifications', models.BooleanField(default=True)),
                ('browser_notifications', models.BooleanField(default=True)),
                ('sound_notifications', models.BooleanField(default=True)),
                ('items_per_page', models.IntegerField(default=25)),
                ('default_currency_display', models.CharField(default='BIF', max_length=10)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='preferences', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Préférences Utilisateur',
                'verbose_name_plural': 'Préférences Utilisateur',
            },
        ),
        migrations.CreateModel(
            name='SystemSettings',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('restaurant_name', models.CharField(default='BarStock Wise', max_length=200)),
                ('restaurant_address', models.TextField(default='Bujumbura, Burundi')),
                ('restaurant_phone', models.CharField(default='+257 XX XX XX XX', max_length=50)),
                ('restaurant_email', models.EmailField(default='<EMAIL>', max_length=254)),
                ('currency', models.CharField(default='BIF', max_length=10)),
                ('tax_rate', models.DecimalField(decimal_places=2, default=18.0, max_digits=5)),
                ('email_notifications_enabled', models.BooleanField(default=True)),
                ('sms_notifications_enabled', models.BooleanField(default=False)),
                ('low_stock_alerts', models.BooleanField(default=True)),
                ('daily_reports', models.BooleanField(default=True)),
                ('auto_print_receipts', models.BooleanField(default=True)),
                ('receipt_copies', models.IntegerField(default=1)),
                ('printer_name', models.CharField(default='Imprimante par défaut', max_length=200)),
                ('language', models.CharField(default='fr', max_length=10)),
                ('timezone', models.CharField(default='Africa/Bujumbura', max_length=50)),
                ('date_format', models.CharField(default='DD/MM/YYYY', max_length=20)),
                ('backup_frequency', models.CharField(default='daily', max_length=20)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Paramètres Système',
                'verbose_name_plural': 'Paramètres Système',
            },
        ),
    ]
