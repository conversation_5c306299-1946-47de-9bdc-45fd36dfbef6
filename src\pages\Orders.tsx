import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { 
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { 
  Clock, 
  Search, 
  Filter,
  CheckCircle,
  XCircle,
  AlertCircle,
  Eye,
  Edit,
  Trash2,
  Plus
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { formatCurrency } from '@/lib/currency';
import { usePermissions } from '@/contexts/PermissionsContext';

// Types pour les commandes
interface OrderItem {
  id: string;
  name: string;
  quantity: number;
  price: number;
  total: number;
}

interface Order {
  id: string;
  tableNumber: string;
  customerName?: string;
  items: OrderItem[];
  total: number;
  status: 'En attente' | 'En préparation' | 'Prêt' | 'Servi' | 'Annulé';
  priority: 'Normal' | 'Urgent' | 'VIP';
  createdAt: string;
  estimatedTime?: number; // minutes
  notes?: string;
  waiter: string;
}

// Données mock
const mockOrders: Order[] = [
  {
    id: 'ORD-001',
    tableNumber: 'Table 5',
    customerName: 'Jean Hakizimana',
    items: [
      { id: '1', name: 'Primus', quantity: 2, price: 3500, total: 7000 },
      { id: '2', name: 'Poulet yassa', quantity: 1, price: 8000, total: 8000 }
    ],
    total: 15000,
    status: 'En préparation',
    priority: 'Normal',
    createdAt: '2024-01-15T14:30:00',
    estimatedTime: 15,
    notes: 'Sans épices',
    waiter: 'Marie Uwimana'
  },
  {
    id: 'ORD-002',
    tableNumber: 'Table 2',
    items: [
      { id: '3', name: 'Chivas', quantity: 1, price: 25000, total: 25000 },
      { id: '4', name: 'Amstel', quantity: 3, price: 4000, total: 12000 }
    ],
    total: 37000,
    status: 'En attente',
    priority: 'VIP',
    createdAt: '2024-01-15T14:45:00',
    estimatedTime: 10,
    waiter: 'Paul Ndayisenga'
  },
  {
    id: 'ORD-003',
    tableNumber: 'Table 8',
    customerName: 'Alice Niragire',
    items: [
      { id: '5', name: 'Pizza Chicken', quantity: 2, price: 12000, total: 24000 },
      { id: '6', name: 'Fanta', quantity: 2, price: 2000, total: 4000 }
    ],
    total: 28000,
    status: 'Prêt',
    priority: 'Urgent',
    createdAt: '2024-01-15T13:20:00',
    waiter: 'Marie Uwimana'
  }
];

const Orders = () => {
  const [orders, setOrders] = useState<Order[]>(mockOrders);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('Tous');
  const [priorityFilter, setPriorityFilter] = useState('Tous');
  const { toast } = useToast();
  const { hasPermission } = usePermissions();

  const filteredOrders = orders.filter(order => {
    const matchesSearch = order.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         order.tableNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         (order.customerName?.toLowerCase().includes(searchTerm.toLowerCase()) ?? false);
    const matchesStatus = statusFilter === 'Tous' || order.status === statusFilter;
    const matchesPriority = priorityFilter === 'Tous' || order.priority === priorityFilter;
    
    return matchesSearch && matchesStatus && matchesPriority;
  });

  const getStatusBadge = (status: string) => {
    const variants = {
      'En attente': { variant: 'secondary' as const, icon: Clock },
      'En préparation': { variant: 'default' as const, icon: AlertCircle },
      'Prêt': { variant: 'success' as const, icon: CheckCircle },
      'Servi': { variant: 'outline' as const, icon: CheckCircle },
      'Annulé': { variant: 'destructive' as const, icon: XCircle }
    };
    
    const { variant, icon: Icon } = variants[status as keyof typeof variants];
    
    return (
      <Badge variant={variant} className="flex items-center gap-1 w-fit">
        <Icon className="w-3 h-3" />
        {status}
      </Badge>
    );
  };

  const getPriorityBadge = (priority: string) => {
    const variants = {
      'Normal': 'outline' as const,
      'Urgent': 'warning' as const,
      'VIP': 'default' as const
    };
    
    return (
      <Badge variant={variants[priority as keyof typeof variants] || 'outline'}>
        {priority}
      </Badge>
    );
  };

  const updateOrderStatus = (orderId: string, newStatus: Order['status']) => {
    if (!hasPermission('orders.update')) {
      toast({
        title: "Accès refusé",
        description: "Vous n'avez pas l'autorisation de modifier les commandes.",
        variant: "destructive"
      });
      return;
    }

    setOrders(prev => prev.map(order => 
      order.id === orderId ? { ...order, status: newStatus } : order
    ));

    toast({
      title: "Commande mise à jour",
      description: `Statut changé vers: ${newStatus}`,
    });
  };

  const deleteOrder = (orderId: string) => {
    if (!hasPermission('orders.delete')) {
      toast({
        title: "Accès refusé",
        description: "Vous n'avez pas l'autorisation de supprimer les commandes.",
        variant: "destructive"
      });
      return;
    }

    setOrders(prev => prev.filter(order => order.id !== orderId));
    toast({
      title: "Commande supprimée",
      description: "La commande a été supprimée avec succès.",
    });
  };

  const getOrderStats = () => {
    const pending = orders.filter(o => o.status === 'En attente').length;
    const preparing = orders.filter(o => o.status === 'En préparation').length;
    const ready = orders.filter(o => o.status === 'Prêt').length;
    const totalValue = orders.reduce((sum, order) => sum + order.total, 0);
    
    return { pending, preparing, ready, totalValue };
  };

  const stats = getOrderStats();

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Gestion des Commandes</h1>
          <p className="text-muted-foreground">
            Suivez et gérez toutes les commandes en temps réel
          </p>
        </div>
        
        {hasPermission('orders.create') && (
          <Button className="bg-gradient-to-r from-primary to-accent">
            <Plus className="w-4 h-4 mr-2" />
            Nouvelle commande
          </Button>
        )}
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">En Attente</CardTitle>
            <Clock className="h-4 w-4 text-warning" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-warning">{stats.pending}</div>
            <p className="text-xs text-muted-foreground">commandes</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">En Préparation</CardTitle>
            <AlertCircle className="h-4 w-4 text-primary" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-primary">{stats.preparing}</div>
            <p className="text-xs text-muted-foreground">en cours</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Prêtes</CardTitle>
            <CheckCircle className="h-4 w-4 text-success" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-success">{stats.ready}</div>
            <p className="text-xs text-muted-foreground">à servir</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Valeur Totale</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(stats.totalValue)}</div>
            <p className="text-xs text-muted-foreground">commandes actives</p>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Rechercher une commande..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-40">
                <SelectValue placeholder="Statut" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="Tous">Tous statuts</SelectItem>
                <SelectItem value="En attente">En attente</SelectItem>
                <SelectItem value="En préparation">En préparation</SelectItem>
                <SelectItem value="Prêt">Prêt</SelectItem>
                <SelectItem value="Servi">Servi</SelectItem>
                <SelectItem value="Annulé">Annulé</SelectItem>
              </SelectContent>
            </Select>
            
            <Select value={priorityFilter} onValueChange={setPriorityFilter}>
              <SelectTrigger className="w-40">
                <SelectValue placeholder="Priorité" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="Tous">Toutes priorités</SelectItem>
                <SelectItem value="Normal">Normal</SelectItem>
                <SelectItem value="Urgent">Urgent</SelectItem>
                <SelectItem value="VIP">VIP</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Orders Table */}
      <Card>
        <CardHeader>
          <CardTitle>Commandes Actives</CardTitle>
          <CardDescription>
            {filteredOrders.length} commande(s) trouvée(s)
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>N° Commande</TableHead>
                <TableHead>Table</TableHead>
                <TableHead>Client</TableHead>
                <TableHead>Statut</TableHead>
                <TableHead>Priorité</TableHead>
                <TableHead>Total</TableHead>
                <TableHead>Temps</TableHead>
                <TableHead>Serveur</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredOrders.map((order) => (
                <TableRow key={order.id}>
                  <TableCell className="font-medium">{order.id}</TableCell>
                  <TableCell>{order.tableNumber}</TableCell>
                  <TableCell>{order.customerName || '-'}</TableCell>
                  <TableCell>{getStatusBadge(order.status)}</TableCell>
                  <TableCell>{getPriorityBadge(order.priority)}</TableCell>
                  <TableCell className="font-medium">{formatCurrency(order.total)}</TableCell>
                  <TableCell>
                    {order.estimatedTime ? `${order.estimatedTime} min` : '-'}
                  </TableCell>
                  <TableCell className="text-sm">{order.waiter}</TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <Button size="sm" variant="outline">
                        <Eye className="w-4 h-4" />
                      </Button>
                      
                      {hasPermission('orders.update') && (
                        <Select 
                          value={order.status} 
                          onValueChange={(newStatus: Order['status']) => updateOrderStatus(order.id, newStatus)}
                        >
                          <SelectTrigger className="w-32 h-8">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="En attente">En attente</SelectItem>
                            <SelectItem value="En préparation">En préparation</SelectItem>
                            <SelectItem value="Prêt">Prêt</SelectItem>
                            <SelectItem value="Servi">Servi</SelectItem>
                            <SelectItem value="Annulé">Annulé</SelectItem>
                          </SelectContent>
                        </Select>
                      )}
                      
                      {hasPermission('orders.delete') && (
                        <Button 
                          size="sm" 
                          variant="outline"
                          onClick={() => deleteOrder(order.id)}
                        >
                          <Trash2 className="w-4 h-4 text-destructive" />
                        </Button>
                      )}
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
};

export default Orders;