import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { 
  CheckCircle, 
  XCircle, 
  Clock, 
  Server, 
  Database,
  Wifi,
  AlertTriangle
} from 'lucide-react';

const ApiTest = () => {
  const [testResults, setTestResults] = useState<any>({});
  const [isLoading, setIsLoading] = useState(false);
  const [customUrl, setCustomUrl] = useState('http://localhost:8000/api');

  const testEndpoint = async (name: string, url: string) => {
    try {
      const response = await fetch(url);
      const data = await response.json();
      
      return {
        name,
        url,
        status: response.status,
        success: response.ok,
        data: data,
        error: null,
        timestamp: new Date().toISOString()
      };
    } catch (error: any) {
      return {
        name,
        url,
        status: 0,
        success: false,
        data: null,
        error: error.message,
        timestamp: new Date().toISOString()
      };
    }
  };

  const runAllTests = async () => {
    setIsLoading(true);
    const baseUrl = customUrl;
    
    const endpoints = [
      { name: 'API Health', url: `${baseUrl}/` },
      { name: 'Products', url: `${baseUrl}/products/` },
      { name: 'Inventory', url: `${baseUrl}/inventory/` },
      { name: 'Sales', url: `${baseUrl}/sales/` },
      { name: 'Tables', url: `${baseUrl}/sales/tables/` },
      { name: 'Suppliers', url: `${baseUrl}/suppliers/` },
    ];

    const results: any = {};
    
    for (const endpoint of endpoints) {
      console.log(`Testing ${endpoint.name}...`);
      results[endpoint.name] = await testEndpoint(endpoint.name, endpoint.url);
      // Petit délai entre les tests
      await new Promise(resolve => setTimeout(resolve, 100));
    }
    
    setTestResults(results);
    setIsLoading(false);
  };

  const getStatusIcon = (result: any) => {
    if (!result) return <Clock className="w-4 h-4 text-gray-400" />;
    if (result.success) return <CheckCircle className="w-4 h-4 text-green-500" />;
    return <XCircle className="w-4 h-4 text-red-500" />;
  };

  const getStatusBadge = (result: any) => {
    if (!result) return <Badge variant="secondary">Non testé</Badge>;
    if (result.success) return <Badge className="bg-green-500">✓ {result.status}</Badge>;
    return <Badge variant="destructive">✗ {result.status || 'Erreur'}</Badge>;
  };

  return (
    <div className="space-y-6 p-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Test API Backend</h1>
          <p className="text-muted-foreground">
            Vérifiez la connectivité avec l'API Django
          </p>
        </div>
        <Button onClick={runAllTests} disabled={isLoading}>
          {isLoading ? (
            <>
              <Clock className="w-4 h-4 mr-2 animate-spin" />
              Test en cours...
            </>
          ) : (
            <>
              <Server className="w-4 h-4 mr-2" />
              Tester l'API
            </>
          )}
        </Button>
      </div>

      {/* Configuration */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Wifi className="w-5 h-5" />
            Configuration
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label htmlFor="api-url">URL de l'API</Label>
            <Input
              id="api-url"
              value={customUrl}
              onChange={(e) => setCustomUrl(e.target.value)}
              placeholder="http://localhost:8000/api"
            />
          </div>
          <div className="text-sm text-muted-foreground">
            <p><strong>Frontend:</strong> {window.location.origin}</p>
            <p><strong>API configurée:</strong> {import.meta.env.VITE_API_URL || 'Non définie'}</p>
          </div>
        </CardContent>
      </Card>

      {/* Résultats des tests */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {Object.entries(testResults).map(([name, result]: [string, any]) => (
          <Card key={name}>
            <CardHeader className="pb-3">
              <CardTitle className="flex items-center justify-between text-base">
                <span className="flex items-center gap-2">
                  {getStatusIcon(result)}
                  {name}
                </span>
                {getStatusBadge(result)}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <div className="text-sm">
                <p><strong>URL:</strong> {result?.url}</p>
                {result?.timestamp && (
                  <p><strong>Testé:</strong> {new Date(result.timestamp).toLocaleTimeString()}</p>
                )}
              </div>
              
              {result?.error && (
                <div className="p-2 bg-red-50 border border-red-200 rounded text-sm text-red-700">
                  <strong>Erreur:</strong> {result.error}
                </div>
              )}
              
              {result?.data && (
                <details className="text-xs">
                  <summary className="cursor-pointer font-medium">Données reçues</summary>
                  <pre className="mt-2 p-2 bg-gray-100 rounded overflow-auto max-h-32">
                    {JSON.stringify(result.data, null, 2)}
                  </pre>
                </details>
              )}
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Instructions de dépannage */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <AlertTriangle className="w-5 h-5" />
            Dépannage
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <h4 className="font-medium">Si l'API ne répond pas :</h4>
            <ul className="list-disc list-inside text-sm space-y-1 text-muted-foreground">
              <li>Vérifiez que le serveur Django est démarré : <code>python manage.py runserver 8000</code></li>
              <li>Vérifiez l'URL de l'API dans la configuration</li>
              <li>Vérifiez les paramètres CORS dans Django</li>
              <li>Vérifiez que la base de données est accessible</li>
            </ul>
          </div>
          
          <div className="space-y-2">
            <h4 className="font-medium">Commandes utiles :</h4>
            <div className="space-y-1 text-sm font-mono bg-gray-100 p-3 rounded">
              <p># Démarrer le serveur Django</p>
              <p>cd backend && python manage.py runserver 8000</p>
              <p></p>
              <p># Vérifier les migrations</p>
              <p>python manage.py showmigrations</p>
              <p></p>
              <p># Créer un superuser</p>
              <p>python manage.py createsuperuser</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default ApiTest;
