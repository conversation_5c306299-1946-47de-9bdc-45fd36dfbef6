# Generated by Django 5.2.4 on 2025-07-30 14:12

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Supplier',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200, verbose_name='Nom du fournisseur')),
                ('contact_person', models.CharField(blank=True, max_length=100, null=True, verbose_name='Personne de contact')),
                ('phone', models.CharField(blank=True, max_length=15, null=True, validators=[django.core.validators.RegexValidator('^\\+?1?\\d{9,15}$')], verbose_name='Téléphone')),
                ('email', models.EmailField(blank=True, max_length=254, null=True, verbose_name='Email')),
                ('address', models.TextField(blank=True, null=True, verbose_name='Adresse')),
                ('city', models.CharField(blank=True, max_length=100, null=True, verbose_name='Ville')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='Notes')),
                ('is_active', models.BooleanField(default=True, verbose_name='Fournisseur actif')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Date de création')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Date de modification')),
            ],
            options={
                'verbose_name': 'Fournisseur',
                'verbose_name_plural': 'Fournisseurs',
                'ordering': ['name'],
            },
        ),
    ]
