from rest_framework import serializers
from .models import StockMovement, Purchase, PurchaseItem
from products.models import Product
from suppliers.models import Supplier
from accounts.models import User

class StockMovementSerializer(serializers.ModelSerializer):
    product_name = serializers.CharField(source='product.name', read_only=True)
    user_name = serializers.CharField(source='user.get_full_name', read_only=True)
    movement_type_display = serializers.CharField(source='get_movement_type_display', read_only=True)
    
    class Meta:
        model = StockMovement
        fields = [
            'id', 'product', 'product_name', 'movement_type', 'movement_type_display',
            'quantity', 'unit_cost', 'total_cost', 'reference', 'notes',
            'user', 'user_name', 'created_at'
        ]
        read_only_fields = ['created_at', 'total_cost']
    
    def validate_quantity(self, value):
        if value <= 0:
            raise serializers.ValidationError("La quantité doit être positive.")
        return value
    
    def validate(self, data):
        product = data.get('product')
        movement_type = data.get('movement_type')
        quantity = data.get('quantity')
        
        # Vérifier le stock pour les sorties
        if movement_type == 'out' and product:
            if product.current_stock < quantity:
                raise serializers.ValidationError(
                    f"Stock insuffisant. Stock actuel: {product.current_stock}"
                )
        
        return data

class PurchaseItemSerializer(serializers.ModelSerializer):
    product_name = serializers.CharField(source='product.name', read_only=True)
    total_cost = serializers.DecimalField(max_digits=12, decimal_places=2, read_only=True)
    
    class Meta:
        model = PurchaseItem
        fields = [
            'id', 'product', 'product_name', 'quantity', 
            'unit_cost', 'total_cost'
        ]

class PurchaseSerializer(serializers.ModelSerializer):
    supplier_name = serializers.CharField(source='supplier.name', read_only=True)
    user_name = serializers.CharField(source='user.get_full_name', read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    items = PurchaseItemSerializer(many=True, read_only=True)
    items_count = serializers.IntegerField(source='items.count', read_only=True)
    
    class Meta:
        model = Purchase
        fields = [
            'id', 'supplier', 'supplier_name', 'purchase_date', 'reference',
            'status', 'status_display', 'total_amount', 'notes',
            'user', 'user_name', 'items', 'items_count', 'created_at'
        ]
        read_only_fields = ['created_at', 'total_amount']

class StockSummarySerializer(serializers.Serializer):
    product_id = serializers.IntegerField()
    product_name = serializers.CharField()
    category_name = serializers.CharField()
    current_stock = serializers.IntegerField()
    minimum_stock = serializers.IntegerField()
    stock_value = serializers.DecimalField(max_digits=12, decimal_places=2)
    last_movement_date = serializers.DateTimeField()
    needs_restock = serializers.BooleanField()
