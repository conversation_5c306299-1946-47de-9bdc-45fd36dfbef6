import React, { useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Printer } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { formatCurrency } from '@/lib/currency';

interface InvoiceItem {
  id: string;
  product_name: string;
  quantity: number;
  unit_price: number;
  total_price: number;
}

interface InvoiceData {
  id: string;
  invoice_number: string;
  customer_name?: string;
  customer_email?: string;
  customer_phone?: string;
  customer_address?: string;
  table_number?: string;
  items: InvoiceItem[];
  subtotal: number;
  tax_amount: number;
  discount_amount: number;
  total_amount: number;
  payment_method: string;
  status: string;
  created_at: string;
  server: string;
  notes?: string;
}

interface InvoicePrinterProps {
  invoice: InvoiceData;
  onPrint?: () => void;
}

const InvoicePrinter: React.FC<InvoicePrinterProps> = ({ invoice, onPrint }) => {
  const printRef = useRef<HTMLDivElement>(null);
  const { toast } = useToast();

  const handlePrint = () => {
    if (onPrint) {
      onPrint();
      return;
    }

    try {
      const printWindow = window.open('', '_blank');
      if (printWindow && printRef.current) {
        const printContent = generatePrintContent();
        printWindow.document.write(printContent);
        printWindow.document.close();
        
        // Attendre que le contenu soit chargé
        setTimeout(() => {
          printWindow.print();
          printWindow.close();
        }, 500);
      }
      
      toast({
        title: "Impression en cours",
        description: "La facture est en cours d'impression.",
      });
    } catch (error) {
      console.error('Erreur lors de l\'impression:', error);
      toast({
        title: "Erreur d'impression",
        description: "Impossible d'imprimer la facture.",
        variant: "destructive",
      });
    }
  };

  const generatePrintContent = () => {
    return `
      <!DOCTYPE html>
      <html>
        <head>
          <title>Facture ${invoice.invoice_number}</title>
          <style>
            @media print {
              body { margin: 0; padding: 20px; font-family: Arial, sans-serif; }
              .no-print { display: none; }
              .page-break { page-break-before: always; }
            }
            body { 
              font-family: Arial, sans-serif; 
              max-width: 800px; 
              margin: 0 auto; 
              padding: 20px; 
              line-height: 1.4;
            }
            .header { 
              text-align: center; 
              margin-bottom: 30px; 
              border-bottom: 2px solid #333; 
              padding-bottom: 20px; 
            }
            .company-info { margin-bottom: 20px; }
            .invoice-details { 
              display: flex; 
              justify-content: space-between; 
              margin-bottom: 30px; 
              flex-wrap: wrap;
            }
            .customer-info, .invoice-info { 
              flex: 1; 
              min-width: 300px;
              margin-bottom: 20px;
            }
            .items-table { 
              width: 100%; 
              border-collapse: collapse; 
              margin-bottom: 20px; 
            }
            .items-table th, .items-table td { 
              border: 1px solid #ddd; 
              padding: 8px; 
              text-align: left; 
            }
            .items-table th { 
              background-color: #f5f5f5; 
              font-weight: bold;
            }
            .items-table .quantity { text-align: center; }
            .items-table .price { text-align: right; }
            .items-table .total { text-align: right; font-weight: bold; }
            .totals { 
              text-align: right; 
              margin-top: 20px; 
              border-top: 2px solid #333;
              padding-top: 20px;
            }
            .total-row { 
              font-weight: bold; 
              font-size: 1.1em; 
              margin: 5px 0;
            }
            .subtotal-row, .tax-row, .discount-row {
              margin: 3px 0;
              font-size: 0.9em;
            }
            .footer { 
              margin-top: 40px; 
              text-align: center; 
              font-size: 0.9em; 
              color: #666; 
              border-top: 1px solid #ddd;
              padding-top: 20px;
            }
            .status-badge { 
              display: inline-block; 
              padding: 4px 8px; 
              border-radius: 4px; 
              font-size: 0.8em; 
              font-weight: bold; 
            }
            .status-paid { background-color: #d4edda; color: #155724; }
            .status-pending { background-color: #fff3cd; color: #856404; }
            .notes {
              margin-top: 20px;
              padding: 15px;
              background-color: #f8f9fa;
              border-left: 4px solid #007bff;
              border-radius: 4px;
            }
            .qr-code {
              text-align: center;
              margin: 20px 0;
            }
            .qr-code img {
              max-width: 100px;
              height: auto;
            }
            @media screen {
              .print-only { display: none; }
            }
            @media print {
              .print-only { display: block; }
              .screen-only { display: none; }
            }
          </style>
        </head>
        <body>
          <div class="header">
            <h1 style="margin: 0; color: #2c3e50; font-size: 28px;">BarStock Wise</h1>
            <p style="margin: 5px 0; color: #7f8c8d; font-size: 16px;">Restaurant & Bar</p>
            <p style="margin: 5px 0; color: #7f8c8d;">Bujumbura, Burundi</p>
            <p style="margin: 5px 0; color: #7f8c8d; font-size: 14px;">
              Tél: +257 22 123 456 | Email: <EMAIL>
            </p>
            <p style="margin: 5px 0; color: #7f8c8d; font-size: 14px;">
              Site web: www.barstockwise.bi
            </p>
          </div>
          
          <div class="invoice-details">
            <div class="customer-info">
              <h3 style="margin: 0 0 10px 0; color: #2c3e50; border-bottom: 1px solid #ecf0f1; padding-bottom: 5px;">
                INFORMATIONS CLIENT
              </h3>
              <p style="margin: 5px 0;"><strong>Nom:</strong> ${invoice.customer_name || 'Client non spécifié'}</p>
              ${invoice.customer_email ? `<p style="margin: 5px 0;"><strong>Email:</strong> ${invoice.customer_email}</p>` : ''}
              ${invoice.customer_phone ? `<p style="margin: 5px 0;"><strong>Téléphone:</strong> ${invoice.customer_phone}</p>` : ''}
              ${invoice.customer_address ? `<p style="margin: 5px 0;"><strong>Adresse:</strong> ${invoice.customer_address}</p>` : ''}
              ${invoice.table_number ? `<p style="margin: 5px 0;"><strong>Table:</strong> ${invoice.table_number}</p>` : ''}
            </div>
            
            <div class="invoice-info">
              <h3 style="margin: 0 0 10px 0; color: #2c3e50; border-bottom: 1px solid #ecf0f1; padding-bottom: 5px;">
                DÉTAILS FACTURE
              </h3>
              <p style="margin: 5px 0;"><strong>N° Facture:</strong> ${invoice.invoice_number}</p>
              <p style="margin: 5px 0;"><strong>Date:</strong> ${new Date(invoice.created_at).toLocaleDateString('fr-FR')}</p>
              <p style="margin: 5px 0;"><strong>Heure:</strong> ${new Date(invoice.created_at).toLocaleTimeString('fr-FR')}</p>
              <p style="margin: 5px 0;"><strong>Serveur:</strong> ${invoice.server}</p>
              <p style="margin: 5px 0;"><strong>Statut:</strong> 
                <span class="status-badge ${invoice.status === 'paid' ? 'status-paid' : 'status-pending'}">
                  ${invoice.status === 'paid' ? 'Payé' : 'En attente'}
                </span>
              </p>
            </div>
          </div>
          
          <table class="items-table">
            <thead>
              <tr>
                <th style="width: 40%;">Article</th>
                <th style="width: 15%;" class="quantity">Quantité</th>
                <th style="width: 20%;" class="price">Prix unitaire</th>
                <th style="width: 25%;" class="total">Total</th>
              </tr>
            </thead>
            <tbody>
              ${invoice.items.map(item => `
                <tr>
                  <td>${item.product_name || item.product?.name || 'Article'}</td>
                  <td class="quantity">${item.quantity}</td>
                  <td class="price">${formatCurrency(item.unit_price || 0)}</td>
                  <td class="total">${formatCurrency(item.total_price || item.subtotal || (item.quantity * item.unit_price) || 0)}</td>
                </tr>
              `).join('')}
            </tbody>
          </table>
          
          <div class="totals">
            <div class="subtotal-row">
              <strong>Sous-total:</strong> ${formatCurrency(invoice.subtotal)}
            </div>
            ${invoice.tax_amount > 0 ? `
              <div class="tax-row">
                <strong>TVA:</strong> ${formatCurrency(invoice.tax_amount)}
              </div>
            ` : ''}
            ${invoice.discount_amount > 0 ? `
              <div class="discount-row">
                <strong>Remise:</strong> -${formatCurrency(invoice.discount_amount)}
              </div>
            ` : ''}
            <div class="total-row">
              <strong>TOTAL:</strong> ${formatCurrency(invoice.total_amount)}
            </div>
            <div style="margin-top: 10px; font-size: 0.9em; color: #7f8c8d;">
              <strong>Mode de paiement:</strong> ${invoice.payment_method}
            </div>
          </div>
          
          ${invoice.notes ? `
            <div class="notes">
              <strong>Notes:</strong> ${invoice.notes}
            </div>
          ` : ''}
          
          <div class="qr-code print-only">
            <p style="margin: 5px 0; font-size: 12px; color: #7f8c8d;">
              Scannez pour vérifier l'authenticité
            </p>
            <div style="width: 100px; height: 100px; border: 1px solid #ddd; margin: 0 auto; display: flex; align-items: center; justify-content: center; font-size: 10px; color: #7f8c8d;">
              QR Code<br/>${invoice.invoice_number}
            </div>
          </div>
          
          <div class="footer">
            <p style="margin: 5px 0; font-weight: bold;">Merci de votre visite !</p>
            <p style="margin: 5px 0;">BarStock Wise - Votre satisfaction est notre priorité</p>
            <p style="margin: 5px 0; font-size: 12px;">
              Cette facture est générée automatiquement par notre système
            </p>
          </div>
        </body>
      </html>
    `;
  };

  return (
    <div>
      <Button 
        variant="outline" 
        onClick={handlePrint}
        className="flex items-center gap-2"
      >
        <Printer className="w-4 h-4" />
        Imprimer
      </Button>
      
      {/* Contenu caché pour l'impression */}
      <div ref={printRef} style={{ display: 'none' }}>
        {/* Le contenu sera généré dynamiquement lors de l'impression */}
      </div>
    </div>
  );
};

export default InvoicePrinter; 