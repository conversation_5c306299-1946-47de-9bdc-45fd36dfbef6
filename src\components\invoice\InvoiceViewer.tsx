import React, { useState, useRef } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  <PERSON><PERSON>,
  <PERSON>alogContent,
  <PERSON><PERSON>Header,
  <PERSON><PERSON>Title,
  DialogTrigger,
} from '@/components/ui/dialog';
import { 
  Eye, 
  Printer, 
  Download, 
  FileText, 
  Calendar,
  User,
  MapPin,
  Phone,
  Mail,
  Receipt
} from 'lucide-react';
import { formatCurrency } from '@/lib/currency';
import { useToast } from '@/hooks/use-toast';

interface InvoiceItem {
  id: string;
  product_name: string;
  quantity: number;
  unit_price: number;
  total_price: number;
}

interface InvoiceData {
  id: string;
  invoice_number: string;
  customer_name?: string;
  customer_email?: string;
  customer_phone?: string;
  customer_address?: string;
  table_number?: string;
  items: InvoiceItem[];
  subtotal: number;
  tax_amount: number;
  discount_amount: number;
  total_amount: number;
  payment_method: string;
  status: string;
  created_at: string;
  server: string;
  notes?: string;
}

interface InvoiceViewerProps {
  invoice: InvoiceData;
  onPrint?: () => void;
  onDownload?: () => void;
}

const InvoiceViewer: React.FC<InvoiceViewerProps> = ({ 
  invoice, 
  onPrint, 
  onDownload 
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [isPrinting, setIsPrinting] = useState(false);
  const [isDownloading, setIsDownloading] = useState(false);
  const invoiceRef = useRef<HTMLDivElement>(null);
  const { toast } = useToast();

  const handlePrint = async () => {
    if (onPrint) {
      onPrint();
      return;
    }

    setIsPrinting(true);
    try {
      const printWindow = window.open('', '_blank');
      if (printWindow && invoiceRef.current) {
        const printContent = generatePrintContent();
        printWindow.document.write(printContent);
        printWindow.document.close();
        
        // Attendre que le contenu soit chargé
        setTimeout(() => {
          printWindow.print();
          printWindow.close();
        }, 500);
      }
      
      toast({
        title: "Impression en cours",
        description: "La facture est en cours d'impression.",
      });
    } catch (error) {
      console.error('Erreur lors de l\'impression:', error);
      toast({
        title: "Erreur d'impression",
        description: "Impossible d'imprimer la facture.",
        variant: "destructive",
      });
    } finally {
      setIsPrinting(false);
    }
  };

  const handleDownload = async () => {
    if (onDownload) {
      onDownload();
      return;
    }

    setIsDownloading(true);
    try {
      const pdfContent = generatePDFContent();
      const blob = new Blob([pdfContent], { type: 'application/pdf' });
      const url = URL.createObjectURL(blob);
      
      const link = document.createElement('a');
      link.href = url;
      link.download = `facture_${invoice.invoice_number}.pdf`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
      
      toast({
        title: "Téléchargement réussi",
        description: "La facture a été téléchargée avec succès.",
      });
    } catch (error) {
      console.error('Erreur lors du téléchargement:', error);
      toast({
        title: "Erreur de téléchargement",
        description: "Impossible de télécharger la facture.",
        variant: "destructive",
      });
    } finally {
      setIsDownloading(false);
    }
  };

  const generatePrintContent = () => {
    return `
      <!DOCTYPE html>
      <html>
        <head>
          <title>Facture ${invoice.invoice_number}</title>
          <style>
            @media print {
              body { margin: 0; padding: 20px; font-family: Arial, sans-serif; }
              .no-print { display: none; }
            }
            body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
            .header { text-align: center; margin-bottom: 30px; border-bottom: 2px solid #333; padding-bottom: 20px; }
            .company-info { margin-bottom: 20px; }
            .invoice-details { display: flex; justify-content: space-between; margin-bottom: 30px; }
            .customer-info, .invoice-info { flex: 1; }
            .items-table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
            .items-table th, .items-table td { border: 1px solid #ddd; padding: 8px; text-align: left; }
            .items-table th { background-color: #f5f5f5; }
            .totals { text-align: right; margin-top: 20px; }
            .total-row { font-weight: bold; font-size: 1.1em; }
            .footer { margin-top: 40px; text-align: center; font-size: 0.9em; color: #666; }
            .status-badge { 
              display: inline-block; 
              padding: 4px 8px; 
              border-radius: 4px; 
              font-size: 0.8em; 
              font-weight: bold; 
            }
            .status-paid { background-color: #d4edda; color: #155724; }
            .status-pending { background-color: #fff3cd; color: #856404; }
          </style>
        </head>
        <body>
          <div class="header">
            <h1>BarStock Wise</h1>
            <p>Restaurant & Bar</p>
            <p>Bujumbura, Burundi</p>
            <p>Tél: +257 22 123 456 | Email: <EMAIL></p>
          </div>
          
          <div class="invoice-details">
            <div class="customer-info">
              <h3>Client</h3>
              <p><strong>Nom:</strong> ${invoice.customer_name || 'Client non spécifié'}</p>
              ${invoice.customer_email ? `<p><strong>Email:</strong> ${invoice.customer_email}</p>` : ''}
              ${invoice.customer_phone ? `<p><strong>Téléphone:</strong> ${invoice.customer_phone}</p>` : ''}
              ${invoice.customer_address ? `<p><strong>Adresse:</strong> ${invoice.customer_address}</p>` : ''}
              ${invoice.table_number ? `<p><strong>Table:</strong> ${invoice.table_number}</p>` : ''}
            </div>
            
            <div class="invoice-info">
              <h3>Facture</h3>
              <p><strong>N° Facture:</strong> ${invoice.invoice_number}</p>
              <p><strong>Date:</strong> ${new Date(invoice.created_at).toLocaleDateString('fr-FR')}</p>
              <p><strong>Heure:</strong> ${new Date(invoice.created_at).toLocaleTimeString('fr-FR')}</p>
              <p><strong>Serveur:</strong> ${invoice.server}</p>
              <p><strong>Statut:</strong> 
                <span class="status-badge ${invoice.status === 'paid' ? 'status-paid' : 'status-pending'}">
                  ${invoice.status === 'paid' ? 'Payé' : 'En attente'}
                </span>
              </p>
            </div>
          </div>
          
          <table class="items-table">
            <thead>
              <tr>
                <th>Article</th>
                <th>Quantité</th>
                <th>Prix unitaire</th>
                <th>Total</th>
              </tr>
            </thead>
            <tbody>
              ${(invoice.items || []).map(item => `
                <tr>
                  <td>${item.product_name || item.product?.name || 'Article'}</td>
                  <td>${item.quantity}</td>
                  <td>${formatCurrency(item.unit_price || 0)}</td>
                  <td>${formatCurrency(item.total_price || item.subtotal || (item.quantity * item.unit_price) || 0)}</td>
                </tr>
              `).join('')}
            </tbody>
          </table>
          
          <div class="totals">
            <p><strong>Sous-total:</strong> ${formatCurrency(invoice.subtotal)}</p>
            ${invoice.tax_amount > 0 ? `<p><strong>TVA:</strong> ${formatCurrency(invoice.tax_amount)}</p>` : ''}
            ${invoice.discount_amount > 0 ? `<p><strong>Remise:</strong> -${formatCurrency(invoice.discount_amount)}</p>` : ''}
            <p class="total-row"><strong>TOTAL:</strong> ${formatCurrency(invoice.total_amount)}</p>
            <p><strong>Mode de paiement:</strong> ${invoice.payment_method}</p>
          </div>
          
          ${invoice.notes ? `
            <div style="margin-top: 20px; padding: 10px; background-color: #f9f9f9; border-left: 4px solid #007bff;">
              <strong>Notes:</strong> ${invoice.notes}
            </div>
          ` : ''}
          
          <div class="footer">
            <p>Merci de votre visite !</p>
            <p>BarStock Wise - Votre satisfaction est notre priorité</p>
          </div>
        </body>
      </html>
    `;
  };

  const generatePDFContent = () => {
    // Simulation d'un contenu PDF simple
    // En production, utilisez une bibliothèque comme jsPDF ou pdfmake
    return `%PDF-1.4
1 0 obj
<<
/Type /Catalog
/Pages 2 0 R
>>
endobj

2 0 obj
<<
/Type /Pages
/Kids [3 0 R]
/Count 1
>>
endobj

3 0 obj
<<
/Type /Page
/Parent 2 0 R
/MediaBox [0 0 612 792]
/Contents 4 0 R
>>
endobj

4 0 obj
<<
/Length 100
>>
stream
BT
/F1 12 Tf
72 720 Td
(Facture ${invoice.invoice_number}) Tj
ET
endstream
endobj

xref
0 5
0000000000 65535 f 
0000000009 00000 n 
0000000058 00000 n 
0000000115 00000 n 
0000000204 00000 n 
trailer
<<
/Size 5
/Root 1 0 R
>>
startxref
297
%%EOF`;
  };

  return (
    <>
      <Dialog open={isOpen} onOpenChange={setIsOpen}>
        <DialogTrigger asChild>
          <Button variant="outline" size="sm">
            <Eye className="w-4 h-4 mr-2" />
            Voir
          </Button>
        </DialogTrigger>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <FileText className="w-5 h-5" />
              Facture {invoice.invoice_number}
            </DialogTitle>
          </DialogHeader>
          
          <div className="space-y-4">
            {/* Actions */}
            <div className="flex gap-2 justify-end">
              <Button 
                variant="outline" 
                onClick={handlePrint}
                disabled={isPrinting}
              >
                <Printer className="w-4 h-4 mr-2" />
                {isPrinting ? 'Impression...' : 'Imprimer'}
              </Button>
              <Button 
                variant="outline" 
                onClick={handleDownload}
                disabled={isDownloading}
              >
                <Download className="w-4 h-4 mr-2" />
                {isDownloading ? 'Téléchargement...' : 'Télécharger'}
              </Button>
            </div>

            {/* Invoice Content */}
            <div ref={invoiceRef} className="bg-white border rounded-lg p-6">
              {/* Header */}
              <div className="text-center mb-6 pb-4 border-b-2 border-gray-300">
                <h1 className="text-2xl font-bold text-gray-800">BarStock Wise</h1>
                <p className="text-gray-600">Restaurant & Bar</p>
                <p className="text-gray-600">Bujumbura, Burundi</p>
                <div className="flex justify-center gap-4 text-sm text-gray-500 mt-2">
                  <span className="flex items-center gap-1">
                    <Phone className="w-3 h-3" />
                    +257 22 123 456
                  </span>
                  <span className="flex items-center gap-1">
                    <Mail className="w-3 h-3" />
                    <EMAIL>
                  </span>
                </div>
              </div>

              {/* Invoice Details */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                <div>
                  <h3 className="font-semibold text-gray-800 mb-2 flex items-center gap-2">
                    <User className="w-4 h-4" />
                    Client
                  </h3>
                  <div className="space-y-1 text-sm">
                    <p><span className="font-medium">Nom:</span> {invoice.customer_name || 'Client non spécifié'}</p>
                    {invoice.customer_email && (
                      <p><span className="font-medium">Email:</span> {invoice.customer_email}</p>
                    )}
                    {invoice.customer_phone && (
                      <p><span className="font-medium">Téléphone:</span> {invoice.customer_phone}</p>
                    )}
                    {invoice.customer_address && (
                      <p><span className="font-medium">Adresse:</span> {invoice.customer_address}</p>
                    )}
                    {invoice.table_number && (
                      <p><span className="font-medium">Table:</span> {invoice.table_number}</p>
                    )}
                  </div>
                </div>

                <div>
                  <h3 className="font-semibold text-gray-800 mb-2 flex items-center gap-2">
                    <Receipt className="w-4 h-4" />
                    Facture
                  </h3>
                  <div className="space-y-1 text-sm">
                    <p><span className="font-medium">N° Facture:</span> {invoice.invoice_number}</p>
                    <p><span className="font-medium">Date:</span> {new Date(invoice.created_at).toLocaleDateString('fr-FR')}</p>
                    <p><span className="font-medium">Heure:</span> {new Date(invoice.created_at).toLocaleTimeString('fr-FR')}</p>
                    <p><span className="font-medium">Serveur:</span> {invoice.server}</p>
                    <p><span className="font-medium">Statut:</span> 
                      <Badge variant={invoice.status === 'paid' ? 'default' : 'secondary'} className="ml-2">
                        {invoice.status === 'paid' ? 'Payé' : 'En attente'}
                      </Badge>
                    </p>
                  </div>
                </div>
              </div>

              {/* Items Table */}
              <div className="mb-6">
                <table className="w-full border-collapse border border-gray-300">
                  <thead>
                    <tr className="bg-gray-50">
                      <th className="border border-gray-300 px-4 py-2 text-left font-medium">Article</th>
                      <th className="border border-gray-300 px-4 py-2 text-center font-medium">Quantité</th>
                      <th className="border border-gray-300 px-4 py-2 text-right font-medium">Prix unitaire</th>
                      <th className="border border-gray-300 px-4 py-2 text-right font-medium">Total</th>
                    </tr>
                  </thead>
                  <tbody>
                    {(invoice.items || []).map((item, index) => (
                      <tr key={index} className="hover:bg-gray-50">
                        <td className="border border-gray-300 px-4 py-2">{item.product_name || item.product?.name || 'Article'}</td>
                        <td className="border border-gray-300 px-4 py-2 text-center">{item.quantity}</td>
                        <td className="border border-gray-300 px-4 py-2 text-right">{formatCurrency(item.unit_price || 0)}</td>
                        <td className="border border-gray-300 px-4 py-2 text-right font-medium">{formatCurrency(item.total_price || item.subtotal || (item.quantity * item.unit_price) || 0)}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>

              {/* Totals */}
              <div className="text-right space-y-2">
                <p><span className="font-medium">Sous-total:</span> {formatCurrency(invoice.subtotal)}</p>
                {invoice.tax_amount > 0 && (
                  <p><span className="font-medium">TVA:</span> {formatCurrency(invoice.tax_amount)}</p>
                )}
                {invoice.discount_amount > 0 && (
                  <p><span className="font-medium">Remise:</span> -{formatCurrency(invoice.discount_amount)}</p>
                )}
                <p className="text-lg font-bold border-t pt-2">
                  <span>TOTAL:</span> {formatCurrency(invoice.total_amount)}
                </p>
                <p className="text-sm text-gray-600">
                  <span className="font-medium">Mode de paiement:</span> {invoice.payment_method}
                </p>
              </div>

              {/* Notes */}
              {invoice.notes && (
                <div className="mt-6 p-4 bg-blue-50 border-l-4 border-blue-400">
                  <p className="font-medium text-blue-800">Notes:</p>
                  <p className="text-blue-700">{invoice.notes}</p>
                </div>
              )}

              {/* Footer */}
              <div className="text-center mt-8 pt-4 border-t border-gray-300">
                <p className="text-gray-600">Merci de votre visite !</p>
                <p className="text-sm text-gray-500">BarStock Wise - Votre satisfaction est notre priorité</p>
              </div>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default InvoiceViewer; 