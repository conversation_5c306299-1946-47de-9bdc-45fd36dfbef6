/**
 * Fonctions pures pour la synchronisation des stocks
 * Alternative aux méthodes statiques pour éviter les problèmes de contexte
 */

import apiClient from './api';

export interface SyncResult {
  success: boolean;
  message: string;
  syncedItems: number;
  errors: string[];
}

/**
 * Vérifie la disponibilité de l'API inventory
 */
export async function checkInventoryAPI(): Promise<boolean> {
  try {
    const response = await apiClient.options('/inventory/');
    const allowedMethods = response.headers['allow'] || '';
    return allowedMethods.includes('POST') && allowedMethods.includes('PUT');
  } catch (error) {
    console.warn('Impossible de vérifier l\'API inventory:', error);
    return false;
  }
}

/**
 * Synchronise les données en utilisant Products comme source de vérité
 */
export async function syncProductsToInventory(): Promise<SyncResult> {
  try {
    console.log('🔄 Début de la synchronisation Products → Inventory');

    // Vérifier si l'API inventory supporte l'écriture
    const inventoryWritable = await checkInventoryAPI();

    if (!inventoryWritable) {
      console.log('ℹ️ API inventory en lecture seule, utilisation de Products comme source de vérité');
      return {
        success: true,
        message: 'Synchronisation non nécessaire - Products utilisé comme source de vérité',
        syncedItems: 0,
        errors: []
      };
    }

    // Si l'API est disponible, procéder à la synchronisation
    const productsResponse = await apiClient.get('/products/');
    const products = Array.isArray(productsResponse.data)
      ? productsResponse.data
      : productsResponse.data.results || [];

    console.log(`📦 ${products.length} produits trouvés`);

    let syncedItems = 0;
    const errors: string[] = [];

    // Synchroniser chaque produit
    for (const product of products) {
      try {
        // Vérifier si l'item existe dans l'inventaire
        const inventoryResponse = await apiClient.get(`/inventory/?product_id=${product.id}`);
        const inventoryItems = Array.isArray(inventoryResponse.data)
          ? inventoryResponse.data
          : inventoryResponse.data.results || [];

        if (inventoryItems.length === 0) {
          // Créer un nouvel item d'inventaire
          await apiClient.post('/inventory/', {
            product_id: product.id,
            current_stock: product.current_stock || 0,
            minimum_stock: product.minimum_stock || 0,
          });
          syncedItems++;
        } else {
          // Mettre à jour l'item existant
          const inventoryItem = inventoryItems[0];
          await apiClient.put(`/inventory/${inventoryItem.id}/`, {
            ...inventoryItem,
            current_stock: product.current_stock || 0,
            minimum_stock: product.minimum_stock || 0,
          });
          syncedItems++;
        }
      } catch (error) {
        const errorMsg = `Erreur pour le produit ${product.name}: ${error}`;
        errors.push(errorMsg);
        console.error(errorMsg);
      }
    }

    return {
      success: errors.length === 0,
      message: `Synchronisation terminée: ${syncedItems} éléments synchronisés`,
      syncedItems,
      errors
    };

  } catch (error) {
    console.error('❌ Erreur lors de la synchronisation:', error);
    return {
      success: false,
      message: `Erreur de synchronisation: ${error instanceof Error ? error.message : 'Erreur inconnue'}`,
      syncedItems: 0,
      errors: [error instanceof Error ? error.message : 'Erreur inconnue']
    };
  }
}

/**
 * Synchronise les données en utilisant les mouvements de stock
 */
export async function syncInventoryToProducts(): Promise<SyncResult> {
  try {
    console.log('🔄 Début de la synchronisation via mouvements de stock');

    // 1. Récupérer les produits
    const productsResponse = await apiClient.get('/products/');
    const products = Array.isArray(productsResponse.data)
      ? productsResponse.data
      : productsResponse.data.results || [];

    // 2. Récupérer l'inventaire
    const inventoryResponse = await apiClient.get('/inventory/');
    const inventory = Array.isArray(inventoryResponse.data)
      ? inventoryResponse.data
      : inventoryResponse.data.results || [];

    console.log(`📦 ${products.length} produits, ${inventory.length} items d'inventaire`);

    let syncedItems = 0;
    const errors: string[] = [];

    // 3. Synchroniser via mouvements de stock
    for (const inventoryItem of inventory) {
      try {
        const product = products.find(p => p.id === inventoryItem.product_id);
        if (!product) continue;

        const stockDifference = inventoryItem.current_stock - (product.current_stock || 0);
        
        if (stockDifference !== 0) {
          // Créer un mouvement de stock pour ajuster
          await apiClient.post('/stock-movements/', {
            product_id: product.id,
            movement_type: stockDifference > 0 ? 'in' : 'out',
            quantity: Math.abs(stockDifference),
            reason: 'Synchronisation automatique',
            notes: `Ajustement: ${product.current_stock || 0} → ${inventoryItem.current_stock}`
          });
          syncedItems++;
        }
      } catch (error) {
        const errorMsg = `Erreur pour l'item ${inventoryItem.id}: ${error}`;
        errors.push(errorMsg);
        console.error(errorMsg);
      }
    }

    return {
      success: errors.length === 0,
      message: `Synchronisation via mouvements terminée: ${syncedItems} ajustements`,
      syncedItems,
      errors
    };

  } catch (error) {
    console.error('❌ Erreur lors de la synchronisation:', error);
    return {
      success: false,
      message: `Erreur de synchronisation: ${error instanceof Error ? error.message : 'Erreur inconnue'}`,
      syncedItems: 0,
      errors: [error instanceof Error ? error.message : 'Erreur inconnue']
    };
  }
}

/**
 * Synchronisation complète adaptée à l'architecture API
 */
export async function fullSync(): Promise<SyncResult> {
  try {
    console.log('🔄 Début de la synchronisation complète');

    // 1. Vérifier la disponibilité des APIs
    const inventoryWritable = await checkInventoryAPI();

    // 2. Choisir la stratégie de synchronisation
    if (inventoryWritable) {
      console.log('📝 API Inventory disponible en écriture - Synchronisation Products → Inventory');
      return await syncProductsToInventory();
    } else {
      console.log('📖 API Inventory en lecture seule - Synchronisation via mouvements');
      return await syncInventoryToProducts();
    }

  } catch (error) {
    console.error('❌ Erreur lors de la synchronisation complète:', error);
    return {
      success: false,
      message: `Erreur de synchronisation complète: ${error instanceof Error ? error.message : 'Erreur inconnue'}`,
      syncedItems: 0,
      errors: [error instanceof Error ? error.message : 'Erreur inconnue']
    };
  }
}

/**
 * Vérifier la cohérence entre Products et Inventory
 */
export async function checkConsistency(): Promise<{
  consistent: boolean;
  issues: string[];
  summary: {
    productsCount: number;
    inventoryCount: number;
    missingInInventory: number;
    missingInProducts: number;
  };
}> {
  try {
    // Récupérer les données
    const productsResponse = await apiClient.get('/products/');
    const products = Array.isArray(productsResponse.data)
      ? productsResponse.data
      : productsResponse.data.results || [];

    const inventoryResponse = await apiClient.get('/inventory/');
    const inventory = Array.isArray(inventoryResponse.data)
      ? inventoryResponse.data
      : inventoryResponse.data.results || [];

    console.log(`🔍 Vérification: ${products.length} produits, ${inventory.length} items d'inventaire`);

    const issues: string[] = [];

    // Vérifier si l'API inventory est en lecture seule
    const inventoryWritable = await checkInventoryAPI();
    if (!inventoryWritable) {
      issues.push('API Inventory en lecture seule - Products utilisé comme source de vérité');
    }

    // Si l'inventaire est vide mais qu'on a des produits, c'est normal en mode lecture seule
    if (inventory.length === 0 && products.length > 0 && !inventoryWritable) {
      return {
        consistent: true,
        issues: ['API Inventory vide (normal en mode lecture seule)'],
        summary: {
          productsCount: products.length,
          inventoryCount: inventory.length,
          missingInInventory: 0,
          missingInProducts: 0
        }
      };
    }

    // Vérifier les produits manquants dans l'inventaire
    const missingInInventory = products.filter(product => 
      !inventory.some(item => item.product_id === product.id)
    );

    // Vérifier les items d'inventaire sans produit correspondant
    const missingInProducts = inventory.filter(item => 
      !products.some(product => product.id === item.product_id)
    );

    if (missingInInventory.length > 0) {
      issues.push(`${missingInInventory.length} produits manquants dans l'inventaire`);
    }

    if (missingInProducts.length > 0) {
      issues.push(`${missingInProducts.length} items d'inventaire sans produit correspondant`);
    }

    // Vérifier les différences de stock
    let stockDifferences = 0;
    for (const product of products) {
      const inventoryItem = inventory.find(item => item.product_id === product.id);
      if (inventoryItem && inventoryItem.current_stock !== product.current_stock) {
        stockDifferences++;
      }
    }

    if (stockDifferences > 0) {
      issues.push(`${stockDifferences} différences de stock détectées`);
    }

    return {
      consistent: issues.length === 0,
      issues,
      summary: {
        productsCount: products.length,
        inventoryCount: inventory.length,
        missingInInventory: missingInInventory.length,
        missingInProducts: missingInProducts.length
      }
    };

  } catch (error) {
    console.error('❌ Erreur lors de la vérification de cohérence:', error);
    return {
      consistent: false,
      issues: [`Erreur de vérification: ${error instanceof Error ? error.message : 'Erreur inconnue'}`],
      summary: {
        productsCount: 0,
        inventoryCount: 0,
        missingInInventory: 0,
        missingInProducts: 0
      }
    };
  }
}
