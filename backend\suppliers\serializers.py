from rest_framework import serializers
from .models import Supplier
from django.core.validators import RegexValidator

class SupplierSerializer(serializers.ModelSerializer):
    total_purchases = serializers.DecimalField(max_digits=12, decimal_places=2, read_only=True)
    purchases_count = serializers.IntegerField(read_only=True)
    last_purchase_date = serializers.DateTimeField(read_only=True)
    
    class Meta:
        model = Supplier
        fields = [
            'id', 'name', 'contact_person', 'phone', 'email', 'address',
            'city', 'country', 'tax_number', 'payment_terms', 'notes',
            'is_active', 'total_purchases', 'purchases_count', 
            'last_purchase_date', 'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at']
    
    def validate_phone(self, value):
        if value:
            # Validation pour les numéros de téléphone burundais
            phone_validator = RegexValidator(
                regex=r'^\+?257?[0-9]{8}$',
                message="Format de téléphone invalide. Utilisez le format: +25712345678 ou 12345678"
            )
            phone_validator(value)
        return value
    
    def validate_email(self, value):
        if value and Supplier.objects.filter(email=value).exclude(pk=self.instance.pk if self.instance else None).exists():
            raise serializers.ValidationError("Un fournisseur avec cet email existe déjà.")
        return value
    
    def validate_name(self, value):
        if Supplier.objects.filter(name=value).exclude(pk=self.instance.pk if self.instance else None).exists():
            raise serializers.ValidationError("Un fournisseur avec ce nom existe déjà.")
        return value

class SupplierStatisticsSerializer(serializers.Serializer):
    supplier_id = serializers.IntegerField()
    supplier_name = serializers.CharField()
    total_purchases = serializers.DecimalField(max_digits=12, decimal_places=2)
    purchases_count = serializers.IntegerField()
    average_purchase_amount = serializers.DecimalField(max_digits=12, decimal_places=2)
    last_purchase_date = serializers.DateTimeField()
    products_supplied = serializers.IntegerField()
    payment_reliability = serializers.CharField()  # 'excellent', 'good', 'average', 'poor'
