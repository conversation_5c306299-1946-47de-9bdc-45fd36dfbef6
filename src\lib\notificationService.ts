import React from 'react';

/**
 * Service de notifications push avancé
 */

export interface NotificationConfig {
  title: string;
  body: string;
  icon?: string;
  badge?: string;
  tag?: string;
  data?: any;
  actions?: Array<{
    action: string;
    title: string;
    icon?: string;
  }>;
  requireInteraction?: boolean;
  silent?: boolean;
}

export interface NotificationPreferences {
  enabled: boolean;
  stockAlerts: boolean;
  salesNotifications: boolean;
  systemAlerts: boolean;
  soundEnabled: boolean;
  vibrationEnabled: boolean;
}

export class NotificationService {
  private static instance: NotificationService;
  private permission: NotificationPermission = 'default';
  private preferences: NotificationPreferences;
  private serviceWorker: ServiceWorkerRegistration | null = null;

  constructor() {
    this.preferences = this.loadPreferences();
    this.initializeService();
  }

  static getInstance(): NotificationService {
    if (!NotificationService.instance) {
      NotificationService.instance = new NotificationService();
    }
    return NotificationService.instance;
  }

  /**
   * Initialiser le service de notifications
   */
  private async initializeService() {
    // Vérifier le support des notifications
    if (!('Notification' in window)) {
      console.warn('Ce navigateur ne supporte pas les notifications');
      return;
    }

    // Vérifier le support des Service Workers
    if ('serviceWorker' in navigator) {
      try {
        this.serviceWorker = await navigator.serviceWorker.register('/sw.js');
        console.log('Service Worker enregistré pour les notifications');
      } catch (error) {
        console.warn('Erreur enregistrement Service Worker:', error);
      }
    }

    this.permission = Notification.permission;
  }

  /**
   * Demander la permission pour les notifications
   */
  async requestPermission(): Promise<boolean> {
    if (this.permission === 'granted') {
      return true;
    }

    if (this.permission === 'denied') {
      console.warn('Notifications refusées par l\'utilisateur');
      return false;
    }

    try {
      this.permission = await Notification.requestPermission();
      return this.permission === 'granted';
    } catch (error) {
      console.error('Erreur demande permission notifications:', error);
      return false;
    }
  }

  /**
   * Envoyer une notification
   */
  async sendNotification(config: NotificationConfig): Promise<boolean> {
    if (!this.preferences.enabled) {
      console.log('Notifications désactivées par l\'utilisateur');
      return false;
    }

    if (this.permission !== 'granted') {
      const granted = await this.requestPermission();
      if (!granted) return false;
    }

    try {
      // Notification via Service Worker si disponible
      if (this.serviceWorker) {
        await this.serviceWorker.showNotification(config.title, {
          body: config.body,
          icon: config.icon || '/icon-192x192.png',
          badge: config.badge || '/badge-72x72.png',
          tag: config.tag,
          data: config.data,
          actions: config.actions,
          requireInteraction: config.requireInteraction,
          silent: config.silent || !this.preferences.soundEnabled,
          vibrate: this.preferences.vibrationEnabled ? [200, 100, 200] : undefined,
        });
      } else {
        // Notification simple
        const notification = new Notification(config.title, {
          body: config.body,
          icon: config.icon || '/icon-192x192.png',
          tag: config.tag,
          data: config.data,
          requireInteraction: config.requireInteraction,
          silent: config.silent || !this.preferences.soundEnabled,
        });

        // Auto-fermeture après 5 secondes si pas d'interaction requise
        if (!config.requireInteraction) {
          setTimeout(() => notification.close(), 5000);
        }
      }

      return true;
    } catch (error) {
      console.error('Erreur envoi notification:', error);
      return false;
    }
  }

  /**
   * Notifications spécialisées pour BarStock Wise
   */
  async notifyLowStock(productName: string, currentStock: number, minStock: number) {
    if (!this.preferences.stockAlerts) return;

    await this.sendNotification({
      title: '⚠️ Stock Faible',
      body: `${productName}: ${currentStock} restant(s) (seuil: ${minStock})`,
      icon: '/icons/stock-alert.png',
      tag: `low-stock-${productName}`,
      requireInteraction: true,
      actions: [
        { action: 'view-stock', title: 'Voir Stock', icon: '/icons/view.png' },
        { action: 'order-now', title: 'Commander', icon: '/icons/order.png' }
      ],
      data: { type: 'low-stock', product: productName, currentStock, minStock }
    });
  }

  async notifyNewSale(saleAmount: number, tableNumber?: string) {
    if (!this.preferences.salesNotifications) return;

    await this.sendNotification({
      title: '💰 Nouvelle Vente',
      body: `Vente de ${saleAmount.toLocaleString()} BIF${tableNumber ? ` - Table ${tableNumber}` : ''}`,
      icon: '/icons/sale.png',
      tag: 'new-sale',
      data: { type: 'new-sale', amount: saleAmount, table: tableNumber }
    });
  }

  async notifySystemAlert(message: string, type: 'info' | 'warning' | 'error' = 'info') {
    if (!this.preferences.systemAlerts) return;

    const icons = {
      info: '/icons/info.png',
      warning: '/icons/warning.png',
      error: '/icons/error.png'
    };

    const titles = {
      info: 'ℹ️ Information Système',
      warning: '⚠️ Alerte Système',
      error: '❌ Erreur Système'
    };

    await this.sendNotification({
      title: titles[type],
      body: message,
      icon: icons[type],
      tag: `system-${type}`,
      requireInteraction: type === 'error',
      data: { type: 'system', level: type, message }
    });
  }

  async notifyBackupComplete(success: boolean, details?: string) {
    await this.sendNotification({
      title: success ? '✅ Sauvegarde Terminée' : '❌ Échec Sauvegarde',
      body: details || (success ? 'Sauvegarde des données réussie' : 'Erreur lors de la sauvegarde'),
      icon: success ? '/icons/backup-success.png' : '/icons/backup-error.png',
      tag: 'backup-status',
      data: { type: 'backup', success, details }
    });
  }

  /**
   * Gestion des préférences
   */
  updatePreferences(newPreferences: Partial<NotificationPreferences>) {
    this.preferences = { ...this.preferences, ...newPreferences };
    this.savePreferences();
  }

  getPreferences(): NotificationPreferences {
    return { ...this.preferences };
  }

  private loadPreferences(): NotificationPreferences {
    try {
      const stored = localStorage.getItem('notification_preferences');
      if (stored) {
        return { ...this.getDefaultPreferences(), ...JSON.parse(stored) };
      }
    } catch (error) {
      console.warn('Erreur chargement préférences notifications:', error);
    }
    return this.getDefaultPreferences();
  }

  private savePreferences() {
    try {
      localStorage.setItem('notification_preferences', JSON.stringify(this.preferences));
    } catch (error) {
      console.error('Erreur sauvegarde préférences notifications:', error);
    }
  }

  private getDefaultPreferences(): NotificationPreferences {
    return {
      enabled: true,
      stockAlerts: true,
      salesNotifications: true,
      systemAlerts: true,
      soundEnabled: true,
      vibrationEnabled: true,
    };
  }

  /**
   * Statistiques et monitoring
   */
  getStatus() {
    return {
      permission: this.permission,
      serviceWorkerAvailable: !!this.serviceWorker,
      preferences: this.preferences,
      browserSupport: 'Notification' in window,
    };
  }
}

// Instance singleton
export const notificationService = NotificationService.getInstance();

// Hook React pour les notifications
export const useNotifications = () => {
  const [status, setStatus] = React.useState(notificationService.getStatus());

  React.useEffect(() => {
    // Mettre à jour le statut périodiquement
    const interval = setInterval(() => {
      setStatus(notificationService.getStatus());
    }, 5000);

    return () => clearInterval(interval);
  }, []);

  return {
    ...notificationService,
    status,
    sendNotification: notificationService.sendNotification.bind(notificationService),
    notifyLowStock: notificationService.notifyLowStock.bind(notificationService),
    notifyNewSale: notificationService.notifyNewSale.bind(notificationService),
    notifySystemAlert: notificationService.notifySystemAlert.bind(notificationService),
    notifyBackupComplete: notificationService.notifyBackupComplete.bind(notificationService),
    updatePreferences: notificationService.updatePreferences.bind(notificationService),
    requestPermission: notificationService.requestPermission.bind(notificationService),
    getPreferences: notificationService.getPreferences.bind(notificationService),
    getStatus: notificationService.getStatus.bind(notificationService),
  };
};

export default notificationService;
