import { useEffect, useRef, useCallback } from 'react';
import { useToast } from '@/hooks/use-toast';

interface UseAutoSaveOptions {
  data: any;
  onSave: (data: any) => Promise<void> | void;
  delay?: number; // <PERSON><PERSON>lai en millisecondes avant sauvegarde
  enabled?: boolean;
  key?: string; // Clé pour le localStorage
}

export const useAutoSave = ({
  data,
  onSave,
  delay = 2000, // 2 secondes par défaut
  enabled = true,
  key = 'autosave'
}: UseAutoSaveOptions) => {
  const { toast } = useToast();
  const timeoutRef = useRef<NodeJS.Timeout>();
  const lastSavedRef = useRef<string>('');
  const isSavingRef = useRef(false);

  // Fonction de sauvegarde avec gestion d'erreurs
  const performSave = useCallback(async () => {
    if (isSavingRef.current) return;
    
    try {
      isSavingRef.current = true;
      await onSave(data);
      
      // Sauvegarder aussi en localStorage comme backup
      if (key) {
        localStorage.setItem(`${key}_backup`, JSON.stringify({
          data,
          timestamp: new Date().toISOString(),
          version: '1.0'
        }));
      }
      
      lastSavedRef.current = JSON.stringify(data);
      
      // Toast discret pour confirmer la sauvegarde
      toast({
        title: "Sauvegardé automatiquement",
        description: `Données sauvegardées à ${new Date().toLocaleTimeString()}`,
        duration: 2000,
      });
      
    } catch (error) {
      console.error('Erreur lors de la sauvegarde automatique:', error);
      toast({
        title: "Erreur de sauvegarde",
        description: "Impossible de sauvegarder automatiquement. Sauvegardez manuellement.",
        variant: "destructive",
        duration: 5000,
      });
    } finally {
      isSavingRef.current = false;
    }
  }, [data, onSave, key, toast]);

  // Effet pour déclencher la sauvegarde automatique
  useEffect(() => {
    if (!enabled) return;

    const currentDataString = JSON.stringify(data);
    
    // Ne sauvegarder que si les données ont changé
    if (currentDataString === lastSavedRef.current) return;
    
    // Annuler le timeout précédent
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    // Programmer une nouvelle sauvegarde
    timeoutRef.current = setTimeout(() => {
      performSave();
    }, delay);

    // Cleanup
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, [data, enabled, delay, performSave]);

  // Fonction pour forcer une sauvegarde immédiate
  const forceSave = useCallback(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    performSave();
  }, [performSave]);

  // Fonction pour récupérer les données depuis le localStorage
  const restoreFromBackup = useCallback(() => {
    if (!key) return null;
    
    try {
      const backup = localStorage.getItem(`${key}_backup`);
      if (backup) {
        const parsed = JSON.parse(backup);
        return {
          data: parsed.data,
          timestamp: new Date(parsed.timestamp),
          version: parsed.version
        };
      }
    } catch (error) {
      console.error('Erreur lors de la récupération du backup:', error);
    }
    
    return null;
  }, [key]);

  // Fonction pour vérifier s'il y a des modifications non sauvegardées
  const hasUnsavedChanges = useCallback(() => {
    return JSON.stringify(data) !== lastSavedRef.current;
  }, [data]);

  // Effet pour sauvegarder avant fermeture de page
  useEffect(() => {
    const handleBeforeUnload = (e: BeforeUnloadEvent) => {
      if (hasUnsavedChanges() && enabled) {
        e.preventDefault();
        e.returnValue = 'Vous avez des modifications non sauvegardées. Êtes-vous sûr de vouloir quitter ?';
        
        // Tentative de sauvegarde rapide
        try {
          onSave(data);
        } catch (error) {
          console.error('Erreur lors de la sauvegarde d\'urgence:', error);
        }
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);
    
    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, [data, enabled, hasUnsavedChanges, onSave]);

  return {
    forceSave,
    restoreFromBackup,
    hasUnsavedChanges,
    isSaving: isSavingRef.current
  };
};

// Hook spécialisé pour les rapports journaliers
export const useDailyReportAutoSave = (reportData: any, selectedDate: string) => {
  const saveToStorage = useCallback(async (data: any) => {
    // Simulation de sauvegarde (en production, ce serait un appel API)
    const reportKey = `daily_report_${selectedDate}`;
    localStorage.setItem(reportKey, JSON.stringify({
      date: selectedDate,
      data,
      lastModified: new Date().toISOString(),
      version: '1.0'
    }));
  }, [selectedDate]);

  const autoSave = useAutoSave({
    data: reportData,
    onSave: saveToStorage,
    delay: 3000, // 3 secondes pour les rapports
    enabled: true,
    key: `daily_report_${selectedDate}`
  });

  // Fonction pour charger un rapport existant
  const loadReport = useCallback((date: string) => {
    try {
      const reportKey = `daily_report_${date}`;
      const saved = localStorage.getItem(reportKey);
      if (saved) {
        const parsed = JSON.parse(saved);
        return parsed.data;
      }
    } catch (error) {
      console.error('Erreur lors du chargement du rapport:', error);
    }
    return null;
  }, []);

  // Fonction pour lister les rapports sauvegardés
  const getSavedReports = useCallback(() => {
    const reports: Array<{ date: string; lastModified: string }> = [];
    
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key?.startsWith('daily_report_')) {
        try {
          const data = JSON.parse(localStorage.getItem(key) || '{}');
          reports.push({
            date: data.date,
            lastModified: data.lastModified
          });
        } catch (error) {
          console.error('Erreur lors de la lecture du rapport:', key, error);
        }
      }
    }
    
    return reports.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());
  }, []);

  return {
    ...autoSave,
    loadReport,
    getSavedReports
  };
};
