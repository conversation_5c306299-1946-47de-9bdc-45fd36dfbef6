import { useEffect, useRef, useState } from 'react';
import { useAuth } from '@/contexts/AuthContextBackend';
import { useToast } from '@/hooks/use-toast';
import { TokenManager } from '@/lib/api';

interface WebSocketMessage {
  type: string;
  data: any;
  timestamp: string;
}

interface UseWebSocketOptions {
  url: string;
  onMessage?: (message: WebSocketMessage) => void;
  onConnect?: () => void;
  onDisconnect?: () => void;
  onError?: (error: Event) => void;
  reconnectInterval?: number;
  maxReconnectAttempts?: number;
}

export const useWebSocket = (options: UseWebSocketOptions) => {
  const { user, isAuthenticated } = useAuth();
  const { toast } = useToast();
  const wsRef = useRef<WebSocket | null>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const [isConnected, setIsConnected] = useState(false);
  const [reconnectAttempts, setReconnectAttempts] = useState(0);

  const {
    url,
    onMessage,
    onConnect,
    onDisconnect,
    onError,
    reconnectInterval = 5000,
    maxReconnectAttempts = 5
  } = options;

  const connect = () => {
    if (!isAuthenticated || !user) {
      console.log('WebSocket: Utilisateur non authentifié');
      return;
    }

    const token = TokenManager.getAccessToken();
    if (!token) {
      console.log('WebSocket: Token non disponible');
      return;
    }

    // Vérifier si les WebSockets sont activés
    if (import.meta.env.VITE_ENABLE_NOTIFICATIONS !== 'true') {
      console.log('WebSocket: WebSockets désactivés en configuration');
      return;
    }

    try {
      // Construire l'URL WebSocket avec le token
      const wsUrl = `${url}?token=${token}`;

      console.log('WebSocket: Tentative de connexion à', wsUrl);
      wsRef.current = new WebSocket(wsUrl);

      wsRef.current.onopen = () => {
        console.log('WebSocket: Connexion établie');
        setIsConnected(true);
        setReconnectAttempts(0);
        onConnect?.();
      };

      wsRef.current.onmessage = (event) => {
        try {
          const message: WebSocketMessage = JSON.parse(event.data);
          console.log('WebSocket: Message reçu', message);
          onMessage?.(message);
        } catch (error) {
          console.error('WebSocket: Erreur lors du parsing du message', error);
        }
      };

      wsRef.current.onclose = (event) => {
        console.log('WebSocket: Connexion fermée', event.code, event.reason);
        setIsConnected(false);
        onDisconnect?.();

        // Tentative de reconnexion si ce n'est pas une fermeture intentionnelle
        if (event.code !== 1000 && reconnectAttempts < maxReconnectAttempts) {
          console.log(`WebSocket: Tentative de reconnexion ${reconnectAttempts + 1}/${maxReconnectAttempts}`);
          reconnectTimeoutRef.current = setTimeout(() => {
            setReconnectAttempts(prev => prev + 1);
            connect();
          }, reconnectInterval);
        } else if (reconnectAttempts >= maxReconnectAttempts) {
          toast({
            title: "Connexion WebSocket échouée",
            description: "Impossible de se connecter au serveur. Veuillez rafraîchir la page.",
            variant: "destructive",
          });
        }
      };

      wsRef.current.onerror = (error) => {
        console.error('WebSocket: Erreur de connexion', error);
        onError?.(error);
      };

    } catch (error) {
      console.error('WebSocket: Erreur lors de la création de la connexion', error);
    }
  };

  const disconnect = () => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }

    if (wsRef.current) {
      wsRef.current.close(1000, 'Déconnexion intentionnelle');
      wsRef.current = null;
    }
    setIsConnected(false);
  };

  const sendMessage = (message: any) => {
    if (wsRef.current && isConnected) {
      try {
        wsRef.current.send(JSON.stringify(message));
        return true;
      } catch (error) {
        console.error('WebSocket: Erreur lors de l\'envoi du message', error);
        return false;
      }
    }
    console.warn('WebSocket: Tentative d\'envoi de message sans connexion active');
    return false;
  };

  // Effet pour gérer la connexion/déconnexion
  useEffect(() => {
    if (isAuthenticated && user) {
      connect();
    } else {
      disconnect();
    }

    return () => {
      disconnect();
    };
  }, [isAuthenticated, user]);

  // Nettoyage lors du démontage du composant
  useEffect(() => {
    return () => {
      disconnect();
    };
  }, []);

  return {
    isConnected,
    sendMessage,
    disconnect,
    reconnect: connect,
    reconnectAttempts
  };
};

// Hook spécialisé pour les notifications
export const useNotificationWebSocket = () => {
  const { user } = useAuth();
  const { toast } = useToast();
  
  const WS_URL = import.meta.env.VITE_WS_URL || 'ws://localhost:8000/ws';
  const notificationUrl = `${WS_URL}/notifications/${user?.id}/`;

  return useWebSocket({
    url: notificationUrl,
    onMessage: (message) => {
      if (message.type === 'notification') {
        const { title, description, variant = 'default' } = message.data;
        toast({
          title,
          description,
          variant: variant as any,
        });
      }
    },
    onConnect: () => {
      console.log('Notifications WebSocket connecté');
    },
    onDisconnect: () => {
      console.log('Notifications WebSocket déconnecté');
    },
  });
};

// Hook spécialisé pour les alertes de stock
export const useStockAlertsWebSocket = () => {
  const { toast } = useToast();
  
  const WS_URL = import.meta.env.VITE_WS_URL || 'ws://localhost:8000/ws';
  const alertsUrl = `${WS_URL}/alerts/`;

  return useWebSocket({
    url: alertsUrl,
    onMessage: (message) => {
      if (message.type === 'stock_alert') {
        const { product_name, current_stock, minimum_stock } = message.data;
        toast({
          title: "🚨 Alerte Stock",
          description: `${product_name}: Stock faible (${current_stock}/${minimum_stock})`,
          variant: "destructive",
        });
      }
    },
    onConnect: () => {
      console.log('Alertes stock WebSocket connecté');
    },
  });
};

// Hook spécialisé pour le dashboard temps réel
export const useDashboardWebSocket = () => {
  const [dashboardData, setDashboardData] = useState<any>(null);
  
  const WS_URL = import.meta.env.VITE_WS_URL || 'ws://localhost:8000/ws';
  const dashboardUrl = `${WS_URL}/dashboard/`;

  const { sendMessage } = useWebSocket({
    url: dashboardUrl,
    onMessage: (message) => {
      if (message.type === 'dashboard_update') {
        setDashboardData(message.data);
      }
    },
    onConnect: () => {
      console.log('Dashboard WebSocket connecté');
      // Demander les données initiales
      sendMessage({ type: 'get_dashboard_data' });
    },
  });

  return {
    dashboardData,
    requestUpdate: () => sendMessage({ type: 'get_dashboard_data' })
  };
};
