import React, { useState, useRef, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  ZoomIn, ZoomOut, RotateCcw, Move, 
  Users, Clock, Settings 
} from 'lucide-react';
import { Table } from '../api/tables';
import { cn } from '@/lib/utils';

interface FloorPlanProps {
  tables: Table[];
  onTableClick: (table: Table) => void;
  onTableDoubleClick: (table: Table) => void;
  className?: string;
}

interface TablePosition {
  id: number;
  x: number;
  y: number;
  rotation: number;
}

export function FloorPlan({ 
  tables, 
  onTableClick, 
  onTableDoubleClick,
  className 
}: FloorPlanProps) {
  const svgRef = useRef<SVGSVGElement>(null);
  const [zoom, setZoom] = useState(1);
  const [pan, setPan] = useState({ x: 0, y: 0 });
  const [isDragging, setIsDragging] = useState(false);
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 });
  const [selectedTable, setSelectedTable] = useState<number | null>(null);

  // Positions par défaut des tables (à adapter selon votre layout)
  const [tablePositions, setTablePositions] = useState<TablePosition[]>(() => {
    // Générer des positions par défaut basées sur l'emplacement
    return tables.map((table, index) => {
      const locations = {
        'Terrasse': { baseX: 100, baseY: 100 },
        'Intérieur': { baseX: 400, baseY: 100 },
        'Salle principale': { baseX: 100, baseY: 300 },
        'Salle VIP': { baseX: 400, baseY: 300 },
        'Bar': { baseX: 250, baseY: 500 },
      };
      
      const location = locations[table.location as keyof typeof locations] || { baseX: 100, baseY: 100 };
      const offset = (index % 4) * 80;
      
      return {
        id: table.id,
        x: location.baseX + offset,
        y: location.baseY + Math.floor(index / 4) * 80,
        rotation: 0,
      };
    });
  });

  const getTableColor = (status: Table['status']) => {
    switch (status) {
      case 'available':
        return '#10b981'; // green-500
      case 'occupied':
        return '#ef4444'; // red-500
      case 'reserved':
        return '#3b82f6'; // blue-500
      case 'cleaning':
        return '#f59e0b'; // yellow-500
      default:
        return '#6b7280'; // gray-500
    }
  };

  const getTableSize = (capacity: number) => {
    // Taille basée sur la capacité
    if (capacity <= 2) return { width: 40, height: 40 };
    if (capacity <= 4) return { width: 50, height: 50 };
    if (capacity <= 6) return { width: 60, height: 60 };
    return { width: 70, height: 70 };
  };

  const handleMouseDown = (e: React.MouseEvent) => {
    if (e.target === svgRef.current) {
      setIsDragging(true);
      setDragStart({ x: e.clientX - pan.x, y: e.clientY - pan.y });
    }
  };

  const handleMouseMove = (e: React.MouseEvent) => {
    if (isDragging) {
      setPan({
        x: e.clientX - dragStart.x,
        y: e.clientY - dragStart.y,
      });
    }
  };

  const handleMouseUp = () => {
    setIsDragging(false);
  };

  const handleZoomIn = () => {
    setZoom(prev => Math.min(prev * 1.2, 3));
  };

  const handleZoomOut = () => {
    setZoom(prev => Math.max(prev / 1.2, 0.5));
  };

  const handleReset = () => {
    setZoom(1);
    setPan({ x: 0, y: 0 });
  };

  const handleTableClick = (table: Table) => {
    setSelectedTable(table.id);
    onTableClick(table);
  };

  const handleTableDoubleClick = (table: Table) => {
    onTableDoubleClick(table);
  };

  return (
    <Card className={cn('h-full', className)}>
      <CardHeader>
        <div className="flex justify-between items-center">
          <CardTitle className="flex items-center gap-2">
            <Move className="w-5 h-5" />
            Plan de Salle
          </CardTitle>
          
          <div className="flex gap-2">
            <Button variant="outline" size="sm" onClick={handleZoomOut}>
              <ZoomOut className="w-4 h-4" />
            </Button>
            <Button variant="outline" size="sm" onClick={handleZoomIn}>
              <ZoomIn className="w-4 h-4" />
            </Button>
            <Button variant="outline" size="sm" onClick={handleReset}>
              <RotateCcw className="w-4 h-4" />
            </Button>
          </div>
        </div>
      </CardHeader>

      <CardContent className="p-0 h-96 overflow-hidden relative">
        <svg
          ref={svgRef}
          className="w-full h-full cursor-move"
          viewBox="0 0 800 600"
          onMouseDown={handleMouseDown}
          onMouseMove={handleMouseMove}
          onMouseUp={handleMouseUp}
          onMouseLeave={handleMouseUp}
        >
          {/* Grille de fond */}
          <defs>
            <pattern id="grid" width="50" height="50" patternUnits="userSpaceOnUse">
              <path d="M 50 0 L 0 0 0 50" fill="none" stroke="#f3f4f6" strokeWidth="1"/>
            </pattern>
          </defs>
          <rect width="100%" height="100%" fill="url(#grid)" />

          {/* Zones de la salle */}
          <g transform={`translate(${pan.x}, ${pan.y}) scale(${zoom})`}>
            {/* Zone Terrasse */}
            <rect
              x="50"
              y="50"
              width="200"
              height="150"
              fill="#ecfdf5"
              stroke="#10b981"
              strokeWidth="2"
              strokeDasharray="5,5"
              rx="10"
            />
            <text x="150" y="40" textAnchor="middle" className="text-sm font-medium fill-green-700">
              Terrasse
            </text>

            {/* Zone Intérieur */}
            <rect
              x="350"
              y="50"
              width="200"
              height="150"
              fill="#eff6ff"
              stroke="#3b82f6"
              strokeWidth="2"
              strokeDasharray="5,5"
              rx="10"
            />
            <text x="450" y="40" textAnchor="middle" className="text-sm font-medium fill-blue-700">
              Intérieur
            </text>

            {/* Zone Salle principale */}
            <rect
              x="50"
              y="250"
              width="300"
              height="200"
              fill="#fefce8"
              stroke="#f59e0b"
              strokeWidth="2"
              strokeDasharray="5,5"
              rx="10"
            />
            <text x="200" y="240" textAnchor="middle" className="text-sm font-medium fill-yellow-700">
              Salle principale
            </text>

            {/* Zone VIP */}
            <rect
              x="400"
              y="250"
              width="150"
              height="200"
              fill="#fdf2f8"
              stroke="#ec4899"
              strokeWidth="2"
              strokeDasharray="5,5"
              rx="10"
            />
            <text x="475" y="240" textAnchor="middle" className="text-sm font-medium fill-pink-700">
              VIP
            </text>

            {/* Bar */}
            <rect
              x="200"
              y="480"
              width="200"
              height="80"
              fill="#f3e8ff"
              stroke="#8b5cf6"
              strokeWidth="2"
              strokeDasharray="5,5"
              rx="10"
            />
            <text x="300" y="470" textAnchor="middle" className="text-sm font-medium fill-purple-700">
              Bar
            </text>

            {/* Tables */}
            {tables.map((table) => {
              const position = tablePositions.find(p => p.id === table.id);
              if (!position) return null;

              const size = getTableSize(table.capacity);
              const color = getTableColor(table.status);
              const isSelected = selectedTable === table.id;

              return (
                <g key={table.id}>
                  {/* Ombre de sélection */}
                  {isSelected && (
                    <circle
                      cx={position.x}
                      cy={position.y}
                      r={Math.max(size.width, size.height) / 2 + 5}
                      fill="none"
                      stroke="#3b82f6"
                      strokeWidth="3"
                      strokeDasharray="3,3"
                    />
                  )}

                  {/* Table */}
                  <circle
                    cx={position.x}
                    cy={position.y}
                    r={Math.max(size.width, size.height) / 2}
                    fill={color}
                    stroke="#ffffff"
                    strokeWidth="2"
                    className="cursor-pointer hover:opacity-80 transition-opacity"
                    onClick={() => handleTableClick(table)}
                    onDoubleClick={() => handleTableDoubleClick(table)}
                  />

                  {/* Numéro de table */}
                  <text
                    x={position.x}
                    y={position.y - 5}
                    textAnchor="middle"
                    className="text-sm font-bold fill-white pointer-events-none"
                  >
                    {table.number}
                  </text>

                  {/* Capacité */}
                  <text
                    x={position.x}
                    y={position.y + 8}
                    textAnchor="middle"
                    className="text-xs fill-white pointer-events-none"
                  >
                    {table.capacity}p
                  </text>

                  {/* Indicateur d'occupation */}
                  {table.is_occupied && table.occupation_duration > 0 && (
                    <circle
                      cx={position.x + size.width / 2 - 5}
                      cy={position.y - size.height / 2 + 5}
                      r="8"
                      fill="#ffffff"
                      stroke={color}
                      strokeWidth="2"
                    />
                  )}
                  {table.is_occupied && table.occupation_duration > 0 && (
                    <text
                      x={position.x + size.width / 2 - 5}
                      y={position.y - size.height / 2 + 9}
                      textAnchor="middle"
                      className="text-xs font-bold"
                      fill={color}
                    >
                      {Math.floor(table.occupation_duration / 60) || 1}h
                    </text>
                  )}
                </g>
              );
            })}
          </g>
        </svg>

        {/* Légende */}
        <div className="absolute bottom-4 left-4 bg-white p-3 rounded-lg shadow-lg border">
          <div className="text-sm font-medium mb-2">Légende</div>
          <div className="space-y-1 text-xs">
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 rounded-full bg-green-500"></div>
              <span>Disponible</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 rounded-full bg-red-500"></div>
              <span>Occupée</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 rounded-full bg-blue-500"></div>
              <span>Réservée</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 rounded-full bg-yellow-500"></div>
              <span>Nettoyage</span>
            </div>
          </div>
        </div>

        {/* Zoom indicator */}
        <div className="absolute top-4 right-4 bg-white px-2 py-1 rounded text-sm">
          {Math.round(zoom * 100)}%
        </div>
      </CardContent>
    </Card>
  );
}
