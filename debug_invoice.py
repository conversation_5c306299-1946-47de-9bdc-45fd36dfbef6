#!/usr/bin/env python3
"""
Script de débogage pour vérifier les données de la facture INV-9
"""

import os
import sys
import django

# Configuration Django
sys.path.append('backend')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'bar_stock_wise.settings')
django.setup()

from sales.models import Sale, SaleItem
from sales.serializers import SaleSerializer

def debug_invoice_9():
    """Débogue la facture INV-9"""
    print("=== DÉBOGAGE FACTURE INV-9 ===\n")
    
    try:
        # Chercher la vente avec référence INV-9
        sale = Sale.objects.get(reference='INV-9')
        print(f"✅ Vente trouvée: {sale.reference}")
        print(f"   ID: {sale.id}")
        print(f"   Client: {sale.customer_name}")
        print(f"   Table: {sale.table}")
        print(f"   Serveur: {sale.server}")
        print(f"   Statut: {sale.status}")
        print(f"   Total: {sale.total_amount} BIF")
        print(f"   Créée le: {sale.created_at}")
        print()
        
        # Vérifier les articles
        items = SaleItem.objects.filter(sale=sale)
        print(f"📦 Articles ({items.count()}):")
        
        if items.exists():
            for i, item in enumerate(items, 1):
                print(f"   {i}. {item.product.name}")
                print(f"      Quantité: {item.quantity}")
                print(f"      Prix unitaire: {item.unit_price} BIF")
                print(f"      Total: {item.total_price} BIF")
                print(f"      Profit: {item.profit} BIF")
                print()
        else:
            print("   ❌ Aucun article trouvé!")
            print()
        
        # Vérifier la sérialisation
        print("🔄 Sérialisation API:")
        serializer = SaleSerializer(sale)
        data = serializer.data
        
        print(f"   Items dans la sérialisation: {len(data.get('items', []))}")
        if data.get('items'):
            for i, item in enumerate(data['items'], 1):
                print(f"   {i}. {item.get('product_name', 'N/A')}")
                print(f"      Quantité: {item.get('quantity', 'N/A')}")
                print(f"      Prix unitaire: {item.get('unit_price', 'N/A')}")
                print(f"      Subtotal: {item.get('subtotal', 'N/A')}")
                print()
        else:
            print("   ❌ Aucun item dans la sérialisation!")
            
        print("📄 Données complètes de la sérialisation:")
        import json
        print(json.dumps(data, indent=2, default=str))
        
    except Sale.DoesNotExist:
        print("❌ Vente INV-9 non trouvée!")
        
        # Lister les ventes disponibles
        print("\n📋 Ventes disponibles:")
        sales = Sale.objects.all().order_by('-created_at')[:10]
        for sale in sales:
            items_count = sale.items.count()
            print(f"   {sale.reference} - {sale.customer_name} - {items_count} articles - {sale.total_amount} BIF")
    
    except Exception as e:
        print(f"❌ Erreur: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    debug_invoice_9()
