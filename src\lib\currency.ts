/**
 * Utilitaires pour la gestion de la devise BIF (Franc Burundais)
 */

export const CURRENCY = {
  code: 'BIF',
  symbol: 'BIF',
  name: 'Franc Burundais',
  locale: 'fr-BI' // Locale pour le Burundi
};

/**
 * Formate un montant en devise BIF
 * @param amount - Le montant à formater
 * @param options - Options de formatage
 * @returns Le montant formaté avec la devise
 */
export function formatCurrency(
  amount: number, 
  options: {
    showSymbol?: boolean;
    minimumFractionDigits?: number;
    maximumFractionDigits?: number;
  } = {}
): string {
  const {
    showSymbol = true,
    minimumFractionDigits = 0,
    maximumFractionDigits = 0
  } = options;

  const formattedAmount = amount.toLocaleString('fr-FR', {
    minimumFractionDigits,
    maximumFractionDigits
  });

  return showSymbol ? `${formattedAmount} ${CURRENCY.code}` : formattedAmount;
}

/**
 * Parse un montant depuis une chaîne
 * @param value - La valeur à parser
 * @returns Le montant numérique
 */
export function parseCurrency(value: string): number {
  // Supprimer tous les caractères non numériques sauf le point et la virgule
  const cleanValue = value.replace(/[^\d.,]/g, '');
  // Remplacer la virgule par un point pour la conversion
  const normalizedValue = cleanValue.replace(',', '.');
  return parseFloat(normalizedValue) || 0;
}

/**
 * Calcule le total d'un tableau de montants
 * @param amounts - Tableau des montants
 * @returns Le total
 */
export function calculateTotal(amounts: number[]): number {
  return amounts.reduce((sum, amount) => sum + amount, 0);
}

/**
 * Calcule la marge bénéficiaire
 * @param sellPrice - Prix de vente
 * @param buyPrice - Prix d'achat
 * @returns La marge en pourcentage
 */
export function calculateMargin(sellPrice: number, buyPrice: number): number {
  if (buyPrice === 0) return 0;
  return ((sellPrice - buyPrice) / buyPrice) * 100;
}

/**
 * Calcule le bénéfice
 * @param sellPrice - Prix de vente
 * @param buyPrice - Prix d'achat
 * @param quantity - Quantité vendue
 * @returns Le bénéfice total
 */
export function calculateProfit(sellPrice: number, buyPrice: number, quantity: number = 1): number {
  return (sellPrice - buyPrice) * quantity;
}

/**
 * Valide si un montant est valide
 * @param amount - Le montant à valider
 * @returns true si le montant est valide
 */
export function isValidAmount(amount: number): boolean {
  return !isNaN(amount) && amount >= 0 && isFinite(amount);
}

/**
 * Arrondit un montant à l'entier le plus proche (BIF n'a pas de centimes)
 * @param amount - Le montant à arrondir
 * @returns Le montant arrondi
 */
export function roundAmount(amount: number): number {
  return Math.round(amount);
}

/**
 * Convertit un montant en format d'affichage pour les tableaux
 * @param amount - Le montant
 * @returns Le montant formaté pour l'affichage
 */
export function displayAmount(amount: number): string {
  if (amount === 0) return '-';
  return formatCurrency(roundAmount(amount));
}

/**
 * Constantes pour les montants typiques au Burundi
 */
export const TYPICAL_AMOUNTS = {
  // Boissons
  BEER_LOCAL: 1500,      // Bière locale
  BEER_IMPORTED: 3000,   // Bière importée
  SODA: 1000,           // Soda
  WATER: 500,           // Eau
  JUICE: 2000,          // Jus
  
  // Plats
  MEAL_SIMPLE: 5000,    // Repas simple
  MEAL_COMPLETE: 8000,  // Repas complet
  SNACK: 2000,          // Collation
  
  // Seuils d'alerte
  LOW_STOCK_VALUE: 50000,    // Valeur de stock bas
  CRITICAL_STOCK_VALUE: 20000, // Valeur de stock critique
  
  // Limites de transaction
  MAX_TRANSACTION: 1000000,   // Transaction maximale
  MIN_TRANSACTION: 100        // Transaction minimale
};

/**
 * Obtient la couleur appropriée pour un montant selon son contexte
 * @param amount - Le montant
 * @param context - Le contexte ('profit', 'loss', 'neutral')
 * @returns La classe CSS de couleur
 */
export function getAmountColor(amount: number, context: 'profit' | 'loss' | 'neutral' = 'neutral'): string {
  if (context === 'profit') {
    return amount > 0 ? 'text-success' : amount < 0 ? 'text-destructive' : 'text-muted-foreground';
  }
  
  if (context === 'loss') {
    return amount > 0 ? 'text-destructive' : 'text-success';
  }
  
  return 'text-foreground';
}
