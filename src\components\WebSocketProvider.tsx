import React, { useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContextBackend';
import { 
  useNotificationWebSocket, 
  useStockAlertsWebSocket,
  useDashboardWebSocket 
} from '@/hooks/useWebSocket';

interface WebSocketProviderProps {
  children: React.ReactNode;
}

const WebSocketProvider: React.FC<WebSocketProviderProps> = ({ children }) => {
  const { isAuthenticated, user } = useAuth();

  // Vérifier si les WebSockets sont activés
  const wsEnabled = import.meta.env.VITE_ENABLE_NOTIFICATIONS === 'true';

  // Initialiser les WebSockets seulement si activés et utilisateur authentifié
  const notificationWS = wsEnabled && isAuthenticated ? useNotificationWebSocket() : { isConnected: false };
  const stockAlertsWS = wsEnabled && isAuthenticated ? useStockAlertsWebSocket() : { isConnected: false };
  const dashboardWS = wsEnabled && isAuthenticated ? useDashboardWebSocket() : { dashboardData: null };

  useEffect(() => {
    if (isAuthenticated && user) {
      console.log('WebSocket Provider: Initialisation des connexions WebSocket pour', user.username);
    }
  }, [isAuthenticated, user]);

  // Afficher les statuts de connexion en mode développement
  useEffect(() => {
    if (import.meta.env.VITE_DEV_MODE === 'true') {
      console.log('WebSocket Status:', {
        notifications: notificationWS.isConnected,
        stockAlerts: stockAlertsWS.isConnected,
        dashboard: dashboardWS.dashboardData ? 'connected' : 'disconnected'
      });
    }
  }, [
    notificationWS.isConnected, 
    stockAlertsWS.isConnected, 
    dashboardWS.dashboardData
  ]);

  return <>{children}</>;
};

export default WebSocketProvider;
