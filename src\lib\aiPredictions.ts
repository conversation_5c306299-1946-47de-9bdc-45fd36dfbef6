// Système de prédictions et recommandations IA pour BarStockWise
import { DailyStockItem } from '@/types/dailyReport';

export interface PredictionData {
  productName: string;
  currentStock: number;
  averageDailyConsumption: number;
  predictedStockOut: Date | null;
  daysUntilStockOut: number;
  recommendedOrderQuantity: number;
  confidence: number; // 0-100%
  seasonalFactor: number;
  trendFactor: number;
}

export interface SalesPredicition {
  productName: string;
  predictedSales: number;
  predictedRevenue: number;
  confidence: number;
  factors: string[];
}

export interface RecommendationAction {
  type: 'order' | 'promotion' | 'price_adjustment' | 'discontinue' | 'highlight';
  priority: 'low' | 'medium' | 'high' | 'critical';
  productName: string;
  title: string;
  description: string;
  expectedImpact: string;
  actionData: any;
}

export class AIPredictor {
  private historicalData: Map<string, number[]> = new Map();
  private seasonalPatterns: Map<string, number[]> = new Map();
  private priceElasticity: Map<string, number> = new Map();

  constructor() {
    this.initializeHistoricalData();
    this.initializeSeasonalPatterns();
    this.initializePriceElasticity();
  }

  private initializeHistoricalData() {
    // Simulation de données historiques (en production, ces données viendraient d'une base de données)
    this.historicalData.set('FANTA', [4, 6, 5, 7, 4, 8, 6, 5, 4, 7, 6, 5, 8, 4, 6]);
    this.historicalData.set('PRIMUS', [5, 7, 6, 8, 5, 9, 7, 6, 5, 8, 7, 6, 9, 5, 7]);
    this.historicalData.set('AMSTEL', [3, 4, 3, 5, 3, 6, 4, 3, 3, 5, 4, 3, 6, 3, 4]);
    this.historicalData.set('CHIVAS', [0, 1, 0, 2, 1, 1, 0, 1, 0, 2, 1, 0, 1, 0, 1]);
    this.historicalData.set('MUTZIG', [4, 5, 4, 6, 4, 7, 5, 4, 4, 6, 5, 4, 7, 4, 5]);
    this.historicalData.set('STELLA ARTOIS', [2, 3, 2, 4, 2, 5, 3, 2, 2, 4, 3, 2, 5, 2, 3]);
  }

  private initializeSeasonalPatterns() {
    // Facteurs saisonniers par mois (1 = normal, >1 = plus de demande, <1 = moins de demande)
    this.seasonalPatterns.set('FANTA', [0.8, 0.9, 1.1, 1.3, 1.5, 1.7, 1.8, 1.6, 1.2, 1.0, 0.9, 0.8]);
    this.seasonalPatterns.set('PRIMUS', [1.2, 1.1, 1.0, 1.1, 1.3, 1.4, 1.5, 1.4, 1.2, 1.1, 1.2, 1.3]);
    this.seasonalPatterns.set('AMSTEL', [1.0, 1.0, 1.1, 1.2, 1.3, 1.4, 1.5, 1.4, 1.2, 1.1, 1.0, 1.0]);
    this.seasonalPatterns.set('CHIVAS', [1.5, 1.2, 1.0, 1.0, 1.1, 1.2, 1.3, 1.2, 1.1, 1.2, 1.4, 1.8]);
  }

  private initializePriceElasticity() {
    // Élasticité prix (sensibilité de la demande au prix)
    this.priceElasticity.set('FANTA', -0.8); // Assez élastique
    this.priceElasticity.set('PRIMUS', -0.6); // Modérément élastique
    this.priceElasticity.set('AMSTEL', -0.4); // Peu élastique (premium)
    this.priceElasticity.set('CHIVAS', -0.2); // Très peu élastique (luxe)
  }

  // Calculer la moyenne mobile
  private calculateMovingAverage(data: number[], window: number = 7): number {
    if (data.length === 0) return 0;
    const relevantData = data.slice(-window);
    return relevantData.reduce((sum, val) => sum + val, 0) / relevantData.length;
  }

  // Calculer la tendance (pente de régression linéaire simple)
  private calculateTrend(data: number[]): number {
    if (data.length < 2) return 0;
    
    const n = data.length;
    const x = Array.from({ length: n }, (_, i) => i);
    const y = data;
    
    const sumX = x.reduce((a, b) => a + b, 0);
    const sumY = y.reduce((a, b) => a + b, 0);
    const sumXY = x.reduce((sum, xi, i) => sum + xi * y[i], 0);
    const sumXX = x.reduce((sum, xi) => sum + xi * xi, 0);
    
    const slope = (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX);
    return slope;
  }

  // Obtenir le facteur saisonnier actuel
  private getSeasonalFactor(productName: string): number {
    const currentMonth = new Date().getMonth();
    const seasonalPattern = this.seasonalPatterns.get(productName);
    return seasonalPattern ? seasonalPattern[currentMonth] : 1.0;
  }

  // Prédire la rupture de stock
  predictStockOut(item: DailyStockItem): PredictionData {
    const historicalConsumption = this.historicalData.get(item.produit) || [item.consommation];
    const averageConsumption = this.calculateMovingAverage(historicalConsumption);
    const trend = this.calculateTrend(historicalConsumption);
    const seasonalFactor = this.getSeasonalFactor(item.produit);
    
    // Ajuster la consommation prédite avec la tendance et la saisonnalité
    const adjustedConsumption = Math.max(0.1, averageConsumption * seasonalFactor * (1 + trend * 0.1));
    
    // Calculer les jours jusqu'à rupture
    const daysUntilStockOut = item.stockRestant > 0 ? Math.floor(item.stockRestant / adjustedConsumption) : 0;
    
    // Date prédite de rupture
    const predictedStockOut = daysUntilStockOut > 0 ? 
      new Date(Date.now() + daysUntilStockOut * 24 * 60 * 60 * 1000) : 
      new Date();

    // Quantité recommandée (pour 2 semaines + stock de sécurité)
    const recommendedOrderQuantity = Math.ceil(adjustedConsumption * 14 * 1.2);
    
    // Calcul de la confiance basé sur la variance des données historiques
    const variance = historicalConsumption.length > 1 ? 
      historicalConsumption.reduce((sum, val) => sum + Math.pow(val - averageConsumption, 2), 0) / historicalConsumption.length : 0;
    const confidence = Math.max(50, Math.min(95, 100 - (variance * 10)));

    return {
      productName: item.produit,
      currentStock: item.stockRestant,
      averageDailyConsumption: adjustedConsumption,
      predictedStockOut: daysUntilStockOut > 0 ? predictedStockOut : null,
      daysUntilStockOut,
      recommendedOrderQuantity,
      confidence,
      seasonalFactor,
      trendFactor: trend
    };
  }

  // Prédire les ventes futures
  predictSales(item: DailyStockItem, daysAhead: number = 7): SalesPredicition {
    const historicalConsumption = this.historicalData.get(item.produit) || [item.consommation];
    const averageConsumption = this.calculateMovingAverage(historicalConsumption);
    const trend = this.calculateTrend(historicalConsumption);
    const seasonalFactor = this.getSeasonalFactor(item.produit);
    
    // Facteurs influençant les prédictions
    const factors: string[] = [];
    
    let adjustmentFactor = 1.0;
    
    // Facteur saisonnier
    if (seasonalFactor > 1.1) {
      factors.push('Saison favorable (+' + ((seasonalFactor - 1) * 100).toFixed(0) + '%)');
      adjustmentFactor *= seasonalFactor;
    } else if (seasonalFactor < 0.9) {
      factors.push('Saison défavorable (' + ((seasonalFactor - 1) * 100).toFixed(0) + '%)');
      adjustmentFactor *= seasonalFactor;
    }
    
    // Facteur de tendance
    if (trend > 0.1) {
      factors.push('Tendance croissante');
      adjustmentFactor *= (1 + trend * 0.5);
    } else if (trend < -0.1) {
      factors.push('Tendance décroissante');
      adjustmentFactor *= (1 + trend * 0.5);
    }
    
    // Facteur de stock (si stock faible, peut limiter les ventes)
    if (item.stockRestant < averageConsumption * 3) {
      factors.push('Stock limité (peut réduire les ventes)');
      adjustmentFactor *= 0.8;
    }
    
    // Prédiction des ventes
    const predictedDailySales = Math.max(0, averageConsumption * adjustmentFactor);
    const predictedSales = Math.round(predictedDailySales * daysAhead);
    const predictedRevenue = predictedSales * item.prixVenteUnitaire;
    
    // Confiance basée sur la stabilité des données historiques
    const variance = historicalConsumption.length > 1 ? 
      historicalConsumption.reduce((sum, val) => sum + Math.pow(val - averageConsumption, 2), 0) / historicalConsumption.length : 0;
    const confidence = Math.max(60, Math.min(90, 100 - (variance * 15)));

    return {
      productName: item.produit,
      predictedSales,
      predictedRevenue,
      confidence,
      factors
    };
  }

  // Générer des recommandations intelligentes
  generateRecommendations(items: DailyStockItem[]): RecommendationAction[] {
    const recommendations: RecommendationAction[] = [];

    items.forEach(item => {
      const prediction = this.predictStockOut(item);
      const salesPrediction = this.predictSales(item);
      
      // Recommandation de commande urgente
      if (prediction.daysUntilStockOut <= 2 && prediction.daysUntilStockOut > 0) {
        recommendations.push({
          type: 'order',
          priority: 'critical',
          productName: item.produit,
          title: 'Commande Urgente Requise',
          description: `Stock épuisé dans ${prediction.daysUntilStockOut} jour(s). Commandez ${prediction.recommendedOrderQuantity} unités.`,
          expectedImpact: `Évite une rupture de stock et une perte de ${(salesPrediction.predictedRevenue * 0.7).toLocaleString()} BIF`,
          actionData: { quantity: prediction.recommendedOrderQuantity, urgency: 'critical' }
        });
      }
      
      // Recommandation de commande préventive
      else if (prediction.daysUntilStockOut <= 7 && prediction.daysUntilStockOut > 2) {
        recommendations.push({
          type: 'order',
          priority: 'high',
          productName: item.produit,
          title: 'Réapprovisionnement Recommandé',
          description: `Stock faible. Commandez ${prediction.recommendedOrderQuantity} unités dans les prochains jours.`,
          expectedImpact: `Maintient la disponibilité et évite les ruptures`,
          actionData: { quantity: prediction.recommendedOrderQuantity, urgency: 'normal' }
        });
      }

      // Recommandation de promotion pour écouler le stock
      if (item.stockRestant > prediction.averageDailyConsumption * 20) {
        recommendations.push({
          type: 'promotion',
          priority: 'medium',
          productName: item.produit,
          title: 'Promotion Suggérée',
          description: `Stock élevé (${item.stockRestant} unités). Une promotion pourrait accélérer l\'écoulement.`,
          expectedImpact: `Réduction du stock excédentaire et augmentation du chiffre d'affaires`,
          actionData: { currentStock: item.stockRestant, suggestedDiscount: 10 }
        });
      }

      // Recommandation d'ajustement de prix
      const elasticity = this.priceElasticity.get(item.produit) || -0.5;
      if (salesPrediction.predictedSales < prediction.averageDailyConsumption * 0.7 && elasticity < -0.6) {
        recommendations.push({
          type: 'price_adjustment',
          priority: 'medium',
          productName: item.produit,
          title: 'Réduction de Prix Suggérée',
          description: `Ventes en baisse. Une réduction de 5-10% pourrait stimuler la demande.`,
          expectedImpact: `Augmentation estimée des ventes de ${Math.abs(elasticity * 10).toFixed(0)}%`,
          actionData: { currentPrice: item.prixVenteUnitaire, suggestedReduction: 0.05 }
        });
      }

      // Recommandation de mise en avant
      if (item.marge > 3000 && salesPrediction.predictedSales > prediction.averageDailyConsumption * 1.2) {
        recommendations.push({
          type: 'highlight',
          priority: 'medium',
          productName: item.produit,
          title: 'Produit à Mettre en Avant',
          description: `Excellente marge (${item.marge.toLocaleString()} BIF) et demande croissante. À promouvoir activement.`,
          expectedImpact: `Maximisation du bénéfice avec un produit rentable`,
          actionData: { margin: item.marge, trend: 'positive' }
        });
      }
    });

    // Trier par priorité
    const priorityOrder = { 'critical': 4, 'high': 3, 'medium': 2, 'low': 1 };
    return recommendations.sort((a, b) => priorityOrder[b.priority] - priorityOrder[a.priority]);
  }

  // Analyser les tendances du marché
  analyzeMarketTrends(items: DailyStockItem[]): {
    growingProducts: string[];
    decliningProducts: string[];
    stableProducts: string[];
    insights: string[];
  } {
    const growingProducts: string[] = [];
    const decliningProducts: string[] = [];
    const stableProducts: string[] = [];
    const insights: string[] = [];

    items.forEach(item => {
      const historicalData = this.historicalData.get(item.produit) || [];
      const trend = this.calculateTrend(historicalData);
      
      if (trend > 0.2) {
        growingProducts.push(item.produit);
      } else if (trend < -0.2) {
        decliningProducts.push(item.produit);
      } else {
        stableProducts.push(item.produit);
      }
    });

    // Générer des insights
    if (growingProducts.length > 0) {
      insights.push(`Produits en croissance: ${growingProducts.join(', ')}. Augmentez les stocks.`);
    }
    
    if (decliningProducts.length > 0) {
      insights.push(`Produits en déclin: ${decliningProducts.join(', ')}. Analysez les causes et ajustez la stratégie.`);
    }
    
    const totalRevenue = items.reduce((sum, item) => sum + (item.stockVendu * item.prixVenteUnitaire), 0);
    const totalProfit = items.reduce((sum, item) => sum + item.benefice, 0);
    const profitMargin = (totalProfit / totalRevenue) * 100;
    
    if (profitMargin > 45) {
      insights.push(`Excellente marge bénéficiaire (${profitMargin.toFixed(1)}%). Maintenez cette performance.`);
    } else if (profitMargin < 35) {
      insights.push(`Marge bénéficiaire faible (${profitMargin.toFixed(1)}%). Optimisez les prix ou réduisez les coûts.`);
    }

    return {
      growingProducts,
      decliningProducts,
      stableProducts,
      insights
    };
  }
}

// Instance globale
export const aiPredictor = new AIPredictor();
