import { useState, useMemo } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { 
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from '@/components/ui/dropdown-menu';
import { Search, History, Calendar, Trash2, Eye, Download, Filter, AlertTriangle, FileSpreadsheet, FileText, ChevronDown, FileDown } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/contexts/AuthContextBackend';
import { Skeleton } from '@/components/ui/skeleton';
import { formatCurrency } from '@/lib/currency';
import { usePermissions } from '@/contexts/PermissionsContext';
import { useSales, useDailySummary, useDeleteSale } from '@/hooks/useApi';
import { parseNumericValue } from '@/lib/utils/numeric';
import jsPDF from 'jspdf';
import 'jspdf-autotable';

// Interface pour les ventes (basée sur l'API Django)
interface SaleItem {
  id: string;
  product: {
    id: string;
    name: string;
  };
  product_name?: string; // Pour compatibilité
  quantity: number;
  unit_price: number | string;
  total_price: number | string;
  notes?: string;
}

interface Sale {
  id: string;
  reference?: string;
  created_at: string;
  updated_at?: string;
  table?: {
    id: string;
    number: string;
  };
  table_number?: string;
  customer_name?: string;
  server: {
    id: string;
    username: string;
    first_name?: string;
    last_name?: string;
    get_full_name?: string;
  };
  server_name?: string;
  status: 'pending' | 'preparing' | 'ready' | 'served' | 'paid' | 'cancelled';
  status_display?: string;
  payment_method?: 'cash' | 'card' | 'mobile' | 'credit';
  payment_method_display?: string;
  subtotal: number | string;
  tax_amount: number | string;
  discount_amount: number | string;
  total_amount: number | string;
  final_amount: number | string;
  items: SaleItem[];
  items_count?: number;
  profit?: number;
  notes?: string;
}
const SalesHistory = () => {
  const { user } = useAuth();
  const { canDelete } = usePermissions();

  // Hooks pour les données API
  const { data: salesData, isLoading: salesLoading, error: salesError } = useSales();
  const { data: dailySummary } = useDailySummary();
  const deleteSaleMutation = useDeleteSale();

  // État local pour les filtres
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedServer, setSelectedServer] = useState('Tous');
  const [selectedPayment, setSelectedPayment] = useState('Tous');
  const [selectedStatus, setSelectedStatus] = useState('all');
  const [selectedDate, setSelectedDate] = useState('');
  const { toast } = useToast();

  // Extraire les données de ventes de la réponse API
  const salesList = useMemo(() => {
    if (!salesData) return [];

    // Gérer différents formats de réponse de l'API
    if (Array.isArray(salesData)) {
      return salesData;
    } else if (salesData.results && Array.isArray(salesData.results)) {
      return salesData.results;
    } else if (salesData.sales && Array.isArray(salesData.sales)) {
      return salesData.sales;
    }

    return [];
  }, [salesData]);

  // Listes pour les filtres
  const servers = useMemo(() => {
    const uniqueServers = ['Tous'];

    if (salesList.length > 0) {
      const serverSet = new Set(
        salesList.map((sale: Sale) =>
          sale.server_name ||
          sale.server?.get_full_name ||
          `${sale.server?.first_name || ''} ${sale.server?.last_name || ''}`.trim() ||
          sale.server?.username ||
          'Serveur inconnu'
        )
      );
      uniqueServers.push(...Array.from(serverSet));
    }

    return uniqueServers;
  }, [salesList]);

  const paymentMethods = useMemo(() => {
    const methods = ['Tous'];

    if (salesList.length > 0) {
      const methodSet = new Set(
        salesList.map((sale: Sale) =>
          sale.payment_method_display ||
          (sale.payment_method === 'cash' ? 'Espèces' :
           sale.payment_method === 'card' ? 'Carte' :
           sale.payment_method === 'mobile' ? 'Mobile Money' :
           sale.payment_method === 'credit' ? 'Crédit' :
           sale.payment_method || 'Non spécifié')
        ).filter(Boolean)
      );
      methods.push(...Array.from(methodSet));
    }

    return methods;
  }, [salesList]);

  // Filtrage des ventes
  const filteredSales = useMemo(() => {
    if (!salesList || salesList.length === 0) return [];

    return salesList.filter((sale: Sale) => {
      // Recherche dans table, client et produits
      const tableNumber = sale.table_number || sale.table?.number || '';
      const customerName = sale.customer_name || '';
      const serverName = sale.server_name ||
                        sale.server?.get_full_name ||
                        `${sale.server?.first_name || ''} ${sale.server?.last_name || ''}`.trim() ||
                        sale.server?.username || '';

      const matchesSearch = searchTerm === '' ||
        tableNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
        customerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        serverName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (sale.reference && sale.reference.toLowerCase().includes(searchTerm.toLowerCase())) ||
        sale.items?.some(item => {
          const productName = item.product_name || item.product?.name || '';
          return productName.toLowerCase().includes(searchTerm.toLowerCase());
        });

      // Filtrage par serveur
      const matchesServer = selectedServer === 'Tous' || serverName === selectedServer;

      // Filtrage par méthode de paiement
      const paymentDisplay = sale.payment_method_display ||
                           (sale.payment_method === 'cash' ? 'Espèces' :
                            sale.payment_method === 'card' ? 'Carte' :
                            sale.payment_method === 'mobile' ? 'Mobile Money' :
                            sale.payment_method === 'credit' ? 'Crédit' :
                            sale.payment_method || 'Non spécifié');
      const matchesPayment = selectedPayment === 'Tous' || paymentDisplay === selectedPayment;

      // Filtrage par date
      const matchesDate = !selectedDate || sale.created_at.startsWith(selectedDate);

      return matchesSearch && matchesServer && matchesPayment && matchesDate;
    });
  }, [salesList, searchTerm, selectedServer, selectedPayment, selectedDate]);

  const deleteSale = async (saleId: string) => {
    if (!canDelete) {
      toast({
        title: "Accès refusé",
        description: "Vous n'avez pas les permissions pour supprimer des ventes.",
        variant: "destructive",
      });
      return;
    }

    // Confirmation avant suppression
    if (!window.confirm("Êtes-vous sûr de vouloir supprimer cette vente ? Cette action est irréversible.")) {
      return;
    }

    try {
      await deleteSaleMutation.mutateAsync(saleId);
      toast({
        title: "Vente supprimée",
        description: "La vente a été supprimée avec succès.",
      });
    } catch (error) {
      console.error('Erreur lors de la suppression:', error);
      toast({
        title: "Erreur",
        description: "Impossible de supprimer la vente. Veuillez réessayer.",
        variant: "destructive",
      });
    }
  };

  // Composants de loading
  const LoadingTable = () => (
    <div className="space-y-3">
      {[...Array(5)].map((_, i) => (
        <div key={i} className="flex items-center space-x-4">
          <Skeleton className="h-4 w-[120px]" />
          <Skeleton className="h-4 w-[100px]" />
          <Skeleton className="h-4 w-[200px]" />
          <Skeleton className="h-4 w-[80px]" />
          <Skeleton className="h-4 w-[100px]" />
          <Skeleton className="h-4 w-[60px]" />
        </div>
      ))}
    </div>
  );

  // Gestion des erreurs
  if (salesError) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <AlertTriangle className="h-12 w-12 text-destructive mx-auto mb-4" />
          <h3 className="text-lg font-semibold mb-2">Erreur de chargement</h3>
          <p className="text-muted-foreground">
            Impossible de charger l'historique des ventes. Vérifiez votre connexion.
          </p>
        </div>
      </div>
    );
  }

  // Fonction utilitaire pour échapper les caractères CSV
  const escapeCsvValue = (value: any): string => {
    if (value === null || value === undefined) return '';
    const stringValue = String(value);
    // Échapper les guillemets doubles et encapsuler si nécessaire
    if (stringValue.includes('"') || stringValue.includes(',') || stringValue.includes('\n') || stringValue.includes('\r')) {
      return `"${stringValue.replace(/"/g, '""')}"`;
    }
    return stringValue;
  };

  // Fonction pour formater les montants
  const formatAmount = (amount: any): string => {
    const numericValue = parseNumericValue(amount);
    return numericValue.toLocaleString('fr-FR', { minimumFractionDigits: 0, maximumFractionDigits: 0 });
  };

  const exportToExcel = () => {
    if (filteredSales.length === 0) {
      toast({
        title: "Aucune donnée",
        description: "Aucune vente à exporter avec les filtres actuels.",
        variant: "destructive",
      });
      return;
    }

    try {
      // En-têtes du CSV avec descriptions détaillées
      const headers = [
        'Date & Heure',
        'Référence Vente',
        'Table/Client',
        'Serveur',
        'Statut Commande',
        'Méthode Paiement',
        'Sous-total (BIF)',
        'Remise (BIF)',
        'Total Final (BIF)',
        'Profit Estimé (BIF)',
        'Nombre Articles',
        'Détail Articles',
        'Notes Commande',
        'Durée Service (min)',
        'Heure Création',
        'Heure Mise à Jour'
      ];

      // Préparer les données avec formatage amélioré
      const csvData = filteredSales.map(sale => {
        // Informations serveur
        const serverName = sale.server_name ||
                         sale.server?.get_full_name ||
                         `${sale.server?.first_name || ''} ${sale.server?.last_name || ''}`.trim() ||
                         sale.server?.username || 'Serveur inconnu';

        // Informations table/client
        const tableInfo = sale.table_number ||
                        sale.table?.number ||
                        sale.customer_name ||
                        'Emporter';

        // Méthode de paiement traduite
        const paymentDisplay = sale.payment_method_display ||
                             (sale.payment_method === 'cash' ? 'Espèces' :
                              sale.payment_method === 'card' ? 'Carte Bancaire' :
                              sale.payment_method === 'mobile' ? 'Mobile Money' :
                              sale.payment_method === 'credit' ? 'Crédit' :
                              sale.payment_method === 'bank_transfer' ? 'Virement' :
                              sale.payment_method || 'Non spécifié');

        // Statut traduit
        const statusDisplay = sale.status_display ||
                            (sale.status === 'pending' ? 'En attente' :
                             sale.status === 'paid' ? 'Payée' :
                             sale.status === 'cancelled' ? 'Annulée' :
                             sale.status === 'refunded' ? 'Remboursée' :
                             sale.status || 'Inconnu');

        // Articles détaillés
        const articlesCount = sale.items?.length || 0;
        const articlesDetail = sale.items?.map(item => {
          const productName = item.product_name || item.product?.name || 'Produit inconnu';
          const unitPrice = formatAmount(item.unit_price);
          const totalPrice = formatAmount(item.total_price);
          return `${productName} (Qté: ${item.quantity}, Prix unit: ${unitPrice} BIF, Total: ${totalPrice} BIF)`;
        }).join(' | ') || 'Aucun article';

        // Calcul de la durée de service
        const createdAt = new Date(sale.created_at);
        const updatedAt = sale.updated_at ? new Date(sale.updated_at) : createdAt;
        const serviceDuration = Math.round((updatedAt.getTime() - createdAt.getTime()) / (1000 * 60));

        // Notes de commande
        const orderNotes = sale.notes || sale.customer_notes || 'Aucune note';

        return [
          createdAt.toLocaleString('fr-FR'),
          sale.reference || `#${sale.id}`,
          tableInfo,
          serverName,
          statusDisplay,
          paymentDisplay,
          formatAmount(sale.subtotal || sale.total_amount),
          formatAmount(sale.discount_amount || 0),
          formatAmount(sale.final_amount || sale.total_amount),
          formatAmount(sale.profit || 0),
          articlesCount,
          articlesDetail,
          orderNotes,
          serviceDuration > 0 ? serviceDuration : 'N/A',
          createdAt.toLocaleTimeString('fr-FR'),
          updatedAt.toLocaleTimeString('fr-FR')
        ];
      });

      // Ajouter une ligne de résumé
      const totalRevenue = filteredSales.reduce((sum, sale) =>
        sum + (parseNumericValue(sale.final_amount) || parseNumericValue(sale.total_amount)), 0);
      const totalProfit = filteredSales.reduce((sum, sale) =>
        sum + parseNumericValue(sale.profit), 0);
      const totalDiscount = filteredSales.reduce((sum, sale) =>
        sum + parseNumericValue(sale.discount_amount), 0);

      const summaryRow = [
        '=== RÉSUMÉ ===',
        `${filteredSales.length} ventes`,
        '',
        '',
        '',
        '',
        '',
        formatAmount(totalDiscount),
        formatAmount(totalRevenue),
        formatAmount(totalProfit),
        '',
        '',
        `Export généré le ${new Date().toLocaleString('fr-FR')}`,
        '',
        '',
        ''
      ];

      // Créer le contenu CSV avec BOM UTF-8 pour Excel
      const csvRows = [headers, ...csvData, [], summaryRow];
      const csvContent = csvRows
        .map(row => row.map(cell => escapeCsvValue(cell)).join(','))
        .join('\n');

      // Ajouter BOM UTF-8 pour une meilleure compatibilité Excel
      const BOM = '\uFEFF';
      const csvWithBOM = BOM + csvContent;

      // Créer et télécharger le fichier
      const blob = new Blob([csvWithBOM], { type: 'text/csv;charset=utf-8;' });
      const link = document.createElement('a');
      const url = URL.createObjectURL(blob);

      // Nom de fichier avec informations contextuelles
      const dateRange = searchTerm ? `_filtre-${searchTerm}` : '';
      const statusSuffix = selectedStatus !== 'all' ? `_${selectedStatus}` : '';
      const serverSuffix = selectedServer !== 'Tous' ? `_${selectedServer.replace(/\s+/g, '-')}` : '';
      const fileName = `historique-ventes_${new Date().toISOString().split('T')[0]}${dateRange}${statusSuffix}${serverSuffix}_${filteredSales.length}-ventes.csv`;

      link.setAttribute('href', url);
      link.setAttribute('download', fileName);
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      toast({
        title: "Export CSV réussi",
        description: `${filteredSales.length} vente(s) exportée(s) avec détails complets. Fichier: ${fileName}`,
      });
    } catch (error) {
      console.error('Erreur lors de l\'export CSV:', error);
      toast({
        title: "Erreur d'export",
        description: `Impossible d'exporter les données: ${error instanceof Error ? error.message : 'Erreur inconnue'}`,
        variant: "destructive",
      });
    }
  };

  // Export CSV simplifié (résumé)
  const exportSummaryCSV = () => {
    if (filteredSales.length === 0) {
      toast({
        title: "Aucune donnée",
        description: "Aucune vente à exporter.",
        variant: "destructive",
      });
      return;
    }

    try {
      const headers = ['Date', 'Référence', 'Table', 'Serveur', 'Total (BIF)', 'Statut'];

      const csvData = filteredSales.map(sale => [
        new Date(sale.created_at).toLocaleDateString('fr-FR'),
        sale.reference || `#${sale.id}`,
        sale.table_number || sale.table?.number || sale.customer_name || 'Emporter',
        sale.server_name || sale.server?.username || 'N/A',
        formatAmount(sale.final_amount || sale.total_amount),
        sale.status_display || sale.status
      ]);

      const csvContent = [headers, ...csvData]
        .map(row => row.map(cell => escapeCsvValue(cell)).join(','))
        .join('\n');

      const BOM = '\uFEFF';
      const blob = new Blob([BOM + csvContent], { type: 'text/csv;charset=utf-8;' });
      const link = document.createElement('a');
      const url = URL.createObjectURL(blob);

      link.setAttribute('href', url);
      link.setAttribute('download', `resume-ventes_${new Date().toISOString().split('T')[0]}.csv`);
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      toast({
        title: "Export résumé réussi",
        description: `${filteredSales.length} vente(s) exportée(s) en format simplifié.`,
      });
    } catch (error) {
      console.error('Erreur export résumé:', error);
      toast({
        title: "Erreur d'export",
        description: "Impossible d'exporter le résumé.",
        variant: "destructive",
      });
    }
  };

  // Export CSV des articles détaillés
  const exportItemsCSV = () => {
    if (filteredSales.length === 0) {
      toast({
        title: "Aucune donnée",
        description: "Aucune vente à exporter.",
        variant: "destructive",
      });
      return;
    }

    try {
      const headers = [
        'Date Vente', 'Référence Vente', 'Table', 'Serveur',
        'Produit', 'Quantité', 'Prix Unitaire (BIF)', 'Total Article (BIF)',
        'Catégorie', 'Notes Article'
      ];

      const csvData: any[] = [];

      filteredSales.forEach(sale => {
        const saleInfo = {
          date: new Date(sale.created_at).toLocaleDateString('fr-FR'),
          reference: sale.reference || `#${sale.id}`,
          table: sale.table_number || sale.table?.number || sale.customer_name || 'Emporter',
          server: sale.server_name || sale.server?.username || 'N/A'
        };

        if (sale.items && sale.items.length > 0) {
          sale.items.forEach(item => {
            csvData.push([
              saleInfo.date,
              saleInfo.reference,
              saleInfo.table,
              saleInfo.server,
              item.product_name || item.product?.name || 'Produit inconnu',
              item.quantity,
              formatAmount(item.unit_price),
              formatAmount(item.total_price),
              item.product?.category || 'N/A',
              item.notes || ''
            ]);
          });
        } else {
          // Vente sans articles détaillés
          csvData.push([
            saleInfo.date,
            saleInfo.reference,
            saleInfo.table,
            saleInfo.server,
            'Aucun détail d\'article',
            '',
            '',
            formatAmount(sale.total_amount),
            '',
            'Vente sans détail d\'articles'
          ]);
        }
      });

      const csvContent = [headers, ...csvData]
        .map(row => row.map(cell => escapeCsvValue(cell)).join(','))
        .join('\n');

      const BOM = '\uFEFF';
      const blob = new Blob([BOM + csvContent], { type: 'text/csv;charset=utf-8;' });
      const link = document.createElement('a');
      const url = URL.createObjectURL(blob);

      link.setAttribute('href', url);
      link.setAttribute('download', `articles-vendus_${new Date().toISOString().split('T')[0]}.csv`);
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      const totalItems = csvData.length;
      toast({
        title: "Export articles réussi",
        description: `${totalItems} ligne(s) d'articles exportée(s) depuis ${filteredSales.length} vente(s).`,
      });
    } catch (error) {
      console.error('Erreur export articles:', error);
      toast({
        title: "Erreur d'export",
        description: "Impossible d'exporter les articles.",
        variant: "destructive",
      });
    }
  };

  // Export PDF professionnel
  const exportToPDF = () => {
    if (filteredSales.length === 0) {
      toast({
        title: "Aucune donnée",
        description: "Aucune vente à exporter.",
        variant: "destructive",
      });
      return;
    }

    try {
      // Créer un nouveau document PDF
      const doc = new jsPDF();
      const pageWidth = doc.internal.pageSize.width;
      const pageHeight = doc.internal.pageSize.height;

      // Configuration des couleurs
      const primaryColor = [102, 126, 234]; // #667eea
      const secondaryColor = [248, 249, 250]; // #f8f9fa
      const textColor = [51, 51, 51]; // #333333

      // En-tête du document
      doc.setFillColor(...primaryColor);
      doc.rect(0, 0, pageWidth, 40, 'F');

      // Logo et titre
      doc.setTextColor(255, 255, 255);
      doc.setFontSize(24);
      doc.setFont('helvetica', 'bold');
      doc.text('BarStock Wise', 20, 25);

      doc.setFontSize(14);
      doc.setFont('helvetica', 'normal');
      doc.text('Historique des Ventes', 20, 35);

      // Informations du rapport
      doc.setTextColor(...textColor);
      doc.setFontSize(10);
      const currentDate = new Date().toLocaleString('fr-FR');
      doc.text(`Généré le: ${currentDate}`, pageWidth - 80, 50);
      doc.text(`Nombre de ventes: ${filteredSales.length}`, pageWidth - 80, 60);

      // Filtres appliqués
      let yPosition = 70;
      doc.setFontSize(12);
      doc.setFont('helvetica', 'bold');
      doc.text('Filtres appliqués:', 20, yPosition);

      doc.setFont('helvetica', 'normal');
      doc.setFontSize(10);
      yPosition += 10;

      if (searchTerm) {
        doc.text(`• Recherche: ${searchTerm}`, 25, yPosition);
        yPosition += 8;
      }
      if (selectedServer !== 'Tous') {
        doc.text(`• Serveur: ${selectedServer}`, 25, yPosition);
        yPosition += 8;
      }
      if (selectedPayment !== 'Tous') {
        doc.text(`• Paiement: ${selectedPayment}`, 25, yPosition);
        yPosition += 8;
      }
      if (selectedDate) {
        doc.text(`• Date: ${selectedDate}`, 25, yPosition);
        yPosition += 8;
      }

      // Statistiques résumées
      yPosition += 10;
      const totalRevenue = filteredSales.reduce((sum, sale) =>
        sum + (parseNumericValue(sale.final_amount) || parseNumericValue(sale.total_amount)), 0);
      const totalProfit = filteredSales.reduce((sum, sale) =>
        sum + parseNumericValue(sale.profit), 0);
      const avgOrder = totalRevenue / Math.max(1, filteredSales.length);

      doc.setFillColor(...secondaryColor);
      doc.rect(15, yPosition, pageWidth - 30, 25, 'F');

      doc.setFontSize(12);
      doc.setFont('helvetica', 'bold');
      doc.text('Résumé Financier', 20, yPosition + 8);

      doc.setFont('helvetica', 'normal');
      doc.setFontSize(10);
      doc.text(`Chiffre d'affaires total: ${formatAmount(totalRevenue)} BIF`, 20, yPosition + 16);
      doc.text(`Profit total estimé: ${formatAmount(totalProfit)} BIF`, 20, yPosition + 22);
      doc.text(`Panier moyen: ${formatAmount(avgOrder)} BIF`, pageWidth - 100, yPosition + 16);
      doc.text(`Marge moyenne: ${totalRevenue > 0 ? ((totalProfit / totalRevenue) * 100).toFixed(1) : 0}%`, pageWidth - 100, yPosition + 22);

      yPosition += 35;

      // Préparer les données pour le tableau
      const tableData = filteredSales.map(sale => {
        const serverName = sale.server_name || sale.server?.username || 'N/A';
        const tableInfo = sale.table_number || sale.table?.number || sale.customer_name || 'Emporter';
        const paymentMethod = sale.payment_method_display ||
                            (sale.payment_method === 'cash' ? 'Espèces' :
                             sale.payment_method === 'card' ? 'Carte' :
                             sale.payment_method === 'mobile' ? 'Mobile Money' :
                             sale.payment_method || 'N/A');

        return [
          new Date(sale.created_at).toLocaleDateString('fr-FR'),
          sale.reference || `#${sale.id}`,
          tableInfo,
          serverName,
          paymentMethod,
          `${formatAmount(sale.final_amount || sale.total_amount)} BIF`,
          sale.status_display || sale.status || 'N/A'
        ];
      });

      // Créer le tableau avec autoTable
      (doc as any).autoTable({
        startY: yPosition,
        head: [['Date', 'Référence', 'Table', 'Serveur', 'Paiement', 'Montant', 'Statut']],
        body: tableData,
        theme: 'grid',
        styles: {
          fontSize: 8,
          cellPadding: 3,
          textColor: textColor,
        },
        headStyles: {
          fillColor: primaryColor,
          textColor: [255, 255, 255],
          fontStyle: 'bold',
          fontSize: 9,
        },
        alternateRowStyles: {
          fillColor: [248, 249, 250],
        },
        columnStyles: {
          0: { cellWidth: 25 }, // Date
          1: { cellWidth: 30 }, // Référence
          2: { cellWidth: 25 }, // Table
          3: { cellWidth: 30 }, // Serveur
          4: { cellWidth: 25 }, // Paiement
          5: { cellWidth: 30, halign: 'right' }, // Montant
          6: { cellWidth: 20 }, // Statut
        },
        margin: { left: 15, right: 15 },
        didDrawPage: function(data: any) {
          // Pied de page
          const pageNumber = doc.internal.getCurrentPageInfo().pageNumber;
          const totalPages = doc.internal.getNumberOfPages();

          doc.setFontSize(8);
          doc.setTextColor(128, 128, 128);
          doc.text(`Page ${pageNumber} sur ${totalPages}`, pageWidth - 40, pageHeight - 10);
          doc.text('BarStock Wise - Système de Gestion', 20, pageHeight - 10);
        }
      });

      // Sauvegarder le PDF
      const fileName = `historique-ventes_${new Date().toISOString().split('T')[0]}_${filteredSales.length}-ventes.pdf`;
      doc.save(fileName);

      toast({
        title: "Export PDF réussi",
        description: `${filteredSales.length} vente(s) exportée(s) en PDF. Fichier: ${fileName}`,
      });
    } catch (error) {
      console.error('Erreur lors de l\'export PDF:', error);
      toast({
        title: "Erreur d'export",
        description: `Impossible d'exporter en PDF: ${error instanceof Error ? error.message : 'Erreur inconnue'}`,
        variant: "destructive",
      });
    }
  };

  const getTotalRevenue = () => filteredSales.reduce((sum, sale) => {
    const finalAmount = parseNumericValue(sale.final_amount);
    const totalAmount = parseNumericValue(sale.total_amount);
    const amount = finalAmount || totalAmount;
    return sum + amount;
  }, 0);

  const getAverageTicket = () => filteredSales.length > 0 ? getTotalRevenue() / filteredSales.length : 0;

  const getMostPopularPaymentMethod = () => {
    if (filteredSales.length === 0) return 'N/A';

    const paymentCounts = filteredSales.reduce((acc, sale) => {
      const method = sale.payment_method_display ||
                    (sale.payment_method === 'cash' ? 'Espèces' :
                     sale.payment_method === 'card' ? 'Carte' :
                     sale.payment_method === 'mobile' ? 'Mobile Money' :
                     sale.payment_method === 'credit' ? 'Crédit' :
                     sale.payment_method || 'Non spécifié');
      acc[method] = (acc[method] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return Object.entries(paymentCounts).reduce((a, b) =>
      paymentCounts[a[0]] > paymentCounts[b[0]] ? a : b
    )[0];
  };

  const getPaymentMethodBadge = (sale: Sale) => {
    const method = sale.payment_method_display ||
                  (sale.payment_method === 'cash' ? 'Espèces' :
                   sale.payment_method === 'card' ? 'Carte' :
                   sale.payment_method === 'mobile' ? 'Mobile Money' :
                   sale.payment_method === 'credit' ? 'Crédit' :
                   sale.payment_method || 'Non spécifié');

    switch (method) {
      case 'Espèces':
        return <Badge variant="default">Espèces</Badge>;
      case 'Carte':
        return <Badge variant="secondary">Carte</Badge>;
      case 'Mobile Money':
        return <Badge variant="outline">Mobile Money</Badge>;
      case 'Crédit':
        return <Badge variant="destructive">Crédit</Badge>;
      default:
        return <Badge variant="outline">{method}</Badge>;
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Historique des Ventes</h1>
          <p className="text-muted-foreground">
            Consultez et analysez toutes les ventes effectuées
          </p>
        </div>
        
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" disabled={salesLoading || filteredSales.length === 0}>
              <Download className="w-4 h-4 mr-2" />
              Exporter CSV ({filteredSales.length})
              <ChevronDown className="w-4 h-4 ml-2" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-56">
            <DropdownMenuItem onClick={exportToExcel}>
              <FileSpreadsheet className="w-4 h-4 mr-2" />
              Export Complet
              <span className="ml-auto text-xs text-muted-foreground">Tous les détails</span>
            </DropdownMenuItem>
            <DropdownMenuItem onClick={exportSummaryCSV}>
              <FileText className="w-4 h-4 mr-2" />
              Export Résumé
              <span className="ml-auto text-xs text-muted-foreground">Simplifié</span>
            </DropdownMenuItem>
            <DropdownMenuItem onClick={exportItemsCSV}>
              <History className="w-4 h-4 mr-2" />
              Export Articles
              <span className="ml-auto text-xs text-muted-foreground">Détail produits</span>
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem onClick={exportToPDF}>
              <FileDown className="w-4 h-4 mr-2" />
              Export PDF
              <span className="ml-auto text-xs text-muted-foreground">Professionnel</span>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      {/* Message informatif si pas de données */}
      {!salesLoading && salesList.length === 0 && (
        <Card>
          <CardContent className="pt-6">
            <div className="text-center py-8">
              <History className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">Aucune vente trouvée</h3>
              <p className="text-muted-foreground">
                Il n'y a pas encore de ventes enregistrées dans le système.
              </p>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Stats Cards */}
      {salesList.length > 0 && (
        <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Ventes Filtrées</CardTitle>
            <History className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{filteredSales.length}</div>
            <p className="text-xs text-muted-foreground">transactions</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Chiffre d'Affaires</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {getTotalRevenue().toLocaleString()} BIF
            </div>
            <p className="text-xs text-muted-foreground">total filtré</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Ticket Moyen</CardTitle>
            <Eye className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {getAverageTicket().toLocaleString()} BIF
            </div>
            <p className="text-xs text-muted-foreground">par transaction</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Méthode Populaire</CardTitle>
            <Filter className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            {salesLoading ? (
              <Skeleton className="h-8 w-20" />
            ) : (
              <div className="text-2xl font-bold">{getMostPopularPaymentMethod()}</div>
            )}
            <p className="text-xs text-muted-foreground">le plus utilisé</p>
          </CardContent>
        </Card>
        </div>
      )}

      {/* Filters */}
      {salesList.length > 0 && (
      <Card>
        <CardContent className="pt-6">
          <div className="grid gap-4 md:grid-cols-5">
            <div className="relative">
              <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Rechercher..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            
            <Select value={selectedServer} onValueChange={setSelectedServer}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {servers.map((server) => (
                  <SelectItem key={server} value={server}>
                    {server}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            
            <Select value={selectedPayment} onValueChange={setSelectedPayment}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {paymentMethods.map((method) => (
                  <SelectItem key={method} value={method}>
                    {method}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            
            <Input
              type="date"
              value={selectedDate}
              onChange={(e) => setSelectedDate(e.target.value)}
            />
            
            <Button 
              variant="outline" 
              onClick={() => {
                setSearchTerm('');
                setSelectedServer('Tous');
                setSelectedPayment('Tous');
                setSelectedDate('');
              }}
            >
              Réinitialiser
            </Button>
          </div>
        </CardContent>
        </Card>
      )}

      {/* Sales Table */}
      {salesList.length > 0 && (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <History className="w-5 h-5" />
            Liste des Ventes
          </CardTitle>
          <CardDescription>
            {filteredSales.length} vente(s) trouvée(s)
          </CardDescription>
        </CardHeader>
        <CardContent>
          {salesLoading ? (
            <LoadingTable />
          ) : filteredSales.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-muted-foreground">Aucune vente trouvée avec les filtres actuels</p>
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Date & Heure</TableHead>
                  <TableHead>Référence</TableHead>
                  <TableHead>Table/Client</TableHead>
                  <TableHead>Articles</TableHead>
                  <TableHead>Serveur</TableHead>
                  <TableHead>Statut</TableHead>
                  <TableHead>Paiement</TableHead>
                  <TableHead>Total</TableHead>
                  {user?.role === 'Admin' && <TableHead>Actions</TableHead>}
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredSales.map((sale) => {
                  const serverName = sale.server_name ||
                                   sale.server?.get_full_name ||
                                   `${sale.server?.first_name || ''} ${sale.server?.last_name || ''}`.trim() ||
                                   sale.server?.username || 'Serveur inconnu';

                  const tableInfo = sale.table_number ||
                                  sale.table?.number ||
                                  sale.customer_name ||
                                  'Emporter';

                  const finalAmount = parseNumericValue(sale.final_amount) || parseNumericValue(sale.total_amount);

                  return (
                    <TableRow key={sale.id}>
                      <TableCell>
                        {new Date(sale.created_at).toLocaleString('fr-FR')}
                      </TableCell>
                      <TableCell className="font-mono text-xs">
                        {sale.reference || sale.id}
                      </TableCell>
                      <TableCell className="font-medium">
                        {tableInfo}
                      </TableCell>
                      <TableCell>
                        <div className="space-y-1 max-w-[200px]">
                          {sale.items?.length > 0 ? (
                            sale.items.map((item, index) => {
                              const productName = item.product_name || item.product?.name || 'Produit inconnu';
                              return (
                                <div key={index} className="text-xs">
                                  {productName} x{item.quantity}
                                  <span className="text-muted-foreground ml-1">
                                    ({formatCurrency(parseNumericValue(item.total_price))})
                                  </span>
                                </div>
                              );
                            })
                          ) : (
                            <span className="text-muted-foreground text-xs">Aucun article</span>
                          )}
                          {sale.items_count && (
                            <div className="text-xs text-muted-foreground">
                              Total: {sale.items_count} article(s)
                            </div>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>{serverName}</TableCell>
                      <TableCell>
                        <Badge
                          variant={
                            sale.status === 'paid' ? 'default' :
                            sale.status === 'pending' ? 'secondary' :
                            sale.status === 'cancelled' ? 'destructive' :
                            'outline'
                          }
                        >
                          {sale.status_display || sale.status}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        {getPaymentMethodBadge(sale)}
                      </TableCell>
                      <TableCell className="font-bold">
                        <div className="space-y-1">
                          <div>{formatCurrency(finalAmount)}</div>
                          {parseNumericValue(sale.discount_amount) > 0 && (
                            <div className="text-xs text-muted-foreground">
                              Remise: -{formatCurrency(parseNumericValue(sale.discount_amount))}
                            </div>
                          )}
                          {parseNumericValue(sale.profit) > 0 && (
                            <div className="text-xs text-green-600">
                              Profit: +{formatCurrency(parseNumericValue(sale.profit))}
                            </div>
                          )}
                        </div>
                      </TableCell>
                      {user?.role === 'Admin' && (
                        <TableCell>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => deleteSale(sale.id)}
                            className="text-destructive hover:text-destructive"
                          >
                            <Trash2 className="w-4 h-4" />
                          </Button>
                        </TableCell>
                      )}
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
          )}
        </CardContent>
        </Card>
      )}
    </div>
  );
};

export default SalesHistory;