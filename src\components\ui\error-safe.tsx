import React, { Component, ErrorInfo, ReactNode } from 'react';
import { AlertTriangle } from 'lucide-react';

interface ErrorSafeProps {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
  componentName?: string;
}

interface ErrorSafeState {
  hasError: boolean;
  error?: Error;
}

class ErrorSafe extends Component<ErrorSafeProps, ErrorSafeState> {
  constructor(props: ErrorSafeProps) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): ErrorSafeState {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error(`ErrorSafe caught an error in ${this.props.componentName || 'component'}:`, error, errorInfo);
    
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }
  }

  componentDidUpdate(prevProps: ErrorSafeProps) {
    // Reset error state when children change
    if (prevProps.children !== this.props.children && this.state.hasError) {
      this.setState({ hasError: false, error: undefined });
    }
  }

  render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        return this.props.fallback;
      }

      return (
        <div className="flex items-center justify-center p-3 border border-destructive/20 rounded-md bg-destructive/5 min-h-[40px]">
          <AlertTriangle className="w-4 h-4 text-destructive mr-2 flex-shrink-0" />
          <span className="text-sm text-destructive">
            Erreur de rendu
          </span>
        </div>
      );
    }

    return this.props.children;
  }
}

// Hook pour utiliser ErrorSafe avec des composants fonctionnels
export function withErrorSafe<P extends object>(
  Component: React.ComponentType<P>,
  fallback?: ReactNode,
  componentName?: string
) {
  const WrappedComponent = (props: P) => (
    <ErrorSafe fallback={fallback} componentName={componentName}>
      <Component {...props} />
    </ErrorSafe>
  );

  WrappedComponent.displayName = `withErrorSafe(${Component.displayName || Component.name})`;
  return WrappedComponent;
}

export default ErrorSafe; 