import React from 'react';
import { Button, ButtonProps } from '@/components/ui/button';
import { usePermissions, Permission, UserRole } from '@/contexts/PermissionsContext';
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip';

interface ProtectedButtonProps extends ButtonProps {
  permission?: Permission;
  permissions?: Permission[];
  requireAll?: boolean;
  roles?: UserRole[];
  fallbackMessage?: string;
  showTooltip?: boolean;
}

export const ProtectedButton: React.FC<ProtectedButtonProps> = ({
  children,
  permission,
  permissions = [],
  requireAll = false,
  roles = [],
  fallbackMessage = "Vous n'avez pas les permissions nécessaires",
  showTooltip = true,
  disabled,
  onClick,
  ...props
}) => {
  const { userRole, hasPermission, hasAnyPermission, hasAllPermissions } = usePermissions();

  // Vérification par rôle
  const hasRoleAccess = roles.length === 0 || roles.includes(userRole);

  // Vérification par permission unique
  const hasSinglePermission = !permission || hasPermission(permission);

  // Vérification par permissions multiples
  const hasMultiplePermissions = permissions.length === 0 || (
    requireAll 
      ? hasAllPermissions(permissions)
      : hasAnyPermission(permissions)
  );

  const hasAccess = hasRoleAccess && hasSinglePermission && hasMultiplePermissions;
  const isDisabled = disabled || !hasAccess;

  const button = (
    <Button
      {...props}
      disabled={isDisabled}
      onClick={hasAccess ? onClick : undefined}
    >
      {children}
    </Button>
  );

  // Si l'utilisateur n'a pas accès et qu'on veut afficher un tooltip
  if (!hasAccess && showTooltip) {
    return (
      <Tooltip>
        <TooltipTrigger asChild>
          {button}
        </TooltipTrigger>
        <TooltipContent>
          <p>{fallbackMessage}</p>
        </TooltipContent>
      </Tooltip>
    );
  }

  return button;
};

export default ProtectedButton;
