// API pour la gestion des tables
import { api, PaginatedResponse, handleApiError } from './base';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';

export interface Table {
  id: number;
  number: string;
  capacity: number;
  status: 'available' | 'occupied' | 'reserved' | 'cleaning';
  location: string;
  is_occupied: boolean;
  is_available: boolean;
  occupation_duration: number;
  occupied_since?: string;
  last_cleaned?: string;
  notes?: string;
  current_sale?: {
    id: number;
    reference: string;
    customer_name: string;
    total_amount: number;
    status: string;
    created_at: string;
  };
  created_at: string;
  updated_at: string;
}

export interface TableReservation {
  id: number;
  table: number;
  table_number: string;
  table_capacity: number;
  customer_name: string;
  customer_phone?: string;
  customer_email?: string;
  party_size: number;
  reservation_date: string;
  reservation_time: string;
  duration_minutes: number;
  status: 'pending' | 'confirmed' | 'seated' | 'completed' | 'cancelled' | 'no_show';
  special_requests?: string;
  notes?: string;
  is_today: boolean;
  is_upcoming: boolean;
  is_overdue: boolean;
  created_by: number;
  created_by_name: string;
  confirmed_by?: number;
  seated_at?: string;
  created_at: string;
  updated_at: string;
}

export interface TableStatusSummary {
  total: number;
  available: number;
  occupied: number;
  reserved: number;
  cleaning: number;
  occupation_rate: number;
}

// API des tables
export const tablesApi = {
  // Lister toutes les tables
  list: () => api.get<Table[]>('/sales/tables/list/'),
  
  // Créer une table
  create: (data: Partial<Table>) => api.post<Table>('/sales/tables/', data),
  
  // Détails d'une table
  get: (id: number) => api.get<Table>(`/sales/tables/${id}/`),
  
  // Modifier une table
  update: (id: number, data: Partial<Table>) => api.put<Table>(`/sales/tables/${id}/`, data),
  
  // Supprimer une table
  delete: (id: number) => api.delete(`/sales/tables/${id}/`),
  
  // Occuper une table
  occupy: (id: number, customerName?: string) => 
    api.post(`/sales/tables/${id}/occupy/`, { customer_name: customerName }),
  
  // Libérer une table
  free: (id: number) => api.post(`/sales/tables/${id}/free/`),
  
  // Résumé des statuts
  statusSummary: () => api.get<TableStatusSummary>('/sales/tables/status-summary/'),
  
  // Tables disponibles
  available: (partySize: number, date?: string, time?: string) => {
    const params = new URLSearchParams({ party_size: partySize.toString() });
    if (date) params.append('date', date);
    if (time) params.append('time', time);
    return api.get<Table[]>(`/sales/tables/available/?${params}`);
  },
  
  // Analyses des tables
  analytics: (days: number = 7) => 
    api.get(`/sales/tables/analytics/?days=${days}`),
};

// API des réservations
export const reservationsApi = {
  // Lister les réservations
  list: (filters?: {
    status?: string;
    table?: number;
    date?: string;
    customer?: string;
  }) => {
    const params = new URLSearchParams();
    if (filters?.status) params.append('status', filters.status);
    if (filters?.table) params.append('table', filters.table.toString());
    if (filters?.date) params.append('reservation_date', filters.date);
    if (filters?.customer) params.append('search', filters.customer);
    
    return api.get<TableReservation[]>(`/sales/reservations/?${params}`);
  },
  
  // Créer une réservation
  create: (data: Partial<TableReservation>) => 
    api.post<TableReservation>('/sales/reservations/', data),
  
  // Détails d'une réservation
  get: (id: number) => api.get<TableReservation>(`/sales/reservations/${id}/`),
  
  // Modifier une réservation
  update: (id: number, data: Partial<TableReservation>) => 
    api.put<TableReservation>(`/sales/reservations/${id}/`, data),
  
  // Supprimer une réservation
  delete: (id: number) => api.delete(`/sales/reservations/${id}/`),
  
  // Confirmer une réservation
  confirm: (id: number) => api.post(`/sales/reservations/${id}/confirm/`),
  
  // Installer les clients
  seat: (id: number) => api.post(`/sales/reservations/${id}/seat/`),
  
  // Réservations du jour
  today: () => api.get<TableReservation[]>('/sales/reservations/today/'),
  
  // Réservations à venir
  upcoming: (hours: number = 2) => 
    api.get<TableReservation[]>(`/sales/reservations/upcoming/?hours=${hours}`),
};

// Hooks React Query pour les tables
export const useTablesQueries = () => {
  const queryClient = useQueryClient();
  
  // Liste des tables
  const useTablesList = () => useQuery({
    queryKey: ['tables'],
    queryFn: () => tablesApi.list(),
  });
  
  // Résumé des statuts
  const useTableStatusSummary = () => useQuery({
    queryKey: ['tables', 'status-summary'],
    queryFn: () => tablesApi.statusSummary(),
    refetchInterval: 30000, // Actualiser toutes les 30 secondes
  });
  
  // Occuper une table
  const useOccupyTable = () => useMutation({
    mutationFn: ({ id, customerName }: { id: number; customerName?: string }) =>
      tablesApi.occupy(id, customerName),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['tables'] });
    },
  });
  
  // Libérer une table
  const useFreeTable = () => useMutation({
    mutationFn: (id: number) => tablesApi.free(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['tables'] });
    },
  });
  
  return {
    useTablesList,
    useTableStatusSummary,
    useOccupyTable,
    useFreeTable,
  };
};

// Hooks pour les réservations
export const useReservationsQueries = () => {
  const queryClient = useQueryClient();
  
  // Liste des réservations
  const useReservationsList = (filters?: Parameters<typeof reservationsApi.list>[0]) => 
    useQuery({
      queryKey: ['reservations', filters],
      queryFn: () => reservationsApi.list(filters),
    });
  
  // Réservations du jour
  const useTodaysReservations = () => useQuery({
    queryKey: ['reservations', 'today'],
    queryFn: () => reservationsApi.today(),
    refetchInterval: 60000, // Actualiser toutes les minutes
  });
  
  // Créer une réservation
  const useCreateReservation = () => useMutation({
    mutationFn: (data: Partial<TableReservation>) => reservationsApi.create(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['reservations'] });
      queryClient.invalidateQueries({ queryKey: ['tables'] });
    },
  });
  
  // Confirmer une réservation
  const useConfirmReservation = () => useMutation({
    mutationFn: (id: number) => reservationsApi.confirm(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['reservations'] });
    },
  });
  
  // Installer les clients
  const useSeatReservation = () => useMutation({
    mutationFn: (id: number) => reservationsApi.seat(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['reservations'] });
      queryClient.invalidateQueries({ queryKey: ['tables'] });
    },
  });
  
  return {
    useReservationsList,
    useTodaysReservations,
    useCreateReservation,
    useConfirmReservation,
    useSeatReservation,
  };
};
