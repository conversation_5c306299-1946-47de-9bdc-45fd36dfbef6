/**
 * Tests unitaires pour StockSyncService
 */

import { describe, it, expect, beforeEach, vi, Mock } from 'vitest';
import { StockSyncService } from '../lib/syncService';
import apiClient from '../lib/api';

// Mock de l'API client
vi.mock('../lib/api', () => ({
  default: {
    get: vi.fn(),
    post: vi.fn(),
    put: vi.fn(),
    patch: vi.fn(),
    options: vi.fn(),
  },
}));

const mockApiClient = apiClient as {
  get: Mock;
  post: Mock;
  put: Mock;
  patch: Mock;
  options: Mock;
};

describe('StockSyncService', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('checkInventoryAPI', () => {
    it('devrait retourner true si l\'API supporte POST et PUT', async () => {
      mockApiClient.options.mockResolvedValue({
        headers: { allow: 'GET, POST, PUT, DELETE' }
      });

      const result = await StockSyncService.checkInventoryAPI();
      expect(result).toBe(true);
      expect(mockApiClient.options).toHaveBeenCalledWith('/inventory/');
    });

    it('devrait retourner false si l\'API ne supporte que GET', async () => {
      mockApiClient.options.mockResolvedValue({
        headers: { allow: 'GET' }
      });

      const result = await StockSyncService.checkInventoryAPI();
      expect(result).toBe(false);
    });

    it('devrait retourner false en cas d\'erreur', async () => {
      mockApiClient.options.mockRejectedValue(new Error('API Error'));

      const result = await StockSyncService.checkInventoryAPI();
      expect(result).toBe(false);
    });
  });

  describe('syncProductsToInventory', () => {
    it('devrait synchroniser les produits vers l\'inventaire si l\'API est disponible', async () => {
      // Mock API disponible
      mockApiClient.options.mockResolvedValue({
        headers: { allow: 'GET, POST, PUT, DELETE' }
      });

      // Mock données produits
      mockApiClient.get.mockResolvedValue({
        data: [
          {
            id: 1,
            name: 'Primus 72cl',
            current_stock: 50,
            minimum_stock: 20,
            purchase_price: 2500,
            selling_price: 3000
          }
        ]
      });

      // Mock création inventaire
      mockApiClient.post.mockResolvedValue({ data: { id: 1 } });

      const result = await StockSyncService.syncProductsToInventory();

      expect(result.success).toBe(true);
      expect(result.syncedItems).toBe(1);
      expect(mockApiClient.post).toHaveBeenCalledWith('/inventory/', expect.objectContaining({
        product_id: 1,
        product_name: 'Primus 72cl',
        current_stock: 50,
        minimum_stock: 20,
        purchase_price: 2500,
        selling_price: 3000
      }));
    });

    it('devrait retourner un message informatif si l\'API n\'est pas disponible', async () => {
      // Mock API non disponible
      mockApiClient.options.mockResolvedValue({
        headers: { allow: 'GET' }
      });

      const result = await StockSyncService.syncProductsToInventory();

      expect(result.success).toBe(true);
      expect(result.message).toContain('source de vérité');
      expect(result.syncedItems).toBe(0);
    });
  });

  describe('checkConsistency', () => {
    it('devrait détecter les incohérences entre produits et inventaire', async () => {
      // Mock produits
      mockApiClient.get.mockImplementation((url) => {
        if (url === '/products/') {
          return Promise.resolve({
            data: [
              { id: 1, name: 'Produit 1' },
              { id: 2, name: 'Produit 2' },
              { id: 3, name: 'Produit 3' }
            ]
          });
        }
        if (url === '/inventory/') {
          return Promise.resolve({
            data: [
              { product_id: 1, product_name: 'Produit 1' },
              { product_id: 4, product_name: 'Produit 4' } // Produit inexistant
            ]
          });
        }
      });

      const result = await StockSyncService.checkConsistency();

      expect(result.consistent).toBe(false);
      expect(result.issues).toHaveLength(2);
      expect(result.summary.productsCount).toBe(3);
      expect(result.summary.inventoryCount).toBe(2);
      expect(result.summary.missingInInventory).toBe(2); // Produits 2 et 3
      expect(result.summary.missingInProducts).toBe(1); // Produit 4
    });

    it('devrait considérer comme cohérent en mode lecture seule', async () => {
      // Mock API lecture seule
      mockApiClient.options.mockResolvedValue({
        headers: { allow: 'GET' }
      });

      // Mock données
      mockApiClient.get.mockImplementation((url) => {
        if (url === '/products/') {
          return Promise.resolve({ data: [{ id: 1, name: 'Produit 1' }] });
        }
        if (url === '/inventory/') {
          return Promise.resolve({ data: [] }); // Inventaire vide
        }
      });

      const result = await StockSyncService.checkConsistency();

      expect(result.consistent).toBe(true);
      expect(result.issues).toContain('lecture seule');
    });
  });

  describe('fullSync', () => {
    it('devrait effectuer une synchronisation complète', async () => {
      // Mock API disponible
      mockApiClient.options.mockResolvedValue({
        headers: { allow: 'GET, POST, PUT, DELETE' }
      });

      // Mock données
      mockApiClient.get.mockImplementation((url) => {
        if (url === '/products/') {
          return Promise.resolve({
            data: [{ id: 1, name: 'Produit 1', current_stock: 10 }]
          });
        }
        if (url === '/inventory/') {
          return Promise.resolve({
            data: [{ product_id: 1, current_stock: 10 }]
          });
        }
      });

      const result = await StockSyncService.fullSync();

      expect(result.success).toBe(true);
      expect(result.syncedItems).toBeGreaterThan(0);
    });
  });
});

// Tests d'intégration
describe('StockSyncService - Tests d\'intégration', () => {
  it('devrait gérer un workflow complet de synchronisation', async () => {
    // Simuler un scénario réel complet
    
    // 1. Vérifier l'API
    mockApiClient.options.mockResolvedValue({
      headers: { allow: 'GET, POST, PUT, DELETE' }
    });

    const apiAvailable = await StockSyncService.checkInventoryAPI();
    expect(apiAvailable).toBe(true);

    // 2. Synchroniser les produits
    mockApiClient.get.mockResolvedValue({
      data: [
        { id: 1, name: 'Test Product', current_stock: 25, minimum_stock: 10 }
      ]
    });
    mockApiClient.post.mockResolvedValue({ data: { id: 1 } });

    const syncResult = await StockSyncService.syncProductsToInventory();
    expect(syncResult.success).toBe(true);

    // 3. Vérifier la cohérence
    mockApiClient.get.mockImplementation((url) => {
      if (url === '/products/') {
        return Promise.resolve({
          data: [{ id: 1, name: 'Test Product' }]
        });
      }
      if (url === '/inventory/') {
        return Promise.resolve({
          data: [{ product_id: 1, product_name: 'Test Product' }]
        });
      }
    });

    const consistencyResult = await StockSyncService.checkConsistency();
    expect(consistencyResult.consistent).toBe(true);
  });

  it('devrait gérer gracieusement les erreurs réseau', async () => {
    // Simuler une erreur réseau
    mockApiClient.options.mockRejectedValue(new Error('Network Error'));
    mockApiClient.get.mockRejectedValue(new Error('Network Error'));

    const apiAvailable = await StockSyncService.checkInventoryAPI();
    expect(apiAvailable).toBe(false);

    const syncResult = await StockSyncService.syncProductsToInventory();
    expect(syncResult.success).toBe(true); // Devrait réussir en mode fallback
    expect(syncResult.message).toContain('source de vérité');
  });
});

// Tests de performance
describe('StockSyncService - Tests de performance', () => {
  it('devrait traiter un grand nombre de produits efficacement', async () => {
    const startTime = Date.now();
    
    // Mock un grand nombre de produits
    const products = Array.from({ length: 1000 }, (_, i) => ({
      id: i + 1,
      name: `Produit ${i + 1}`,
      current_stock: Math.floor(Math.random() * 100),
      minimum_stock: 10
    }));

    mockApiClient.options.mockResolvedValue({
      headers: { allow: 'GET, POST, PUT, DELETE' }
    });
    mockApiClient.get.mockResolvedValue({ data: products });
    mockApiClient.post.mockResolvedValue({ data: { id: 1 } });

    const result = await StockSyncService.syncProductsToInventory();
    
    const duration = Date.now() - startTime;
    
    expect(result.success).toBe(true);
    expect(result.syncedItems).toBe(1000);
    expect(duration).toBeLessThan(5000); // Moins de 5 secondes
  });
});

export {};
