import React from 'react';
import { QueryClient } from '@tanstack/react-query';

/**
 * Service d'optimisation des requêtes API
 */
export class APIOptimizer {
  private static cache = new Map<string, { data: any; timestamp: number; ttl: number }>();
  private static pendingRequests = new Map<string, Promise<any>>();

  /**
   * Cache intelligent avec TTL
   */
  static async getCachedData<T>(
    key: string, 
    fetcher: () => Promise<T>, 
    ttl: number = 5 * 60 * 1000 // 5 minutes par défaut
  ): Promise<T> {
    const now = Date.now();
    const cached = this.cache.get(key);

    // Vérifier si les données en cache sont encore valides
    if (cached && (now - cached.timestamp) < cached.ttl) {
      console.log(`📦 Cache hit pour ${key}`);
      return cached.data;
    }

    // Éviter les requêtes multiples simultanées
    if (this.pendingRequests.has(key)) {
      console.log(`⏳ Requête en cours pour ${key}`);
      return this.pendingRequests.get(key)!;
    }

    // Nouvelle requête
    console.log(`🌐 Nouvelle requête pour ${key}`);
    const promise = fetcher().then(data => {
      this.cache.set(key, { data, timestamp: now, ttl });
      this.pendingRequests.delete(key);
      return data;
    }).catch(error => {
      this.pendingRequests.delete(key);
      throw error;
    });

    this.pendingRequests.set(key, promise);
    return promise;
  }

  /**
   * Invalidation sélective du cache
   */
  static invalidateCache(pattern?: string) {
    if (pattern) {
      // Invalider les clés qui correspondent au pattern
      for (const key of this.cache.keys()) {
        if (key.includes(pattern)) {
          this.cache.delete(key);
          console.log(`🗑️ Cache invalidé pour ${key}`);
        }
      }
    } else {
      // Invalider tout le cache
      this.cache.clear();
      console.log('🗑️ Cache entièrement invalidé');
    }
  }

  /**
   * Préchargement intelligent des données
   */
  static async preloadData(endpoints: Array<{ key: string; fetcher: () => Promise<any>; ttl?: number }>) {
    console.log('🚀 Préchargement des données...');
    
    const promises = endpoints.map(({ key, fetcher, ttl }) => 
      this.getCachedData(key, fetcher, ttl).catch(error => {
        console.warn(`⚠️ Erreur préchargement ${key}:`, error);
        return null;
      })
    );

    await Promise.allSettled(promises);
    console.log('✅ Préchargement terminé');
  }

  /**
   * Nettoyage automatique du cache expiré
   */
  static cleanExpiredCache() {
    const now = Date.now();
    let cleaned = 0;

    for (const [key, cached] of this.cache.entries()) {
      if ((now - cached.timestamp) > cached.ttl) {
        this.cache.delete(key);
        cleaned++;
      }
    }

    if (cleaned > 0) {
      console.log(`🧹 ${cleaned} entrées de cache expirées nettoyées`);
    }
  }

  /**
   * Statistiques du cache
   */
  static getCacheStats() {
    const now = Date.now();
    let valid = 0;
    let expired = 0;

    for (const cached of this.cache.values()) {
      if ((now - cached.timestamp) < cached.ttl) {
        valid++;
      } else {
        expired++;
      }
    }

    return {
      total: this.cache.size,
      valid,
      expired,
      pending: this.pendingRequests.size
    };
  }
}

/**
 * Configuration optimisée pour React Query
 */
export const optimizedQueryConfig = {
  defaultOptions: {
    queries: {
      // Cache pendant 5 minutes
      staleTime: 5 * 60 * 1000,
      // Garde en cache pendant 10 minutes
      cacheTime: 10 * 60 * 1000,
      // Retry intelligent
      retry: (failureCount: number, error: any) => {
        // Ne pas retry sur les erreurs 4xx
        if (error?.response?.status >= 400 && error?.response?.status < 500) {
          return false;
        }
        // Retry jusqu'à 3 fois pour les autres erreurs
        return failureCount < 3;
      },
      // Délai de retry progressif
      retryDelay: (attemptIndex: number) => Math.min(1000 * 2 ** attemptIndex, 30000),
      // Refetch en arrière-plan
      refetchOnWindowFocus: false,
      refetchOnReconnect: true,
    },
    mutations: {
      // Retry pour les mutations critiques
      retry: 1,
      retryDelay: 1000,
    },
  },
};

/**
 * Hook pour l'optimisation automatique
 */
export const useAPIOptimization = () => {
  // Nettoyage automatique toutes les 10 minutes
  React.useEffect(() => {
    const interval = setInterval(() => {
      APIOptimizer.cleanExpiredCache();
    }, 10 * 60 * 1000);

    return () => clearInterval(interval);
  }, []);

  return {
    getCachedData: APIOptimizer.getCachedData,
    invalidateCache: APIOptimizer.invalidateCache,
    preloadData: APIOptimizer.preloadData,
    getCacheStats: APIOptimizer.getCacheStats,
  };
};

export default APIOptimizer;
