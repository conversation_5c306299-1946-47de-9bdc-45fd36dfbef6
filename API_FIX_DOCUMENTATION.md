# Correction de l'erreur API 400 (Bad Request)

## Problème identifié

L'erreur `POST http://localhost:8000/api/sales/ 400 (Bad Request)` était causée par un format de données incorrect envoyé au backend.

## Analyse du problème

### Format envoyé par le frontend (incorrect)
```javascript
{
  table_number: "Table 1",           // ❌ String au lieu d'ID
  items: [
    {
      product_id: "123",             // ❌ product_id au lieu de product
      quantity: 2,
      unit_price: 1500               // ❌ Non requis (calculé automatiquement)
    }
  ],
  total_amount: 3000,                // ❌ Non requis (calculé automatiquement)
  customer_name: "<PERSON>"          // ❌ Non supporté dans ce format
}
```

### Format attendu par le backend (correct)
```javascript
{
  table: 1,                          // ✅ ID de la table
  items: [
    {
      product: 123,                  // ✅ ID du produit
      quantity: 2                    // ✅ Seulement quantité requise
    }
  ],
  payment_method: "cash",            // ✅ Méthode de paiement
  notes: "Client: <PERSON>"          // ✅ Notes optionnelles
}
```

## Solutions implémentées

### 1. Correction du format des données

**Fichier :** `src/pages/Sales.tsx`

- Conversion du `table_number` (string) en `table` (ID)
- Remplacement de `product_id` par `product`
- Suppression des champs non requis (`unit_price`, `total_amount`)
- Ajout du champ `payment_method`
- Utilisation du champ `notes` pour le nom du client

### 2. Ajout de la sélection de méthode de paiement

**Fichier :** `src/components/ui/payment-method-select.tsx`

- Composant de sélection de méthode de paiement
- Options : Espèces, Carte bancaire, Mobile Money, Crédit
- Intégration avec le CustomSelect

### 3. Amélioration de la gestion des tables

- Recherche de l'objet table par numéro
- Validation de l'existence de la table
- Gestion des erreurs si la table n'est pas trouvée

## Code corrigé

### Avant
```javascript
const saleData = {
  table_number: selectedTable,
  items: currentSale.map(item => ({
    product_id: item.id,
    quantity: item.quantity,
    unit_price: item.price
  })),
  total_amount: getCartTotal(),
  customer_name: customerName || undefined
};
```

### Après
```javascript
// Extraire l'ID de la table depuis le string "Table X"
const tableNumber = selectedTable.replace('Table ', '');

// Trouver l'objet table correspondant
const tableObject = tablesData?.results?.find(table => table.number === tableNumber) ||
                   (Array.isArray(tablesData) ? tablesData.find(table => table.number === tableNumber) : null);

if (!tableObject) {
  toast({
    title: "Erreur",
    description: "Table introuvable",
    variant: "destructive",
  });
  return;
}

const saleData = {
  table: tableObject.id, // ID de la table
  items: currentSale.map(item => ({
    product: item.id, // ID du produit
    quantity: item.quantity
  })),
  payment_method: paymentMethod,
  notes: customerName ? `Client: ${customerName}` : undefined
};
```

## Modèles de données backend

### SaleCreateSerializer
```python
class SaleCreateSerializer(serializers.ModelSerializer):
    items = SaleItemCreateSerializer(many=True)
    
    class Meta:
        model = Sale
        fields = ['table', 'payment_method', 'discount_amount', 'notes', 'items']
```

### SaleItemCreateSerializer
```python
class SaleItemCreateSerializer(serializers.ModelSerializer):
    class Meta:
        model = SaleItem
        fields = ['product', 'quantity', 'notes']
```

## Tests recommandés

1. **Création de vente simple** : Une table, un produit
2. **Création de vente multiple** : Une table, plusieurs produits
3. **Gestion des erreurs** : Table inexistante, produit sans stock
4. **Méthodes de paiement** : Tester toutes les options
5. **Notes client** : Vérifier l'enregistrement des notes

## Validation des données

Le backend valide automatiquement :
- Existence de la table
- Existence des produits
- Stock suffisant pour chaque produit
- Quantités positives
- Méthode de paiement valide

## Gestion des erreurs

Les erreurs courantes et leurs solutions :
- **Table introuvable** : Vérifier que la table existe et est active
- **Produit introuvable** : Vérifier que le produit existe et est actif
- **Stock insuffisant** : Vérifier le stock disponible
- **Données invalides** : Vérifier le format des données envoyées

## Améliorations futures

1. **Validation côté client** : Vérifier le stock avant l'envoi
2. **Cache des données** : Mettre en cache les tables et produits
3. **Retry automatique** : Réessayer en cas d'erreur temporaire
4. **Feedback en temps réel** : Mettre à jour le stock en temps réel
5. **Historique des ventes** : Afficher les ventes récentes

Ces corrections devraient résoudre définitivement l'erreur 400 et permettre la création correcte des ventes. 