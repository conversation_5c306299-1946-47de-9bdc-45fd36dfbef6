# Script PowerShell pour démarrer l'environnement de développement complet
Write-Host "🚀 Démarrage de l'environnement de développement BarStockWise" -ForegroundColor Green
Write-Host "=" * 60 -ForegroundColor Cyan

# Fonction pour démarrer le backend en arrière-plan
function Start-Backend {
    Write-Host "🔧 Démarrage du backend Django..." -ForegroundColor Yellow
    Start-Process PowerShell -ArgumentList "-NoExit", "-Command", "& '.\start-backend.ps1'"
    Start-Sleep -Seconds 3
}

# Fonction pour démarrer le frontend
function Start-Frontend {
    Write-Host "🎨 Démarrage du frontend React..." -ForegroundColor Yellow
    Start-Sleep -Seconds 5  # Attendre que le backend soit prêt
    Start-Process PowerShell -ArgumentList "-NoExit", "-Command", "& '.\start-frontend.ps1'"
}

# Vérifier les prérequis
Write-Host "🔍 Vérification des prérequis..." -ForegroundColor Cyan

# Vérifier Python
try {
    $pythonVersion = python --version 2>&1
    Write-Host "✅ Python: $pythonVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ Python manquant" -ForegroundColor Red
    Write-Host "📥 Installez Python depuis https://python.org/" -ForegroundColor Yellow
    exit 1
}

# Vérifier Node.js
try {
    $nodeVersion = node --version 2>&1
    Write-Host "✅ Node.js: $nodeVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ Node.js manquant" -ForegroundColor Red
    Write-Host "📥 Installez Node.js depuis https://nodejs.org/" -ForegroundColor Yellow
    exit 1
}

# Vérifier la structure du projet
if (!(Test-Path "backend") -or !(Test-Path "src")) {
    Write-Host "❌ Structure de projet invalide" -ForegroundColor Red
    Write-Host "📁 Assurez-vous d'être dans le dossier racine du projet" -ForegroundColor Yellow
    exit 1
}

Write-Host "✅ Tous les prérequis sont satisfaits" -ForegroundColor Green
Write-Host ""

# Demander à l'utilisateur ce qu'il veut démarrer
Write-Host "🎯 Que voulez-vous démarrer ?" -ForegroundColor Cyan
Write-Host "1. Backend seulement" -ForegroundColor White
Write-Host "2. Frontend seulement" -ForegroundColor White
Write-Host "3. Backend + Frontend (recommandé)" -ForegroundColor White
Write-Host "4. Annuler" -ForegroundColor White

$choice = Read-Host "Votre choix (1-4)"

switch ($choice) {
    "1" {
        Write-Host "🔧 Démarrage du backend uniquement..." -ForegroundColor Yellow
        & ".\start-backend.ps1"
    }
    "2" {
        Write-Host "🎨 Démarrage du frontend uniquement..." -ForegroundColor Yellow
        Write-Host "⚠️ Assurez-vous que le backend est déjà démarré !" -ForegroundColor Yellow
        & ".\start-frontend.ps1"
    }
    "3" {
        Write-Host "🚀 Démarrage de l'environnement complet..." -ForegroundColor Green
        Write-Host ""
        Write-Host "📋 Informations importantes:" -ForegroundColor Cyan
        Write-Host "   • Backend Django: http://localhost:8000" -ForegroundColor White
        Write-Host "   • Frontend React: http://localhost:5173" -ForegroundColor White
        Write-Host "   • Admin Django: http://localhost:8000/admin" -ForegroundColor White
        Write-Host "   • API Documentation: http://localhost:8000/api/" -ForegroundColor White
        Write-Host ""
        Write-Host "🔑 Comptes de test:" -ForegroundColor Cyan
        Write-Host "   • Admin: admin / admin123" -ForegroundColor White
        Write-Host "   • Gérant: gerant / gerant123" -ForegroundColor White
        Write-Host "   • Serveur: serveur1 / serveur123" -ForegroundColor White
        Write-Host ""
        
        Start-Backend
        Start-Frontend
        
        Write-Host "✅ Environnement de développement démarré !" -ForegroundColor Green
        Write-Host "🌐 Ouvrez http://localhost:5173 dans votre navigateur" -ForegroundColor Green
        Write-Host "🛑 Fermez les fenêtres PowerShell pour arrêter les serveurs" -ForegroundColor Yellow
    }
    "4" {
        Write-Host "❌ Opération annulée" -ForegroundColor Red
        exit 0
    }
    default {
        Write-Host "❌ Choix invalide" -ForegroundColor Red
        exit 1
    }
}

Write-Host ""
Write-Host "🎉 Bonne développement avec BarStockWise !" -ForegroundColor Green
