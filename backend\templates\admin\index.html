{% extends "admin/base_site.html" %}
{% load i18n static admin_urls %}

{% block title %}{{ title }} | {{ site_title|default:_('Django site admin') }}{% endblock %}

{% block branding %}
<h1 id="site-name">
    <a href="{% url 'admin:index' %}">
        <img src="{% static 'admin/img/logo.png' %}" alt="BarStockWise" height="40" style="margin-right: 10px;">
        BarStockWise Administration
    </a>
</h1>
{% endblock %}

{% block content %}
<div id="content-main">
    
    <!-- Statistiques rapides -->
    {% if stats %}
    <div class="dashboard-stats" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 30px;">
        
        <div class="stat-card" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 8px; text-align: center;">
            <h3 style="margin: 0; font-size: 2em;">{{ stats.users_count }}</h3>
            <p style="margin: 5px 0 0 0; opacity: 0.9;">Utilisateurs actifs</p>
        </div>
        
        <div class="stat-card" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); color: white; padding: 20px; border-radius: 8px; text-align: center;">
            <h3 style="margin: 0; font-size: 2em;">{{ stats.products_count }}</h3>
            <p style="margin: 5px 0 0 0; opacity: 0.9;">Produits actifs</p>
        </div>
        
        <div class="stat-card" style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); color: white; padding: 20px; border-radius: 8px; text-align: center;">
            <h3 style="margin: 0; font-size: 2em;">{{ stats.sales_today }}</h3>
            <p style="margin: 5px 0 0 0; opacity: 0.9;">Ventes aujourd'hui</p>
        </div>
        
        <div class="stat-card" style="background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); color: white; padding: 20px; border-radius: 8px; text-align: center;">
            <h3 style="margin: 0; font-size: 2em;">{{ stats.expenses_today }}</h3>
            <p style="margin: 5px 0 0 0; opacity: 0.9;">Dépenses aujourd'hui</p>
        </div>
        
    </div>
    {% endif %}
    
    <!-- Liens rapides -->
    <div class="quick-actions" style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 30px;">
        <h2 style="margin-top: 0; color: #495057;">Actions rapides</h2>
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 15px;">
            
            <a href="{% url 'admin:sales_sale_add' %}" class="quick-action-btn" style="display: block; padding: 15px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; text-align: center; transition: background 0.3s;">
                <strong>➕ Nouvelle vente</strong><br>
                <small>Enregistrer une nouvelle vente</small>
            </a>
            
            <a href="{% url 'admin:products_product_changelist' %}" class="quick-action-btn" style="display: block; padding: 15px; background: #28a745; color: white; text-decoration: none; border-radius: 5px; text-align: center; transition: background 0.3s;">
                <strong>📦 Gérer les produits</strong><br>
                <small>Voir et modifier les produits</small>
            </a>
            
            <a href="{% url 'admin:inventory_stockmovement_changelist' %}" class="quick-action-btn" style="display: block; padding: 15px; background: #ffc107; color: #212529; text-decoration: none; border-radius: 5px; text-align: center; transition: background 0.3s;">
                <strong>📊 Mouvements de stock</strong><br>
                <small>Suivre les mouvements de stock</small>
            </a>
            
            <a href="{% url 'admin:expenses_expense_add' %}" class="quick-action-btn" style="display: block; padding: 15px; background: #dc3545; color: white; text-decoration: none; border-radius: 5px; text-align: center; transition: background 0.3s;">
                <strong>💰 Nouvelle dépense</strong><br>
                <small>Enregistrer une dépense</small>
            </a>
            
        </div>
    </div>
    
    <!-- Alertes et notifications -->
    <div class="alerts-section" style="margin-bottom: 30px;">
        <h2 style="color: #495057;">Alertes et notifications</h2>
        
        <div class="alert-item" style="background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin-bottom: 10px;">
            <strong>⚠️ Stock bas</strong><br>
            Certains produits ont un stock inférieur au seuil minimum.
            <a href="{% url 'admin:products_product_changelist' %}?current_stock__lte=5" style="color: #856404; text-decoration: underline;">Voir les produits</a>
        </div>
        
        <div class="alert-item" style="background: #d1ecf1; border: 1px solid #bee5eb; padding: 15px; border-radius: 5px; margin-bottom: 10px;">
            <strong>ℹ️ Rappel</strong><br>
            N'oubliez pas de générer le rapport journalier.
            <a href="{% url 'admin:reports_dailyreport_changelist' %}" style="color: #0c5460; text-decoration: underline;">Voir les rapports</a>
        </div>
        
    </div>

    <!-- Contenu par défaut de Django Admin -->
    {% if app_list %}
        <div class="app-list">
            {% for app in app_list %}
                <div class="app-{{ app.app_label }} module">
                    <table>
                        <caption>
                            <a href="{{ app.app_url }}" class="section" title="{% blocktrans with name=app.name %}Models in the {{ name }} application{% endblocktrans %}">{{ app.name }}</a>
                        </caption>
                        {% for model in app.models %}
                            <tr class="model-{{ model.object_name|lower }}">
                                {% if model.admin_url %}
                                    <th scope="row"><a href="{{ model.admin_url }}">{{ model.name }}</a></th>
                                {% else %}
                                    <th scope="row">{{ model.name }}</th>
                                {% endif %}

                                {% if model.add_url %}
                                    <td><a href="{{ model.add_url }}" class="addlink">{% trans 'Add' %}</a></td>
                                {% else %}
                                    <td>&nbsp;</td>
                                {% endif %}

                                {% if model.admin_url %}
                                    {% if model.view_only %}
                                        <td><a href="{{ model.admin_url }}" class="viewlink">{% trans 'View' %}</a></td>
                                    {% else %}
                                        <td><a href="{{ model.admin_url }}" class="changelink">{% trans 'Change' %}</a></td>
                                    {% endif %}
                                {% else %}
                                    <td>&nbsp;</td>
                                {% endif %}
                            </tr>
                        {% endfor %}
                    </table>
                </div>
            {% endfor %}
        </div>
    {% else %}
        <p>{% trans "You don't have permission to view or edit anything." %}</p>
    {% endif %}
</div>

<style>
.quick-action-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.stat-card {
    transition: transform 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
}

.dashboard-stats {
    animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.alert-item {
    animation: slideInLeft 0.5s ease-out;
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}
</style>
{% endblock %}

{% block sidebar %}
<div id="content-related">
    <div class="module" id="recent-actions-module">
        <h2>{% trans 'Recent actions' %}</h2>
        <h3>{% trans 'My actions' %}</h3>
        {% load log %}
        {% get_admin_log 10 as admin_log for_user user %}
        {% if not admin_log %}
            <p>{% trans 'None available' %}</p>
        {% else %}
            <ul class="actionlist">
                {% for entry in admin_log %}
                    <li class="{% if entry.is_addition %}addlink{% endif %}{% if entry.is_change %}changelink{% endif %}{% if entry.is_deletion %}deletelink{% endif %}">
                        {% if entry.is_deletion or not entry.get_admin_url %}
                            {{ entry.object_repr }}
                        {% else %}
                            <a href="{{ entry.get_admin_url }}">{{ entry.object_repr }}</a>
                        {% endif %}
                        <br>
                        {% if entry.content_type %}
                            <span class="mini quiet">{% filter capfirst %}{{ entry.content_type.name }}{% endfilter %}</span>
                        {% else %}
                            <span class="mini quiet">{% trans 'Unknown content' %}</span>
                        {% endif %}
                    </li>
                {% endfor %}
            </ul>
        {% endif %}
    </div>
</div>
{% endblock %}
