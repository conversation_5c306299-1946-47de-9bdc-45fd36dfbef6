<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Simple - Notifications</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px 5px;
            font-size: 16px;
        }
        button:hover {
            background: #0056b3;
        }
        .status {
            background: #e9ecef;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
            font-family: monospace;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .warning { background: #fff3cd; color: #856404; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔔 Test Simple des Notifications</h1>
        <p>Ce test permet de diagnostiquer les problèmes de notifications sans React.</p>
        
        <div id="status" class="status">
            Chargement...
        </div>
        
        <button onclick="checkSupport()">1. Vérifier le Support</button>
        <button onclick="requestPermission()">2. Demander Permission</button>
        <button onclick="sendTestNotification()">3. Test Notification</button>
        <button onclick="showResetInstructions()">4. Instructions Reset</button>
        <button onclick="testInNewWindow()">5. Test Nouvelle Fenêtre</button>
        
        <div id="log" style="margin-top: 20px;"></div>
    </div>

    <script>
        function log(message, type = 'info') {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'error' ? 'error' : type === 'success' ? 'success' : type === 'warning' ? 'warning' : 'status';
            logDiv.innerHTML += `<div class="${className}">[${timestamp}] ${message}</div>`;
            console.log(`[${timestamp}] ${message}`);
        }

        function updateStatus() {
            const statusDiv = document.getElementById('status');
            const support = 'Notification' in window;
            const permission = support ? Notification.permission : 'Non supporté';
            const serviceWorker = 'serviceWorker' in navigator;
            
            statusDiv.innerHTML = `
                <strong>État Actuel :</strong><br>
                Support Notifications: ${support ? '✅ Oui' : '❌ Non'}<br>
                Permission: <strong>${permission}</strong><br>
                Service Worker: ${serviceWorker ? '✅ Oui' : '❌ Non'}<br>
                User Agent: ${navigator.userAgent.split(' ')[0]}
            `;
            
            if (permission === 'denied') {
                statusDiv.className = 'status error';
            } else if (permission === 'granted') {
                statusDiv.className = 'status success';
            } else {
                statusDiv.className = 'status warning';
            }
        }

        function checkSupport() {
            log('🔍 Vérification du support...');
            
            if (!('Notification' in window)) {
                log('❌ Les notifications ne sont pas supportées par ce navigateur', 'error');
                return false;
            }
            
            log('✅ Les notifications sont supportées', 'success');
            log(`📊 Permission actuelle: ${Notification.permission}`);
            
            if ('serviceWorker' in navigator) {
                log('✅ Service Worker supporté', 'success');
            } else {
                log('⚠️ Service Worker non supporté', 'warning');
            }
            
            updateStatus();
            return true;
        }

        async function requestPermission() {
            log('🔐 Demande de permission...');
            
            if (!checkSupport()) return;
            
            try {
                const permission = await Notification.requestPermission();
                log(`📋 Résultat de la permission: ${permission}`);
                
                if (permission === 'granted') {
                    log('✅ Permission accordée!', 'success');
                } else if (permission === 'denied') {
                    log('❌ Permission refusée. Vérifiez les paramètres du navigateur.', 'error');
                    log('💡 Solution: Cliquez sur l\'icône 🔒 dans la barre d\'adresse et autorisez les notifications', 'warning');
                } else {
                    log('⚠️ Permission en attente ou ignorée', 'warning');
                }
                
                updateStatus();
                return permission === 'granted';
            } catch (error) {
                log(`❌ Erreur lors de la demande: ${error.message}`, 'error');
                return false;
            }
        }

        async function sendTestNotification() {
            log('🔔 Envoi de notification de test...');
            
            if (Notification.permission !== 'granted') {
                log('❌ Permission non accordée. Demandez d\'abord la permission.', 'error');
                return;
            }
            
            try {
                const notification = new Notification('🎉 Test Réussi!', {
                    body: 'Les notifications fonctionnent parfaitement sur BarStock Wise',
                    icon: '/icon-192x192.png',
                    badge: '/badge-72x72.png',
                    tag: 'test-notification',
                    requireInteraction: false
                });
                
                notification.onclick = function() {
                    log('👆 Notification cliquée!', 'success');
                    notification.close();
                };
                
                notification.onshow = function() {
                    log('✅ Notification affichée avec succès!', 'success');
                };
                
                notification.onerror = function(error) {
                    log(`❌ Erreur d'affichage: ${error}`, 'error');
                };
                
                // Auto-fermeture après 5 secondes
                setTimeout(() => {
                    notification.close();
                    log('🔄 Notification fermée automatiquement', 'info');
                }, 5000);
                
            } catch (error) {
                log(`❌ Erreur lors de l'envoi: ${error.message}`, 'error');
            }
        }

        function showResetInstructions() {
            log('📋 Instructions pour réinitialiser les permissions:', 'warning');
            log('');
            log('🔧 MÉTHODE 1 - Via l\'icône de la barre d\'adresse:');
            log('1. Cliquez sur l\'icône 🔒 ou ⚙️ à côté de l\'URL');
            log('2. Trouvez "Notifications" et changez vers "Autoriser"');
            log('3. Rechargez la page (F5)');
            log('');
            log('🔧 MÉTHODE 2 - Via les paramètres Chrome:');
            log('1. Allez dans chrome://settings/content/notifications');
            log('2. Dans "Bloqué", trouvez localhost:8080 et supprimez-le');
            log('3. Rechargez la page');
            log('');
            log('🔧 MÉTHODE 3 - Effacer les données du site:');
            log('1. F12 → Application → Storage → Clear storage');
            log('2. Rechargez la page');
            log('');
            log('💡 Si rien ne marche, testez en mode incognito (Ctrl+Shift+N)');
        }

        function testInNewWindow() {
            log('🪟 Ouverture dans une nouvelle fenêtre...');
            const newWindow = window.open(window.location.href, '_blank', 'width=800,height=600');
            if (newWindow) {
                log('✅ Nouvelle fenêtre ouverte. Testez les notifications là-bas.', 'success');
            } else {
                log('❌ Impossible d\'ouvrir une nouvelle fenêtre (popup bloqué)', 'error');
            }
        }

        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            log('🚀 Test des notifications initialisé');
            updateStatus();
            checkSupport();
        });
    </script>
</body>
</html>
