import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { useTables, useSales } from '@/hooks/useApi';

export const DataDebugger = () => {
  const { data: tablesData, isLoading: tablesLoading, error: tablesError } = useTables();
  const { data: salesData, isLoading: salesLoading, error: salesError } = useSales();

  return (
    <Card className="mb-4 border-blue-200 bg-blue-50">
      <CardHeader>
        <CardTitle className="text-blue-800">🔍 Debug Données API</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid gap-4 md:grid-cols-2">
          {/* Tables */}
          <div>
            <h4 className="font-medium mb-2">Tables API</h4>
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <Badge variant={tablesError ? "destructive" : tablesLoading ? "secondary" : "default"}>
                  {tablesError ? "Erreur" : tablesLoading ? "Chargement" : "Chargé"}
                </Badge>
                {tablesError && (
                  <span className="text-xs text-red-600">{tablesError.message}</span>
                )}
              </div>
              
              <div className="text-sm space-y-1">
                <p><strong>Type:</strong> {typeof tablesData}</p>
                <p><strong>Est un tableau:</strong> {Array.isArray(tablesData) ? 'Oui' : 'Non'}</p>
                <p><strong>Longueur:</strong> {Array.isArray(tablesData) ? tablesData.length : 'N/A'}</p>
                {tablesData && typeof tablesData === 'object' && !Array.isArray(tablesData) && (
                  <p><strong>Clés:</strong> {Object.keys(tablesData).join(', ')}</p>
                )}
              </div>
              
              <details className="text-xs">
                <summary className="cursor-pointer font-medium">Données brutes</summary>
                <pre className="mt-2 p-2 bg-white rounded border overflow-auto max-h-32">
                  {JSON.stringify(tablesData, null, 2)}
                </pre>
              </details>
            </div>
          </div>

          {/* Sales */}
          <div>
            <h4 className="font-medium mb-2">Sales API</h4>
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <Badge variant={salesError ? "destructive" : salesLoading ? "secondary" : "default"}>
                  {salesError ? "Erreur" : salesLoading ? "Chargement" : "Chargé"}
                </Badge>
                {salesError && (
                  <span className="text-xs text-red-600">{salesError.message}</span>
                )}
              </div>
              
              <div className="text-sm space-y-1">
                <p><strong>Type:</strong> {typeof salesData}</p>
                <p><strong>Est un tableau:</strong> {Array.isArray(salesData) ? 'Oui' : 'Non'}</p>
                <p><strong>Longueur:</strong> {Array.isArray(salesData) ? salesData.length : 'N/A'}</p>
                {salesData && typeof salesData === 'object' && !Array.isArray(salesData) && (
                  <p><strong>Clés:</strong> {Object.keys(salesData).join(', ')}</p>
                )}
              </div>
              
              <details className="text-xs">
                <summary className="cursor-pointer font-medium">Données brutes</summary>
                <pre className="mt-2 p-2 bg-white rounded border overflow-auto max-h-32">
                  {JSON.stringify(salesData, null, 2)}
                </pre>
              </details>
            </div>
          </div>
        </div>

        {/* URLs testées */}
        <div>
          <h4 className="font-medium mb-2">URLs API</h4>
          <div className="text-sm space-y-1">
            <p><strong>Base URL:</strong> {import.meta.env.VITE_API_URL || 'http://localhost:8000/api'}</p>
            <p><strong>Tables:</strong> /sales/tables/</p>
            <p><strong>Sales:</strong> /sales/</p>
          </div>
        </div>

        {/* Actions de test */}
        <div className="flex gap-2">
          <Button 
            size="sm" 
            variant="outline"
            onClick={() => window.open(`${import.meta.env.VITE_API_URL || 'http://localhost:8000/api'}/sales/tables/`, '_blank')}
          >
            Tester Tables API
          </Button>
          <Button 
            size="sm" 
            variant="outline"
            onClick={() => window.open(`${import.meta.env.VITE_API_URL || 'http://localhost:8000/api'}/sales/`, '_blank')}
          >
            Tester Sales API
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};
