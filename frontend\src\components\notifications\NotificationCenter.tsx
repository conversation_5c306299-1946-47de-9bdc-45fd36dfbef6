import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { 
  Bell, Mail, MessageSquare, Settings, 
  Send, Clock, Users, AlertTriangle,
  CheckCircle, XCircle, Phone
} from 'lucide-react';
import { toast } from 'sonner';

interface NotificationSettings {
  email_enabled: boolean;
  sms_enabled: boolean;
  reservation_confirmations: boolean;
  reservation_reminders: boolean;
  staff_notifications: boolean;
  overdue_alerts: boolean;
}

interface NotificationHistory {
  id: number;
  type: 'email' | 'sms';
  recipient: string;
  subject: string;
  status: 'sent' | 'failed' | 'pending';
  sent_at: string;
  error_message?: string;
}

export function NotificationCenter() {
  const [settings, setSettings] = useState<NotificationSettings>({
    email_enabled: true,
    sms_enabled: true,
    reservation_confirmations: true,
    reservation_reminders: true,
    staff_notifications: true,
    overdue_alerts: true,
  });

  const [history, setHistory] = useState<NotificationHistory[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [testMessage, setTestMessage] = useState('');
  const [testRecipient, setTestRecipient] = useState('');
  const [showTestDialog, setShowTestDialog] = useState(false);

  // Charger les paramètres et l'historique
  useEffect(() => {
    loadSettings();
    loadHistory();
  }, []);

  const loadSettings = async () => {
    try {
      // API call to load notification settings
      // const response = await api.get('/notifications/settings/');
      // setSettings(response.data);
    } catch (error) {
      toast.error('Erreur lors du chargement des paramètres');
    }
  };

  const loadHistory = async () => {
    try {
      // API call to load notification history
      // const response = await api.get('/notifications/history/');
      // setHistory(response.data);
      
      // Mock data for demo
      setHistory([
        {
          id: 1,
          type: 'email',
          recipient: '<EMAIL>',
          subject: 'Confirmation de réservation',
          status: 'sent',
          sent_at: '2024-01-15T10:30:00Z',
        },
        {
          id: 2,
          type: 'sms',
          recipient: '+257 XX XX XX XX',
          subject: 'Rappel de réservation',
          status: 'sent',
          sent_at: '2024-01-15T09:15:00Z',
        },
        {
          id: 3,
          type: 'email',
          recipient: '<EMAIL>',
          subject: 'Confirmation de réservation',
          status: 'failed',
          sent_at: '2024-01-15T08:45:00Z',
          error_message: 'Adresse email invalide',
        },
      ]);
    } catch (error) {
      toast.error('Erreur lors du chargement de l\'historique');
    }
  };

  const updateSettings = async (newSettings: NotificationSettings) => {
    try {
      setIsLoading(true);
      // API call to update settings
      // await api.put('/notifications/settings/', newSettings);
      setSettings(newSettings);
      toast.success('Paramètres mis à jour');
    } catch (error) {
      toast.error('Erreur lors de la mise à jour');
    } finally {
      setIsLoading(false);
    }
  };

  const sendTestNotification = async (type: 'email' | 'sms') => {
    if (!testRecipient || !testMessage) {
      toast.error('Veuillez remplir tous les champs');
      return;
    }

    try {
      setIsLoading(true);
      // API call to send test notification
      // await api.post('/notifications/test/', {
      //   type,
      //   recipient: testRecipient,
      //   message: testMessage,
      // });
      
      toast.success(`Test ${type === 'email' ? 'email' : 'SMS'} envoyé`);
      setShowTestDialog(false);
      setTestMessage('');
      setTestRecipient('');
      loadHistory(); // Refresh history
    } catch (error) {
      toast.error('Erreur lors de l\'envoi du test');
    } finally {
      setIsLoading(false);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'sent':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'failed':
        return <XCircle className="w-4 h-4 text-red-500" />;
      case 'pending':
        return <Clock className="w-4 h-4 text-yellow-500" />;
      default:
        return <AlertTriangle className="w-4 h-4 text-gray-500" />;
    }
  };

  const getStatusBadge = (status: string) => {
    const variants = {
      sent: 'bg-green-500',
      failed: 'bg-red-500',
      pending: 'bg-yellow-500',
    };

    const labels = {
      sent: 'Envoyé',
      failed: 'Échec',
      pending: 'En attente',
    };

    return (
      <Badge className={`text-white ${variants[status as keyof typeof variants] || 'bg-gray-500'}`}>
        {labels[status as keyof typeof labels] || status}
      </Badge>
    );
  };

  return (
    <div className="space-y-6">
      {/* En-tête */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">Centre de Notifications</h1>
          <p className="text-muted-foreground">
            Gérez les notifications email et SMS pour vos clients
          </p>
        </div>
        
        <Dialog open={showTestDialog} onOpenChange={setShowTestDialog}>
          <DialogTrigger asChild>
            <Button>
              <Send className="w-4 h-4 mr-2" />
              Test de notification
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Envoyer un test de notification</DialogTitle>
            </DialogHeader>
            <div className="space-y-4">
              <div>
                <Label htmlFor="recipient">Destinataire</Label>
                <Input
                  id="recipient"
                  value={testRecipient}
                  onChange={(e) => setTestRecipient(e.target.value)}
                  placeholder="<EMAIL> ou +257XXXXXXXX"
                />
              </div>
              <div>
                <Label htmlFor="message">Message</Label>
                <Textarea
                  id="message"
                  value={testMessage}
                  onChange={(e) => setTestMessage(e.target.value)}
                  placeholder="Votre message de test..."
                  rows={3}
                />
              </div>
              <div className="flex gap-2">
                <Button 
                  onClick={() => sendTestNotification('email')}
                  disabled={isLoading}
                  className="flex-1"
                >
                  <Mail className="w-4 h-4 mr-2" />
                  Test Email
                </Button>
                <Button 
                  onClick={() => sendTestNotification('sms')}
                  disabled={isLoading}
                  variant="outline"
                  className="flex-1"
                >
                  <MessageSquare className="w-4 h-4 mr-2" />
                  Test SMS
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      {/* Statistiques rapides */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Emails envoyés</CardTitle>
            <Mail className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {history.filter(h => h.type === 'email' && h.status === 'sent').length}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">SMS envoyés</CardTitle>
            <MessageSquare className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {history.filter(h => h.type === 'sms' && h.status === 'sent').length}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Échecs</CardTitle>
            <XCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">
              {history.filter(h => h.status === 'failed').length}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Taux de succès</CardTitle>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              {history.length > 0 
                ? Math.round((history.filter(h => h.status === 'sent').length / history.length) * 100)
                : 0}%
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        {/* Paramètres de notification */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Settings className="w-5 h-5" />
              Paramètres de notification
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="email-enabled">Notifications email</Label>
                <p className="text-sm text-muted-foreground">
                  Activer l'envoi d'emails automatiques
                </p>
              </div>
              <Switch
                id="email-enabled"
                checked={settings.email_enabled}
                onCheckedChange={(checked) => 
                  updateSettings({ ...settings, email_enabled: checked })
                }
              />
            </div>

            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="sms-enabled">Notifications SMS</Label>
                <p className="text-sm text-muted-foreground">
                  Activer l'envoi de SMS automatiques
                </p>
              </div>
              <Switch
                id="sms-enabled"
                checked={settings.sms_enabled}
                onCheckedChange={(checked) => 
                  updateSettings({ ...settings, sms_enabled: checked })
                }
              />
            </div>

            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="confirmations">Confirmations de réservation</Label>
                <p className="text-sm text-muted-foreground">
                  Envoyer automatiquement les confirmations
                </p>
              </div>
              <Switch
                id="confirmations"
                checked={settings.reservation_confirmations}
                onCheckedChange={(checked) => 
                  updateSettings({ ...settings, reservation_confirmations: checked })
                }
              />
            </div>

            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="reminders">Rappels de réservation</Label>
                <p className="text-sm text-muted-foreground">
                  Envoyer des rappels 1h avant
                </p>
              </div>
              <Switch
                id="reminders"
                checked={settings.reservation_reminders}
                onCheckedChange={(checked) => 
                  updateSettings({ ...settings, reservation_reminders: checked })
                }
              />
            </div>

            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="staff-notifications">Notifications personnel</Label>
                <p className="text-sm text-muted-foreground">
                  Alertes pour le personnel
                </p>
              </div>
              <Switch
                id="staff-notifications"
                checked={settings.staff_notifications}
                onCheckedChange={(checked) => 
                  updateSettings({ ...settings, staff_notifications: checked })
                }
              />
            </div>
          </CardContent>
        </Card>

        {/* Historique des notifications */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Bell className="w-5 h-5" />
              Historique récent
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {history.slice(0, 10).map((notification) => (
                <div key={notification.id} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex items-center gap-3">
                    {notification.type === 'email' ? (
                      <Mail className="w-4 h-4 text-blue-500" />
                    ) : (
                      <MessageSquare className="w-4 h-4 text-green-500" />
                    )}
                    <div>
                      <p className="font-medium text-sm">{notification.subject}</p>
                      <p className="text-xs text-muted-foreground">
                        {notification.recipient}
                      </p>
                      {notification.error_message && (
                        <p className="text-xs text-red-500">
                          {notification.error_message}
                        </p>
                      )}
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    {getStatusBadge(notification.status)}
                    <span className="text-xs text-muted-foreground">
                      {new Date(notification.sent_at).toLocaleTimeString('fr-FR')}
                    </span>
                  </div>
                </div>
              ))}
              
              {history.length === 0 && (
                <div className="text-center py-8 text-muted-foreground">
                  <Bell className="w-12 h-12 mx-auto mb-4 opacity-50" />
                  <p>Aucune notification envoyée récemment</p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
