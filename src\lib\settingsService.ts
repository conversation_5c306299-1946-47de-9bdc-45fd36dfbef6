import apiClient from './api';

// Interface pour les paramètres système
export interface SystemSettings {
  restaurant: {
    name: string;
    address: string;
    phone: string;
    email: string;
    currency: string;
    tax_rate: number;
  };
  notifications: {
    email_enabled: boolean;
    sms_enabled: boolean;
    low_stock_alerts: boolean;
    daily_reports: boolean;
  };
  printing: {
    auto_print_receipts: boolean;
    receipt_copies: number;
    printer_name: string;
  };
  system: {
    language: string;
    timezone: string;
    date_format: string;
    backup_frequency: string;
  };
}

// Paramètres par défaut
const DEFAULT_SETTINGS: SystemSettings = {
  restaurant: {
    name: "BarS<PERSON> Wise",
    address: "Bujumbura, Burundi",
    phone: "+257 XX XX XX XX",
    email: "<EMAIL>",
    currency: "BIF",
    tax_rate: 18
  },
  notifications: {
    email_enabled: true,
    sms_enabled: false,
    low_stock_alerts: true,
    daily_reports: true
  },
  printing: {
    auto_print_receipts: true,
    receipt_copies: 1,
    printer_name: "Imprimante par défaut"
  },
  system: {
    language: "fr",
    timezone: "Africa/Bujumbura",
    date_format: "DD/MM/YYYY",
    backup_frequency: "daily"
  }
};

/**
 * Service de gestion des paramètres avec fallback localStorage
 */
export class SettingsService {
  private static readonly STORAGE_KEY = 'barstock_settings';

  /**
   * Récupérer les paramètres (API ou localStorage)
   */
  static async getSettings(): Promise<SystemSettings> {
    try {
      // Essayer d'abord l'API
      const response = await apiClient.get('/settings/');
      console.log('✅ Paramètres récupérés depuis l\'API');
      return response.data;
    } catch (error) {
      console.warn('⚠️ API settings non disponible, utilisation du localStorage');
      
      // Fallback vers localStorage
      const stored = localStorage.getItem(this.STORAGE_KEY);
      if (stored) {
        try {
          return JSON.parse(stored);
        } catch (parseError) {
          console.warn('Erreur parsing localStorage, utilisation des défauts');
        }
      }
      
      // Sauvegarder les paramètres par défaut
      this.saveToLocalStorage(DEFAULT_SETTINGS);
      return DEFAULT_SETTINGS;
    }
  }

  /**
   * Sauvegarder les paramètres (API et localStorage)
   */
  static async saveSettings(settings: SystemSettings): Promise<void> {
    try {
      // Essayer d'abord l'API
      await apiClient.put('/settings/', settings);
      console.log('✅ Paramètres sauvegardés sur le serveur');
    } catch (error) {
      console.warn('⚠️ Impossible de sauvegarder sur le serveur, sauvegarde locale');
    }
    
    // Toujours sauvegarder localement comme backup
    this.saveToLocalStorage(settings);
  }

  /**
   * Sauvegarder dans localStorage
   */
  private static saveToLocalStorage(settings: SystemSettings): void {
    try {
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(settings));
      console.log('💾 Paramètres sauvegardés localement');
    } catch (error) {
      console.error('Erreur sauvegarde localStorage:', error);
    }
  }

  /**
   * Réinitialiser aux paramètres par défaut
   */
  static async resetToDefaults(): Promise<SystemSettings> {
    await this.saveSettings(DEFAULT_SETTINGS);
    return DEFAULT_SETTINGS;
  }

  /**
   * Vérifier la disponibilité de l'API
   */
  static async checkAPIAvailability(): Promise<boolean> {
    try {
      await apiClient.get('/settings/');
      return true;
    } catch (error) {
      return false;
    }
  }

  /**
   * Synchroniser localStorage vers serveur
   */
  static async syncToServer(): Promise<{ success: boolean; message: string }> {
    try {
      const localSettings = localStorage.getItem(this.STORAGE_KEY);
      if (!localSettings) {
        return {
          success: false,
          message: 'Aucun paramètre local à synchroniser'
        };
      }

      const settings = JSON.parse(localSettings);
      await apiClient.put('/settings/', settings);
      
      return {
        success: true,
        message: 'Paramètres synchronisés avec le serveur'
      };
    } catch (error) {
      return {
        success: false,
        message: `Erreur de synchronisation: ${error instanceof Error ? error.message : 'Erreur inconnue'}`
      };
    }
  }

  /**
   * Récupérer les informations système
   */
  static async getSystemInfo(): Promise<any> {
    try {
      const response = await apiClient.get('/settings/system-info/');
      return response.data;
    } catch (error) {
      // Informations par défaut si API non disponible
      return {
        version: "1.0.0",
        database: "PostgreSQL",
        server: "Django 4.2",
        uptime: this.calculateUptime(),
        last_backup: new Date().toISOString(),
        storage_used: "2.3 GB",
        memory_usage: "45%",
        cpu_usage: "12%"
      };
    }
  }

  /**
   * Calculer le temps de fonctionnement approximatif
   */
  private static calculateUptime(): string {
    const startTime = localStorage.getItem('app_start_time');
    if (!startTime) {
      const now = Date.now().toString();
      localStorage.setItem('app_start_time', now);
      return "Quelques secondes";
    }

    const elapsed = Date.now() - parseInt(startTime);
    const days = Math.floor(elapsed / (1000 * 60 * 60 * 24));
    const hours = Math.floor((elapsed % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    const minutes = Math.floor((elapsed % (1000 * 60 * 60)) / (1000 * 60));

    if (days > 0) {
      return `${days} jour${days > 1 ? 's' : ''}, ${hours} heure${hours > 1 ? 's' : ''}`;
    } else if (hours > 0) {
      return `${hours} heure${hours > 1 ? 's' : ''}, ${minutes} minute${minutes > 1 ? 's' : ''}`;
    } else {
      return `${minutes} minute${minutes > 1 ? 's' : ''}`;
    }
  }
}

export default SettingsService;
