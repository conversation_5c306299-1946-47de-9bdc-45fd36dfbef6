from rest_framework import serializers
from .models import Category, Product

class CategorySerializer(serializers.ModelSerializer):
    """
    Serializer pour les catégories de produits
    """
    
    type_display = serializers.CharField(source='get_type_display', read_only=True)
    products_count = serializers.SerializerMethodField()
    
    class Meta:
        model = Category
        fields = [
            'id', 'name', 'type', 'type_display', 'description',
            'is_active', 'products_count', 'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at']
    
    def get_products_count(self, obj):
        return obj.products.filter(is_active=True).count()


class ProductSerializer(serializers.ModelSerializer):
    """
    Serializer pour les produits
    """
    
    category_name = serializers.CharField(source='category.name', read_only=True)
    category_type = serializers.CharField(source='category.type', read_only=True)
    unit_display = serializers.CharField(source='get_unit_display', read_only=True)
    profit_margin = serializers.DecimalField(max_digits=10, decimal_places=2, read_only=True)
    profit_percentage = serializers.DecimalField(max_digits=5, decimal_places=2, read_only=True)
    is_low_stock = serializers.BooleanField(read_only=True)
    is_out_of_stock = serializers.BooleanField(read_only=True)
    
    class Meta:
        model = Product
        fields = [
            'id', 'name', 'category', 'category_name', 'category_type',
            'code', 'description', 'unit', 'unit_display',
            'purchase_price', 'selling_price', 'profit_margin', 'profit_percentage',
            'initial_stock', 'current_stock', 'minimum_stock',
            'is_low_stock', 'is_out_of_stock',
            'units_per_case', 'case_price',
            'is_active', 'is_available',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at', 'profit_margin', 'profit_percentage']
    
    def validate(self, data):
        # Validation des prix
        if data.get('selling_price') and data.get('purchase_price'):
            if data['selling_price'] <= data['purchase_price']:
                raise serializers.ValidationError(
                    "Le prix de vente doit être supérieur au prix d'achat."
                )
        
        # Validation du stock minimum
        if data.get('minimum_stock', 0) < 0:
            raise serializers.ValidationError(
                "Le stock minimum ne peut pas être négatif."
            )
        
        return data


class ProductListSerializer(serializers.ModelSerializer):
    """
    Serializer simplifié pour la liste des produits
    """
    
    category_name = serializers.CharField(source='category.name', read_only=True)
    unit_display = serializers.CharField(source='get_unit_display', read_only=True)
    is_low_stock = serializers.BooleanField(read_only=True)
    is_out_of_stock = serializers.BooleanField(read_only=True)
    
    class Meta:
        model = Product
        fields = [
            'id', 'name', 'category_name', 'code', 'unit_display',
            'selling_price', 'current_stock', 'minimum_stock',
            'is_low_stock', 'is_out_of_stock', 'is_available'
        ]


class ProductStockUpdateSerializer(serializers.Serializer):
    """
    Serializer pour mettre à jour le stock d'un produit
    """
    
    quantity = serializers.IntegerField(min_value=1)
    operation = serializers.ChoiceField(choices=['add', 'subtract', 'set'])
    reason = serializers.CharField(max_length=200, required=False)
    
    def validate_quantity(self, value):
        if value <= 0:
            raise serializers.ValidationError("La quantité doit être positive.")
        return value


class ProductBulkUpdateSerializer(serializers.Serializer):
    """
    Serializer pour les mises à jour en lot
    """
    
    products = serializers.ListField(
        child=serializers.DictField(),
        min_length=1
    )
    
    def validate_products(self, value):
        for item in value:
            if 'id' not in item:
                raise serializers.ValidationError("L'ID du produit est requis.")
            
            # Vérifier que le produit existe
            try:
                Product.objects.get(id=item['id'])
            except Product.DoesNotExist:
                raise serializers.ValidationError(f"Produit avec ID {item['id']} introuvable.")
        
        return value
