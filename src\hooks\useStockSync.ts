import { useState, useCallback } from 'react';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import {
  syncProductsToInventory,
  syncInventoryToProducts,
  fullSync,
  checkConsistency
} from '@/lib/syncFunctions';
import { useToast } from '@/hooks/use-toast';

interface SyncState {
  isLoading: boolean;
  lastSync: Date | null;
  error: string | null;
}

/**
 * Hook pour gérer la synchronisation des stocks
 */
export const useStockSync = () => {
  const [syncState, setSyncState] = useState<SyncState>({
    isLoading: false,
    lastSync: null,
    error: null
  });

  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Invalider les caches après synchronisation
  const invalidateStockCaches = useCallback(() => {
    queryClient.invalidateQueries({ queryKey: ['products'] });
    queryClient.invalidateQueries({ queryKey: ['inventory'] });
    queryClient.invalidateQueries({ queryKey: ['lowStockAlerts'] });
  }, [queryClient]);

  // Mutation pour synchroniser Products → Inventory
  const syncToInventoryMutation = useMutation({
    mutationFn: syncProductsToInventory,
    onMutate: () => {
      setSyncState(prev => ({ ...prev, isLoading: true, error: null }));
    },
    onSuccess: (result) => {
      setSyncState(prev => ({ 
        ...prev, 
        isLoading: false, 
        lastSync: new Date(),
        error: null 
      }));
      
      invalidateStockCaches();
      
      toast({
        title: result.success ? "Synchronisation réussie" : "Synchronisation partielle",
        description: result.message,
        variant: result.success ? "default" : "destructive",
      });

      if (result.errors.length > 0) {
        console.warn('Erreurs de synchronisation:', result.errors);
      }
    },
    onError: (error) => {
      const errorMessage = error instanceof Error ? error.message : 'Erreur inconnue';
      setSyncState(prev => ({ 
        ...prev, 
        isLoading: false, 
        error: errorMessage 
      }));
      
      toast({
        title: "Erreur de synchronisation",
        description: errorMessage,
        variant: "destructive",
      });
    }
  });

  // Mutation pour synchroniser Inventory → Products
  const syncToProductsMutation = useMutation({
    mutationFn: syncInventoryToProducts,
    onMutate: () => {
      setSyncState(prev => ({ ...prev, isLoading: true, error: null }));
    },
    onSuccess: (result) => {
      setSyncState(prev => ({ 
        ...prev, 
        isLoading: false, 
        lastSync: new Date(),
        error: null 
      }));
      
      invalidateStockCaches();
      
      toast({
        title: result.success ? "Synchronisation réussie" : "Synchronisation partielle",
        description: result.message,
        variant: result.success ? "default" : "destructive",
      });
    },
    onError: (error) => {
      const errorMessage = error instanceof Error ? error.message : 'Erreur inconnue';
      setSyncState(prev => ({ 
        ...prev, 
        isLoading: false, 
        error: errorMessage 
      }));
      
      toast({
        title: "Erreur de synchronisation",
        description: errorMessage,
        variant: "destructive",
      });
    }
  });

  // Mutation pour synchronisation complète
  const fullSyncMutation = useMutation({
    mutationFn: fullSync,
    onMutate: () => {
      setSyncState(prev => ({ ...prev, isLoading: true, error: null }));
    },
    onSuccess: (result) => {
      setSyncState(prev => ({ 
        ...prev, 
        isLoading: false, 
        lastSync: new Date(),
        error: null 
      }));
      
      invalidateStockCaches();
      
      toast({
        title: result.success ? "Synchronisation complète réussie" : "Synchronisation complète partielle",
        description: result.message,
        variant: result.success ? "default" : "destructive",
      });
    },
    onError: (error) => {
      const errorMessage = error instanceof Error ? error.message : 'Erreur inconnue';
      setSyncState(prev => ({ 
        ...prev, 
        isLoading: false, 
        error: errorMessage 
      }));
      
      toast({
        title: "Erreur de synchronisation complète",
        description: errorMessage,
        variant: "destructive",
      });
    }
  });

  // Vérification de cohérence
  const checkConsistencyFn = useCallback(async () => {
    try {
      const result = await checkConsistency();
      
      toast({
        title: result.consistent ? "Données cohérentes" : "Incohérences détectées",
        description: result.consistent 
          ? `${result.summary.productsCount} produits et ${result.summary.inventoryCount} items d'inventaire synchronisés`
          : `${result.issues.join(', ')}`,
        variant: result.consistent ? "default" : "destructive",
      });

      return result;
    } catch (error) {
      toast({
        title: "Erreur de vérification",
        description: error instanceof Error ? error.message : 'Erreur inconnue',
        variant: "destructive",
      });
      throw error;
    }
  }, [toast]);

  // Synchronisation automatique au démarrage
  const autoSync = useCallback(async () => {
    try {
      // Vérifier d'abord la cohérence
      const consistency = await checkConsistency();
      
      if (!consistency.consistent) {
        console.log('🔄 Incohérences détectées, synchronisation automatique...');
        await fullSyncMutation.mutateAsync();
      } else {
        console.log('✅ Données déjà cohérentes');
      }
    } catch (error) {
      console.error('❌ Erreur lors de la synchronisation automatique:', error);
    }
  }, [fullSyncMutation]);

  return {
    // État
    syncState,
    isLoading: syncState.isLoading || 
               syncToInventoryMutation.isPending || 
               syncToProductsMutation.isPending || 
               fullSyncMutation.isPending,

    // Actions
    syncToInventory: syncToInventoryMutation.mutate,
    syncToProducts: syncToProductsMutation.mutate,
    fullSync: fullSyncMutation.mutate,
    checkConsistency: checkConsistencyFn,
    autoSync,

    // Utilitaires
    invalidateStockCaches
  };
};

export default useStockSync;
