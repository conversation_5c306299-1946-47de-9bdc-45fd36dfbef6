import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { 
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { 
  Plus, 
  Edit, 
  Trash2,
  Users,
  Clock,
  CheckCircle,
  AlertCircle,
  Coffee
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { useTables, useCreateTable, useUpdateTable, useDeleteTable } from '@/hooks/useApi';
import { formatCurrency } from '@/lib/currency';

// Types
interface Table {
  id: string;
  number: number;
  capacity: number;
  status: 'Libre' | 'Occupée' | 'Réservée' | 'En nettoyage';
  currentOrder?: {
    id: string;
    items: number;
    total: number;
    startTime: string;
  };
  location: string;
  notes?: string;
}

// Les données des tables sont maintenant récupérées dynamiquement via l'API

const Tables = () => {
  const { toast } = useToast();

  // Fonction pour voir les détails d'une commande
  const handleViewOrder = (orderId: string) => {
    // Rediriger vers la page des factures avec l'ID de la commande
    window.open(`/invoices?order=${orderId}`, '_blank');
  };

  // Hooks pour les données API
  const { data: tablesData, isLoading: tablesLoading, error: tablesError } = useTables();
  const createTable = useCreateTable();
  const updateTable = useUpdateTable();
  const deleteTable = useDeleteTable();

  // Tables dynamiques
  const tables = React.useMemo(() => {
    if (!tablesData) return [];

    // Gérer différents formats de réponse de l'API
    let tablesList = [];
    if (Array.isArray(tablesData)) {
      tablesList = tablesData;
    } else if (tablesData.results && Array.isArray(tablesData.results)) {
      tablesList = tablesData.results;
    }

    return tablesList.map((table: any) => ({
      id: table.id.toString(),
      number: table.number,
      capacity: table.capacity,
      status: table.status === 'available' ? 'Libre' :
              table.status === 'occupied' ? 'Occupée' :
              table.status === 'reserved' ? 'Réservée' :
              table.status === 'cleaning' ? 'En nettoyage' : 'Libre',
      location: table.location || 'Intérieur',
      notes: table.notes || '',
      // Ajouter currentOrder si disponible
      currentOrder: table.current_sale ? {
        id: table.current_sale.reference || table.current_sale.id,
        items: table.current_sale.items?.length || 0,
        total: table.current_sale.total_amount || 0,
        startTime: table.current_sale.created_at ? new Date(table.current_sale.created_at).toLocaleTimeString('fr-FR', { hour: '2-digit', minute: '2-digit' }) : ''
      } : null,
      // Propriétés supplémentaires du backend
      is_occupied: table.is_occupied,
      is_available: table.is_available,
      occupation_duration: table.occupation_duration
    }));
  }, [tablesData]);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editingTable, setEditingTable] = useState<Table | null>(null);
  
  const [formData, setFormData] = useState({
    number: '',
    capacity: '',
    location: 'Intérieur',
    notes: ''
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      if (editingTable) {
        // Modification via API
        await updateTable.mutateAsync({
          id: editingTable.id,
          data: {
            number: parseInt(formData.number),
            capacity: parseInt(formData.capacity),
            location: formData.location,
            notes: formData.notes || undefined
          }
        });
        toast({
          title: "Table modifiée",
          description: "Les informations de la table ont été mises à jour.",
        });
      } else {
        // Ajout via API
        await createTable.mutateAsync({
          number: parseInt(formData.number),
          capacity: parseInt(formData.capacity),
          status: 'available', // Utiliser le statut backend
          location: formData.location,
          notes: formData.notes || undefined
        });
        toast({
          title: "Table ajoutée",
          description: "La nouvelle table a été créée avec succès.",
        });
      }
    } catch (error) {
      toast({
        title: "Erreur",
        description: "Une erreur est survenue lors de l'opération.",
        variant: "destructive",
      });
    }
    
    resetForm();
  };

  const resetForm = () => {
    setFormData({
      number: '',
      capacity: '',
      location: 'Intérieur',
      notes: ''
    });
    setEditingTable(null);
    setIsDialogOpen(false);
  };

  const handleEdit = (table: Table) => {
    setFormData({
      number: table.number.toString(),
      capacity: table.capacity.toString(),
      location: table.location,
      notes: table.notes || ''
    });
    setEditingTable(table);
    setIsDialogOpen(true);
  };

  const handleDelete = async (tableId: string) => {
    const table = tables.find(t => t.id === tableId);
    if (table?.status === 'Occupée') {
      toast({
        title: "Impossible de supprimer",
        description: "Cette table est actuellement occupée.",
        variant: "destructive",
      });
      return;
    }

    try {
      await deleteTable.mutateAsync(tableId);
      toast({
        title: "Table supprimée",
        description: "La table a été retirée de la liste.",
        variant: "destructive",
      });
    } catch (error) {
      toast({
        title: "Erreur",
        description: "Impossible de supprimer la table.",
        variant: "destructive",
      });
    }
  };

  // Fonction pour convertir les statuts français vers anglais
  const convertStatusToBackend = (status: Table['status']) => {
    switch (status) {
      case 'Libre': return 'available';
      case 'Occupée': return 'occupied';
      case 'Réservée': return 'reserved';
      case 'En nettoyage': return 'cleaning';
      default: return 'available';
    }
  };

  const changeTableStatus = async (tableId: string, newStatus: Table['status']) => {
    try {
      const table = tables.find(t => t.id === tableId);
      const backendStatus = convertStatusToBackend(newStatus);

      await updateTable.mutateAsync({
        id: tableId,
        data: {
          status: backendStatus,
          // Conserver les autres propriétés de la table
          number: table?.number,
          capacity: table?.capacity,
          location: table?.location,
          notes: table?.notes
        }
      });

      toast({
        title: "Statut mis à jour",
        description: `La table ${table?.number} est maintenant ${newStatus.toLowerCase()}.`,
      });
    } catch (error) {
      console.error('Erreur lors de la mise à jour du statut:', error);
      toast({
        title: "Erreur",
        description: "Impossible de modifier le statut de la table.",
        variant: "destructive",
      });
    }
  };

  const getStatusBadge = (status: Table['status']) => {
    const variants = {
      'Libre': { variant: 'default' as const, icon: CheckCircle },
      'Occupée': { variant: 'destructive' as const, icon: Users },
      'Réservée': { variant: 'secondary' as const, icon: Clock },
      'En nettoyage': { variant: 'outline' as const, icon: AlertCircle }
    };
    
    const { variant, icon: Icon } = variants[status];
    
    return (
      <Badge variant={variant} className="flex items-center gap-1 w-fit">
        <Icon className="w-3 h-3" />
        {status}
      </Badge>
    );
  };

  const getStatusColor = (status: Table['status']) => {
    const colors = {
      'Libre': 'bg-green-100 border-green-300 hover:bg-green-50',
      'Occupée': 'bg-red-100 border-red-300 hover:bg-red-50',
      'Réservée': 'bg-yellow-100 border-yellow-300 hover:bg-yellow-50',
      'En nettoyage': 'bg-gray-100 border-gray-300 hover:bg-gray-50'
    };
    return colors[status];
  };

  const stats = {
    total: tables.length,
    libre: tables.filter(t => t.status === 'Libre').length,
    occupee: tables.filter(t => t.status === 'Occupée').length,
    reservee: tables.filter(t => t.status === 'Réservée').length,
    totalCapacity: tables.reduce((sum, t) => sum + t.capacity, 0),
    currentRevenue: tables
      .filter(t => t.currentOrder)
      .reduce((sum, t) => sum + (t.currentOrder?.total || 0), 0)
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Gestion des Tables</h1>
          <p className="text-muted-foreground">
            Gérez l'occupation et l'état de vos tables
          </p>
        </div>
        
        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogTrigger asChild>
            <Button onClick={() => resetForm()}>
              <Plus className="w-4 h-4 mr-2" />
              Nouvelle Table
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-md">
            <DialogHeader>
              <DialogTitle>
                {editingTable ? 'Modifier la table' : 'Nouvelle table'}
              </DialogTitle>
              <DialogDescription>
                {editingTable 
                  ? 'Modifiez les informations de la table'
                  : 'Ajoutez une nouvelle table à votre restaurant'
                }
              </DialogDescription>
            </DialogHeader>
            
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="number">Numéro</Label>
                  <Input
                    id="number"
                    type="number"
                    value={formData.number}
                    onChange={(e) => setFormData({...formData, number: e.target.value})}
                    placeholder="1"
                    required
                    min="1"
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="capacity">Capacité</Label>
                  <Input
                    id="capacity"
                    type="number"
                    value={formData.capacity}
                    onChange={(e) => setFormData({...formData, capacity: e.target.value})}
                    placeholder="4"
                    required
                    min="1"
                  />
                </div>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="location">Emplacement</Label>
                <Select 
                  value={formData.location} 
                  onValueChange={(value) => setFormData({...formData, location: value})}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Intérieur">Intérieur</SelectItem>
                    <SelectItem value="Terrasse">Terrasse</SelectItem>
                    <SelectItem value="Salle principale">Salle principale</SelectItem>
                    <SelectItem value="Salle VIP">Salle VIP</SelectItem>
                    <SelectItem value="Bar">Bar</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="notes">Notes (optionnel)</Label>
                <Input
                  id="notes"
                  value={formData.notes}
                  onChange={(e) => setFormData({...formData, notes: e.target.value})}
                  placeholder="Informations supplémentaires..."
                />
              </div>
              
              <div className="flex gap-2 pt-4">
                <Button type="submit" className="flex-1">
                  {editingTable ? 'Modifier' : 'Ajouter'}
                </Button>
                <Button type="button" variant="outline" onClick={resetForm}>
                  Annuler
                </Button>
              </div>
            </form>
          </DialogContent>
        </Dialog>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-5">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Tables</CardTitle>
            <Coffee className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.total}</div>
            <p className="text-xs text-muted-foreground">
              {stats.totalCapacity} places au total
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Tables Libres</CardTitle>
            <CheckCircle className="h-4 w-4 text-success" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-success">{stats.libre}</div>
            <p className="text-xs text-muted-foreground">disponibles</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Tables Occupées</CardTitle>
            <Users className="h-4 w-4 text-destructive" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-destructive">{stats.occupee}</div>
            <p className="text-xs text-muted-foreground">en service</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Réservations</CardTitle>
            <Clock className="h-4 w-4 text-warning" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-warning">{stats.reservee}</div>
            <p className="text-xs text-muted-foreground">à venir</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">CA en Cours</CardTitle>
            <Coffee className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(stats.currentRevenue)}</div>
            <p className="text-xs text-muted-foreground">commandes actives</p>
          </CardContent>
        </Card>
      </div>

      {/* Tables Grid */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
        {tables.map((table) => (
          <Card key={table.id} className={`cursor-pointer transition-colors ${getStatusColor(table.status)}`}>
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <CardTitle className="text-lg">Table {table.number}</CardTitle>
                {getStatusBadge(table.status)}
              </div>
              <CardDescription className="flex items-center gap-2">
                <Users className="w-4 h-4" />
                {table.capacity} places • {table.location}
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-3">
              {table.currentOrder && (
                <div
                  className="p-3 bg-background/50 rounded-lg cursor-pointer hover:bg-background/70 transition-colors"
                  onClick={() => handleViewOrder(table.currentOrder!.id)}
                  title="Cliquer pour voir les détails de la commande"
                >
                  <div className="flex justify-between items-center mb-1">
                    <span className="text-sm font-medium">Commande #{table.currentOrder.id}</span>
                    <span className="text-xs text-muted-foreground">{table.currentOrder.startTime}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm">{table.currentOrder.items} articles</span>
                    <span className="font-bold">{formatCurrency(table.currentOrder.total)}</span>
                  </div>
                  <div className="text-xs text-blue-600 mt-1">
                    👁️ Cliquer pour voir la facture
                  </div>
                </div>
              )}
              
              {table.notes && (
                <div className="text-sm text-muted-foreground bg-background/50 p-2 rounded">
                  {table.notes}
                </div>
              )}
              
              <div className="flex gap-2">
                <Select 
                  value={table.status} 
                  onValueChange={(value: Table['status']) => changeTableStatus(table.id, value)}
                >
                  <SelectTrigger className="flex-1">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Libre">Libre</SelectItem>
                    <SelectItem value="Occupée">Occupée</SelectItem>
                    <SelectItem value="Réservée">Réservée</SelectItem>
                    <SelectItem value="En nettoyage">En nettoyage</SelectItem>
                  </SelectContent>
                </Select>
                
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleEdit(table)}
                >
                  <Edit className="w-4 h-4" />
                </Button>
                
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleDelete(table.id)}
                  disabled={table.status === 'Occupée'}
                >
                  <Trash2 className="w-4 h-4" />
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
};

export default Tables;
