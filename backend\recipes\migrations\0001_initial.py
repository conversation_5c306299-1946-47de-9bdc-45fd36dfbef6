# Generated by Django 5.2.4 on 2025-07-31 13:28

import django.core.validators
import django.db.models.deletion
from decimal import Decimal
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('products', '0002_product_shelf_life_days_product_storage_temperature_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Recipe',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200, verbose_name='Nom de la recette')),
                ('description', models.TextField(blank=True, null=True, verbose_name='Description')),
                ('portions_per_recipe', models.PositiveIntegerField(default=1, verbose_name='Portions par recette')),
                ('preparation_time', models.PositiveIntegerField(help_text='Temps en minutes', verbose_name='Temps de préparation')),
                ('cooking_time', models.PositiveIntegerField(help_text='Temps en minutes', verbose_name='Temps de cuisson')),
                ('difficulty', models.CharField(choices=[('easy', 'Facile'), ('medium', 'Moyen'), ('hard', 'Difficile')], default='medium', max_length=10, verbose_name='Difficulté')),
                ('waste_factor', models.DecimalField(decimal_places=2, default=Decimal('1.10'), help_text='Facteur de perte (1.10 = 10% de perte)', max_digits=4, validators=[django.core.validators.MinValueValidator(Decimal('1.00'))], verbose_name='Facteur de perte')),
                ('is_active', models.BooleanField(default=True, verbose_name='Recette active')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Date de création')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Date de modification')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='Créé par')),
                ('dish', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='recipe', to='products.product', verbose_name='Plat')),
            ],
            options={
                'verbose_name': 'Recette',
                'verbose_name_plural': 'Recettes',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='RecipeProduction',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('portions_produced', models.PositiveIntegerField(verbose_name='Portions produites')),
                ('actual_waste', models.DecimalField(decimal_places=2, default=Decimal('0.00'), help_text='Perte réelle en pourcentage', max_digits=5, verbose_name='Perte réelle (%)')),
                ('production_cost', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=10, verbose_name='Coût de production')),
                ('production_date', models.DateTimeField(auto_now_add=True, verbose_name='Date de production')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='Notes de production')),
                ('produced_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='Produit par')),
                ('recipe', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='productions', to='recipes.recipe', verbose_name='Recette')),
            ],
            options={
                'verbose_name': 'Production de recette',
                'verbose_name_plural': 'Productions de recettes',
                'ordering': ['-production_date'],
            },
        ),
        migrations.CreateModel(
            name='RecipeIngredient',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('quantity_needed', models.DecimalField(decimal_places=3, max_digits=8, validators=[django.core.validators.MinValueValidator(Decimal('0.001'))], verbose_name='Quantité nécessaire')),
                ('unit', models.CharField(max_length=20, verbose_name='Unité')),
                ('waste_factor', models.DecimalField(decimal_places=2, default=Decimal('1.05'), help_text='Facteur de perte pour cet ingrédient', max_digits=4, validators=[django.core.validators.MinValueValidator(Decimal('1.00'))], verbose_name='Facteur de perte')),
                ('is_optional', models.BooleanField(default=False, verbose_name='Ingrédient optionnel')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='Notes de préparation')),
                ('order', models.PositiveIntegerField(default=0, verbose_name='Ordre dans la recette')),
                ('ingredient', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='products.product', verbose_name='Ingrédient')),
                ('recipe', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='ingredients', to='recipes.recipe', verbose_name='Recette')),
            ],
            options={
                'verbose_name': 'Ingrédient de recette',
                'verbose_name_plural': 'Ingrédients de recette',
                'ordering': ['order', 'ingredient__name'],
                'unique_together': {('recipe', 'ingredient')},
            },
        ),
    ]
