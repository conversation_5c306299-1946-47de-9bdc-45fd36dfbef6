import { useState, useEffect, ChangeEvent, useMemo } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import { 
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { 
  FileText, 
  Download, 
  AlertTriangle, 
  CheckCircle, 
  Calculator,
  Save,
  Upload,
  Eye
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { formatCurrency } from '@/lib/currency';
import { useAuth } from '@/contexts/AuthContextBackend';
import { usePermissions, ProtectedComponent } from '@/contexts/PermissionsContext';
import { DailyStockItem, DailyReport as DailyReportType, StockAlert } from '@/types/dailyReport';
import { DailyReportCalculator } from '@/lib/dailyReportCalculations';
import { exportDailyReportToPDF, DailyReportPDFData } from '@/lib/pdfExport';
import { createEmptyDailyReport, validateImportData } from '@/lib/reportTemplates';
import ProductSelector from '@/components/reports/ProductSelector';
import EditableCell from '@/components/reports/EditableCell';
import AIRecommendations from '@/components/reports/AIRecommendations';
import { useDailyReportAutoSave } from '@/hooks/useAutoSave';
import { useNotifications } from '@/lib/notifications';
import { aiPredictor } from '@/lib/aiPredictions';
import {
  useDailyReports,
  useCreateDailyReport,
  useUpdateDailyReport,
  useDailySummary,
  useSales,
  useExpenses,
  useProducts
} from '@/hooks/useApi';
import { Skeleton } from '@/components/ui/skeleton';
import { parseNumericValue } from '@/lib/utils/numeric';

const DailyReport = () => {
  // Hooks pour les données API
  const { data: dailyReportsResponse, isLoading: dailyReportsLoading, error: dailyReportsError } = useDailyReports();
  const { data: productsResponse, isLoading: productsLoading } = useProducts();
  const { data: salesResponse, isLoading: salesLoading } = useSales();
  const createDailyReport = useCreateDailyReport();
  const updateDailyReport = useUpdateDailyReport();

  // Extraire les données de la réponse paginée
  const dailyReports = useMemo(() => {
    if (!dailyReportsResponse) return [];
    return dailyReportsResponse?.results || (Array.isArray(dailyReportsResponse) ? dailyReportsResponse : []);
  }, [dailyReportsResponse]);

  const products = useMemo(() => {
    if (!productsResponse) return [];
    return productsResponse?.results || (Array.isArray(productsResponse) ? productsResponse : []);
  }, [productsResponse]);

  // Générer les données de rapport journalier à partir des produits API
  const generateDailyDataFromProducts = useMemo(() => {
    if (!products || products.length === 0) return [];

    return products.map((product: any) => ({
      id: product.id,
      produit: product.name,
      category: product.category?.name || 'Non définie',
      prixCasier: parseNumericValue(product.case_price),
      stockInitial: parseNumericValue(product.initial_stock),
      stockEntrant: 0, // À saisir par l'utilisateur
      stockTotal: parseNumericValue(product.current_stock),
      consommation: 0, // À calculer
      perte: 0, // À saisir par l'utilisateur
      stockRestant: parseNumericValue(product.current_stock),
      prixAchatUnitaire: parseNumericValue(product.purchase_price),
      prixVenteUnitaire: parseNumericValue(product.selling_price),
      stockConsomme: 0, // À calculer
      stockVendu: 0, // À saisir par l'utilisateur
      marge: parseNumericValue(product.selling_price) - parseNumericValue(product.purchase_price),
      benefice: 0 // À calculer
    }));
  }, [products]);

  // État local
  const { toast } = useToast();
  const { canManageReports, canExportReports } = usePermissions();
  const [reportData, setReportData] = useState<DailyStockItem[]>([]);
  const [selectedDate, setSelectedDate] = useState(new Date().toISOString().split('T')[0]);
  const [alerts, setAlerts] = useState<StockAlert[]>([]);
  const [isCalculating, setIsCalculating] = useState(false);
  const [showValidationDialog, setShowValidationDialog] = useState(false);

  // Initialiser les données de rapport à partir des produits API
  useEffect(() => {
    if (generateDailyDataFromProducts.length > 0 && reportData.length === 0) {
      setReportData(generateDailyDataFromProducts);
    }
  }, [generateDailyDataFromProducts, reportData.length]);

  // État de chargement global
  const isLoading = dailyReportsLoading || productsLoading || salesLoading;

  const calculator = new DailyReportCalculator();

  // Sauvegarde automatique
  const { forceSave, loadReport, hasUnsavedChanges, isSaving } = useDailyReportAutoSave(reportData, selectedDate);

  // Notifications
  const {
    requestPermission,
    notifyStockAlert,
    notifyStockOut,
    notifyCalculationError,
    notifyDailyReportReady
  } = useNotifications();

  // Calcul automatique des totaux
  const calculateTotals = () => {
    const bieresTotal = calculator.calculateCategoryTotals(reportData, 'Bières Brarudi');
    const liqueursTotal = calculator.calculateCategoryTotals(reportData, 'Liqueurs');
    
    return {
      recetteBieres: bieresTotal.recette,
      recetteLiqueurs: liqueursTotal.recette,
      recetteTotal: bieresTotal.recette + liqueursTotal.recette,
      beneficeTotal: bieresTotal.benefice + liqueursTotal.benefice,
      stockVenduTotal: bieresTotal.stockVenduTotal + liqueursTotal.stockVenduTotal
    };
  };

  // Validation et correction automatique
  const validateAndCorrectData = async () => {
    setIsCalculating(true);
    const allAlerts: StockAlert[] = [];
    const correctedData: DailyStockItem[] = [];

    // Demander la permission pour les notifications si pas encore fait
    await requestPermission();

    reportData.forEach(item => {
      const { correctedItem, alerts: itemAlerts } = calculator.validateAndCorrectItem(item);
      const stockAlerts = calculator.generateStockAlerts(correctedItem);

      correctedData.push(correctedItem);
      allAlerts.push(...itemAlerts, ...stockAlerts);

      // Envoyer des notifications pour les alertes critiques
      stockAlerts.forEach(alert => {
        if (alert.type === 'stock_out') {
          notifyStockOut(item.produit);
        } else if (alert.type === 'stock_low') {
          notifyStockAlert(item.produit, correctedItem.stockRestant, 10);
        }
      });

      // Notifications pour les erreurs de calcul
      itemAlerts.forEach(alert => {
        if (alert.type === 'calculation_error') {
          notifyCalculationError(item.produit, alert.message);
        }
      });
    });

    setReportData(correctedData);
    setAlerts(allAlerts);
    setIsCalculating(false);

    toast({
      title: "Validation terminée",
      description: `${allAlerts.length} alertes détectées. Données corrigées automatiquement.`,
    });
  };

  // Mise à jour d'un item
  const updateItem = (id: string, field: keyof DailyStockItem, value: number) => {
    setReportData(prev => prev.map(item => 
      item.id === id ? { ...item, [field]: value } : item
    ));
  };

  // Import depuis Excel/CSV
  const handleFileImport = (event: ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const content = e.target?.result as string;

        // Parse CSV simple (pour l'exemple)
        const lines = content.split('\n');
        const headers = lines[0].split(',').map(h => h.trim());

        const importedData = lines.slice(1)
          .filter(line => line.trim())
          .map((line, index) => {
            const values = line.split(',');
            const row: any = {};
            headers.forEach((header, i) => {
              const value = values[i]?.trim();
              if (header.includes('stock') || header.includes('prix') || header.includes('consommation')) {
                row[header] = parseFloat(value) || 0;
              } else {
                row[header] = value;
              }
            });
            return row;
          });

        // Validation des données
        const validation = validateImportData(importedData);

        if (!validation.valid) {
          toast({
            title: "Erreur d'import",
            description: `${validation.errors.length} erreur(s) détectée(s): ${validation.errors[0]}`,
            variant: "destructive",
          });
          return;
        }

        // Conversion vers notre format
        const convertedData: DailyStockItem[] = importedData.map((row, index) => ({
          id: `imported-${index}`,
          produit: row.produit || `Produit ${index + 1}`,
          category: row.category || 'Autres Boissons',
          prixCasier: row.prixCasier || 0,
          stockInitial: row.stockInitial || 0,
          stockEntrant: row.stockEntrant || 0,
          stockTotal: (row.stockInitial || 0) + (row.stockEntrant || 0),
          consommation: row.consommation || 0,
          perte: row.perte || 0,
          stockRestant: row.stockRestant || 0,
          prixAchatUnitaire: row.prixAchatUnitaire || 0,
          prixVenteUnitaire: row.prixVenteUnitaire || 0,
          stockConsomme: row.stockConsomme || row.consommation || 0,
          stockVendu: row.stockVendu || 0,
          marge: row.marge || 0,
          benefice: row.benefice || 0
        }));

        setReportData(convertedData);

        toast({
          title: "Import réussi",
          description: `${convertedData.length} produit(s) importé(s) depuis ${file.name}`,
        });

        if (validation.warnings.length > 0) {
          toast({
            title: "Avertissements",
            description: `${validation.warnings.length} avertissement(s). Vérifiez les données.`,
          });
        }

      } catch (error) {
        toast({
          title: "Erreur d'import",
          description: "Impossible de lire le fichier. Vérifiez le format CSV.",
          variant: "destructive",
        });
      }
    };
    reader.readAsText(file);
  };

  // Export du rapport
  const exportReport = () => {
    const totals = calculateTotals();
    const { user } = useAuth();

    const pdfData: DailyReportPDFData = {
      date: selectedDate,
      generatedBy: user?.username || `${user?.first_name || ''} ${user?.last_name || ''}`.trim() || 'Utilisateur',
      items: reportData.map(item => ({
        produit: item.produit,
        category: item.category,
        stockInitial: item.stockInitial,
        stockEntrant: item.stockEntrant,
        stockTotal: item.stockTotal,
        consommation: item.consommation,
        perte: item.perte,
        stockRestant: item.stockRestant,
        prixVenteUnitaire: item.prixVenteUnitaire,
        stockVendu: item.stockVendu,
        benefice: item.benefice,
        marge: item.marge
      })),
      totals: {
        recetteBieres: totals.recetteBieres,
        recetteLiqueurs: totals.recetteLiqueurs,
        recetteTotal: totals.recetteTotal,
        beneficeTotal: totals.beneficeTotal
      },
      alerts: alerts.map(alert => ({
        productName: alert.productName,
        alertType: alert.alertType,
        message: alert.message,
        severity: alert.severity
      }))
    };

    try {
      exportDailyReportToPDF(pdfData);
      toast({
        title: "Export réussi",
        description: "Le rapport journalier a été exporté en PDF.",
      });
    } catch (error) {
      toast({
        title: "Erreur d'export",
        description: "Impossible d'exporter le rapport en PDF.",
        variant: "destructive",
      });
    }
  };

  // Sauvegarde du rapport
  const saveReport = () => {
    forceSave();
    toast({
      title: "Rapport sauvegardé",
      description: `Rapport du ${selectedDate} sauvegardé avec succès.`,
    });
  };

  // Charger un rapport existant quand la date change
  useEffect(() => {
    const existingReport = loadReport(selectedDate);
    if (existingReport && existingReport.length > 0) {
      setReportData(existingReport);
      toast({
        title: "Rapport chargé",
        description: `Rapport existant du ${selectedDate} chargé.`,
      });
    } else {
      // Créer un nouveau rapport avec les données des produits API
      setReportData(generateDailyDataFromProducts);
    }
  }, [selectedDate, loadReport]);

  // Demander la permission pour les notifications au démarrage
  useEffect(() => {
    const initNotifications = async () => {
      const granted = await requestPermission();
      if (granted) {
        toast({
          title: "Notifications activées",
          description: "Vous recevrez des alertes pour les stocks faibles et ruptures.",
        });
      }
    };

    initNotifications();
  }, [requestPermission]);

  // Nouveau rapport avec sélecteur de produits
  const handleNewReport = () => {
    const emptyReport = createEmptyDailyReport(selectedDate);
    setReportData(emptyReport);
    setAlerts([]);
    toast({
      title: "Nouveau rapport créé",
      description: `Rapport vide créé pour le ${selectedDate}`,
    });
  };

  // Gestion de la sélection de produits
  const handleProductsSelected = (newItems: DailyStockItem[]) => {
    setReportData(newItems);
    toast({
      title: "Produits sélectionnés",
      description: `${newItems.length} produit(s) ajouté(s) au rapport`,
    });
  };

  const totals = calculateTotals();

  return (
    <div className="space-y-6">
      {/* En-tête */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">Rapport Journalier Boissons</h1>
          <p className="text-muted-foreground">
            Gestion et suivi des stocks quotidiens
          </p>
          {/* Loading State */}
          {isLoading && (
            <div className="flex items-center space-x-2 mt-2">
              <Skeleton className="h-4 w-4 rounded-full" />
              <span className="text-sm text-muted-foreground">Chargement des données...</span>
            </div>
          )}
        </div>
        
        <div className="flex flex-wrap gap-2">
          <Input
            type="date"
            value={selectedDate}
            onChange={(e) => setSelectedDate(e.target.value)}
            className="w-40"
          />

          <ProtectedComponent permission="reports.create">
            <ProductSelector
              onProductsSelected={handleProductsSelected}
              selectedProducts={reportData.map(item => item.id)}
            />
          </ProtectedComponent>

          <ProtectedComponent permission="reports.create">
            <div className="relative">
              <input
                type="file"
                accept=".xlsx,.xls,.csv"
                onChange={handleFileImport}
                className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
              />
              <Button variant="outline">
                <Upload className="w-4 h-4 mr-2" />
                Importer CSV
              </Button>
            </div>
          </ProtectedComponent>

          <ProtectedComponent permission="reports.validate">
            <Button
              onClick={validateAndCorrectData}
              disabled={isCalculating}
              variant="outline"
            >
              <Calculator className="w-4 h-4 mr-2" />
              {isCalculating ? 'Calcul...' : 'Valider & Corriger'}
            </Button>
          </ProtectedComponent>

          <ProtectedComponent permission="reports.create">
            <Button onClick={saveReport} variant="outline" disabled={isSaving}>
              <Save className="w-4 h-4 mr-2" />
              {isSaving ? 'Sauvegarde...' : 'Sauvegarder'}
            </Button>
          </ProtectedComponent>

          {/* Indicateur de statut */}
          <div className="flex items-center text-sm text-muted-foreground">
            {isSaving ? (
              <span className="text-blue-600">💾 Sauvegarde en cours...</span>
            ) : hasUnsavedChanges() ? (
              <span className="text-orange-600">⚠️ Modifications non sauvegardées</span>
            ) : (
              <span className="text-green-600">✅ Tout est sauvegardé</span>
            )}
          </div>

          <ProtectedComponent permission="reports.export">
            <Button onClick={exportReport}>
              <Download className="w-4 h-4 mr-2" />
              Export PDF
            </Button>
          </ProtectedComponent>
        </div>
      </div>

      {/* Alertes */}
      {alerts.length > 0 && (
        <Alert>
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            <strong>{alerts.length} alertes détectées</strong>
            <div className="mt-2 space-y-1">
              {alerts.slice(0, 3).map(alert => (
                <div key={alert.id} className="text-sm">
                  <Badge variant={alert.severity === 'Critique' ? 'destructive' : 'secondary'}>
                    {alert.alertType}
                  </Badge>
                  <span className="ml-2">{alert.productName}: {alert.message}</span>
                </div>
              ))}
              {alerts.length > 3 && (
                <div className="text-sm text-muted-foreground">
                  ... et {alerts.length - 3} autres alertes
                </div>
              )}
            </div>
          </AlertDescription>
        </Alert>
      )}

      {/* Résumé */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Recette Bières</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(totals.recetteBieres)}</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Recette Liqueurs</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(totals.recetteLiqueurs)}</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Recette Total</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(totals.recetteTotal)}</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Bénéfice Total</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              {formatCurrency(totals.beneficeTotal)}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Tableau des données */}
      <Tabs defaultValue="bieres" className="space-y-4">
        <TabsList>
          <TabsTrigger value="bieres">Bières Brarudi</TabsTrigger>
          <TabsTrigger value="liqueurs">Liqueurs</TabsTrigger>
          <TabsTrigger value="tous">Tous les produits</TabsTrigger>
        </TabsList>

        <TabsContent value="bieres" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Bières Brarudi</CardTitle>
              <CardDescription>
                Suivi détaillé des stocks de bières
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Produit</TableHead>
                      <TableHead>Stock Initial</TableHead>
                      <TableHead>Stock Entrant</TableHead>
                      <TableHead>Stock Total</TableHead>
                      <TableHead>Consommation</TableHead>
                      <TableHead>Stock Restant</TableHead>
                      <TableHead>P.V.U</TableHead>
                      <TableHead>Stock Vendu</TableHead>
                      <TableHead>Bénéfice</TableHead>
                      <TableHead>Statut</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {reportData
                      .filter(item => item.category === 'Bières Brarudi')
                      .map(item => (
                        <TableRow key={item.id}>
                          <TableCell className="font-medium">{item.produit}</TableCell>
                          <TableCell>
                            <EditableCell
                              value={item.stockInitial}
                              onSave={(newValue) => updateItem(item.id, 'stockInitial', newValue)}
                            />
                          </TableCell>
                          <TableCell>
                            <EditableCell
                              value={item.stockEntrant}
                              onSave={(newValue) => updateItem(item.id, 'stockEntrant', newValue)}
                            />
                          </TableCell>
                          <TableCell className="font-medium">{item.stockTotal}</TableCell>
                          <TableCell>
                            <EditableCell
                              value={item.consommation}
                              onSave={(newValue) => updateItem(item.id, 'consommation', newValue)}
                            />
                          </TableCell>
                          <TableCell>
                            <span className={`font-medium ${
                              item.stockRestant === 0 ? 'text-red-600' :
                              item.stockRestant < 10 ? 'text-orange-600' : 'text-green-600'
                            }`}>
                              {item.stockRestant}
                            </span>
                          </TableCell>
                          <TableCell>
                            <EditableCell
                              value={item.prixVenteUnitaire}
                              onSave={(newValue) => updateItem(item.id, 'prixVenteUnitaire', newValue)}
                              type="currency"
                            />
                          </TableCell>
                          <TableCell>
                            <EditableCell
                              value={item.stockVendu}
                              onSave={(newValue) => updateItem(item.id, 'stockVendu', newValue)}
                            />
                          </TableCell>
                          <TableCell className="font-medium text-green-600">
                            {formatCurrency(item.benefice)}
                          </TableCell>
                          <TableCell>
                            {item.stockRestant === 0 ? (
                              <Badge variant="destructive">Rupture</Badge>
                            ) : item.stockRestant < 10 ? (
                              <Badge variant="secondary">Stock faible</Badge>
                            ) : (
                              <Badge variant="default">OK</Badge>
                            )}
                          </TableCell>
                        </TableRow>
                      ))}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="liqueurs" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Liqueurs</CardTitle>
              <CardDescription>
                Suivi des spiritueux et liqueurs
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Produit</TableHead>
                      <TableHead>Stock Initial</TableHead>
                      <TableHead>Stock Restant</TableHead>
                      <TableHead>P.V.U</TableHead>
                      <TableHead>Stock Vendu</TableHead>
                      <TableHead>Bénéfice</TableHead>
                      <TableHead>Statut</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {reportData
                      .filter(item => item.category === 'Liqueurs')
                      .map(item => (
                        <TableRow key={item.id}>
                          <TableCell className="font-medium">{item.produit}</TableCell>
                          <TableCell>{item.stockInitial}</TableCell>
                          <TableCell>
                            <span className={`font-medium ${
                              item.stockRestant === 0 ? 'text-red-600' : 'text-green-600'
                            }`}>
                              {item.stockRestant}
                            </span>
                          </TableCell>
                          <TableCell>{formatCurrency(item.prixVenteUnitaire)}</TableCell>
                          <TableCell>{item.stockVendu}</TableCell>
                          <TableCell className="font-medium text-green-600">
                            {formatCurrency(item.benefice)}
                          </TableCell>
                          <TableCell>
                            {item.stockVendu === 0 ? (
                              <Badge variant="outline">Pas de vente</Badge>
                            ) : (
                              <Badge variant="default">Vendu</Badge>
                            )}
                          </TableCell>
                        </TableRow>
                      ))}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Section IA et Recommandations */}
      {reportData.length > 0 && (
        <div className="mt-8">
          <AIRecommendations
            reportData={reportData}
            onActionTaken={(action) => {
              toast({
                title: "Action IA",
                description: `Recommandation "${action.title}" notée pour ${action.productName}`,
              });
            }}
          />
        </div>
      )}
    </div>
  );
};

export default DailyReport;
