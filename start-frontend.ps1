# Script PowerShell pour démarrer le frontend React
Write-Host "🚀 Démarrage du frontend BarStockWise..." -ForegroundColor Green

# Vérifier si Node.js est installé
try {
    $nodeVersion = node --version 2>&1
    Write-Host "✅ Node.js détecté: $nodeVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ Node.js n'est pas installé ou non accessible" -ForegroundColor Red
    Write-Host "📥 Veuillez installer Node.js depuis https://nodejs.org/" -ForegroundColor Yellow
    exit 1
}

# Vérifier si npm est installé
try {
    $npmVersion = npm --version 2>&1
    Write-Host "✅ npm détecté: $npmVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ npm n'est pas installé" -ForegroundColor Red
    exit 1
}

# Vérifier si les node_modules existent
if (Test-Path "node_modules") {
    Write-Host "✅ Dépendances détectées" -ForegroundColor Green
} else {
    Write-Host "📦 Installation des dépendances..." -ForegroundColor Yellow
    npm install
}

# Vérifier si le fichier .env existe
if (Test-Path ".env") {
    Write-Host "✅ Configuration .env détectée" -ForegroundColor Green
} else {
    Write-Host "⚠️ Fichier .env manquant, copie depuis .env.example..." -ForegroundColor Yellow
    if (Test-Path ".env.example") {
        Copy-Item ".env.example" ".env"
        Write-Host "✅ Fichier .env créé" -ForegroundColor Green
    } else {
        Write-Host "❌ Fichier .env.example manquant" -ForegroundColor Red
    }
}

# Afficher les informations de configuration
Write-Host "`n📋 Configuration:" -ForegroundColor Cyan
Write-Host "   Frontend: http://localhost:5173" -ForegroundColor White
Write-Host "   Backend API: http://localhost:8000/api" -ForegroundColor White
Write-Host "   WebSocket: ws://localhost:8000/ws" -ForegroundColor White

Write-Host "`n🔧 Assurez-vous que le backend Django est démarré avant d'utiliser l'application" -ForegroundColor Yellow
Write-Host "🛑 Appuyez sur Ctrl+C pour arrêter le serveur" -ForegroundColor Yellow

# Démarrer le serveur de développement
Write-Host "`n🌐 Démarrage du serveur de développement..." -ForegroundColor Green
npm run dev
