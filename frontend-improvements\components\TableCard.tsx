import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  Users, Clock, MapPin, DollarSign, 
  Play, Square, Trash2, Settings 
} from 'lucide-react';
import { Table } from '../api/tables';
import { cn } from '@/lib/utils';

interface TableCardProps {
  table: Table;
  onOccupy: (id: number) => void;
  onFree: (id: number) => void;
  onEdit: (table: Table) => void;
  onViewSale?: (saleId: number) => void;
  className?: string;
}

export function TableCard({ 
  table, 
  onOccupy, 
  onFree, 
  onEdit, 
  onViewSale,
  className 
}: TableCardProps) {
  
  const getStatusColor = (status: Table['status']) => {
    switch (status) {
      case 'available':
        return 'bg-green-50 border-green-200 hover:bg-green-100';
      case 'occupied':
        return 'bg-red-50 border-red-200 hover:bg-red-100';
      case 'reserved':
        return 'bg-blue-50 border-blue-200 hover:bg-blue-100';
      case 'cleaning':
        return 'bg-yellow-50 border-yellow-200 hover:bg-yellow-100';
      default:
        return 'bg-gray-50 border-gray-200 hover:bg-gray-100';
    }
  };

  const getStatusBadge = (status: Table['status']) => {
    const variants = {
      available: 'bg-green-500',
      occupied: 'bg-red-500',
      reserved: 'bg-blue-500',
      cleaning: 'bg-yellow-500',
    };

    const labels = {
      available: 'Libre',
      occupied: 'Occupée',
      reserved: 'Réservée',
      cleaning: 'Nettoyage',
    };

    return (
      <Badge className={cn('text-white', variants[status])}>
        {labels[status]}
      </Badge>
    );
  };

  const formatDuration = (minutes: number) => {
    if (minutes < 60) return `${minutes}min`;
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return `${hours}h${mins > 0 ? ` ${mins}min` : ''}`;
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency: 'BIF',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  return (
    <Card className={cn(
      'transition-all duration-200 cursor-pointer',
      getStatusColor(table.status),
      className
    )}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg font-semibold">
            Table {table.number}
          </CardTitle>
          {getStatusBadge(table.status)}
        </div>
        
        <div className="flex items-center gap-4 text-sm text-muted-foreground">
          <div className="flex items-center gap-1">
            <Users className="w-4 h-4" />
            <span>{table.capacity} places</span>
          </div>
          
          <div className="flex items-center gap-1">
            <MapPin className="w-4 h-4" />
            <span>{table.location}</span>
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-3">
        {/* Informations d'occupation */}
        {table.is_occupied && table.occupation_duration > 0 && (
          <div className="flex items-center gap-2 p-2 bg-orange-50 rounded-lg">
            <Clock className="w-4 h-4 text-orange-600" />
            <span className="text-sm text-orange-800">
              Occupée depuis {formatDuration(table.occupation_duration)}
            </span>
          </div>
        )}

        {/* Vente en cours */}
        {table.current_sale && (
          <div className="p-3 bg-background/50 rounded-lg border">
            <div className="flex justify-between items-center mb-2">
              <span className="font-medium text-sm">Commande en cours</span>
              <Badge variant="outline" className="text-xs">
                {table.current_sale.status}
              </Badge>
            </div>
            
            <div className="space-y-1 text-sm">
              <div className="flex justify-between">
                <span className="text-muted-foreground">Client:</span>
                <span>{table.current_sale.customer_name}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">Montant:</span>
                <span className="font-medium">
                  {formatCurrency(table.current_sale.total_amount)}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">Référence:</span>
                <span className="text-xs font-mono">
                  {table.current_sale.reference}
                </span>
              </div>
            </div>
            
            {onViewSale && (
              <Button
                variant="outline"
                size="sm"
                className="w-full mt-2"
                onClick={() => onViewSale(table.current_sale!.id)}
              >
                <DollarSign className="w-4 h-4 mr-1" />
                Voir la commande
              </Button>
            )}
          </div>
        )}

        {/* Notes */}
        {table.notes && (
          <div className="p-2 bg-blue-50 rounded-lg">
            <p className="text-sm text-blue-800">{table.notes}</p>
          </div>
        )}

        {/* Actions */}
        <div className="flex gap-2 pt-2">
          {table.status === 'available' && (
            <Button
              onClick={() => onOccupy(table.id)}
              className="flex-1"
              size="sm"
            >
              <Play className="w-4 h-4 mr-1" />
              Occuper
            </Button>
          )}
          
          {table.status === 'occupied' && (
            <Button
              onClick={() => onFree(table.id)}
              variant="outline"
              className="flex-1"
              size="sm"
            >
              <Square className="w-4 h-4 mr-1" />
              Libérer
            </Button>
          )}
          
          {table.status === 'cleaning' && (
            <Button
              onClick={() => onFree(table.id)}
              variant="outline"
              className="flex-1"
              size="sm"
            >
              Terminer nettoyage
            </Button>
          )}
          
          <Button
            onClick={() => onEdit(table)}
            variant="ghost"
            size="sm"
          >
            <Settings className="w-4 h-4" />
          </Button>
        </div>

        {/* Dernière activité */}
        {table.last_cleaned && (
          <div className="text-xs text-muted-foreground pt-2 border-t">
            Dernier nettoyage: {new Date(table.last_cleaned).toLocaleString('fr-FR')}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
