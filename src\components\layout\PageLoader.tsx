import React, { Suspense } from 'react';
import LoadingSpinner from '@/components/ui/loading-spinner';

interface PageLoaderProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
}

const PageLoader: React.FC<PageLoaderProps> = ({ 
  children, 
  fallback 
}) => {
  const defaultFallback = (
    <div className="flex items-center justify-center min-h-[400px]">
      <LoadingSpinner size="lg" text="Chargement de la page..." />
    </div>
  );

  return (
    <Suspense fallback={fallback || defaultFallback}>
      {children}
    </Suspense>
  );
};

export default PageLoader;
