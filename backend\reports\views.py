from django.http import HttpResponse
from rest_framework import generics, status, permissions
from rest_framework.decorators import api_view, permission_classes, action
from rest_framework.response import Response
from rest_framework.views import APIView
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework.filters import SearchFilter, OrderingFilter
from django.db.models import Sum, Count, Avg, Max, F, Q
from django.utils import timezone
from datetime import datetime, timedelta
from decimal import Decimal
from .models import DailyReport, StockAlert
from .serializers import (
    DailyReportSerializer, DailyReportCreateSerializer,
    StockAlertSerializer, StockAlertCreateSerializer,
    ReportSummarySerializer
)
from .pdf_generator import PDFReportGenerator
from .excel_generator import ExcelReportGenerator
from products.models import Product
from sales.models import Sale, SaleItem
from expenses.models import Expense
from accounts.permissions import IsAdminOrGerant, IsAuthenticated

class DailyReportListCreateView(generics.ListCreateAPIView):
    """
    Vue pour lister et créer des rapports quotidiens
    """
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ['date', 'user']
    search_fields = ['notes']
    ordering_fields = ['date', 'total_sales', 'created_at']
    ordering = ['-date']

    def get_queryset(self):
        if not self.request.user.can_generate_reports():
            return DailyReport.objects.none()

        return DailyReport.objects.select_related('user')

    def get_serializer_class(self):
        if self.request.method == 'POST':
            return DailyReportCreateSerializer
        return DailyReportSerializer

    def perform_create(self, serializer):
        if not self.request.user.can_generate_reports():
            raise permissions.PermissionDenied("Permission insuffisante pour créer des rapports.")
        serializer.save()

class DailyReportDetailView(generics.RetrieveUpdateDestroyAPIView):
    """
    Vue pour récupérer, modifier ou supprimer un rapport quotidien
    """
    serializer_class = DailyReportSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        if not self.request.user.can_generate_reports():
            return DailyReport.objects.none()

        return DailyReport.objects.select_related('user')

    def perform_update(self, serializer):
        if not self.request.user.can_generate_reports():
            raise permissions.PermissionDenied("Permission insuffisante pour modifier des rapports.")
        serializer.save()

    def perform_destroy(self, instance):
        if not self.request.user.can_delete_records():
            raise permissions.PermissionDenied("Permission insuffisante pour supprimer des rapports.")
        instance.delete()

class StockAlertListCreateView(generics.ListCreateAPIView):
    """
    Vue pour lister et créer des alertes de stock
    """
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ['alert_type', 'status', 'product__category']
    search_fields = ['product__name', 'message']
    ordering_fields = ['created_at', 'product__name']
    ordering = ['-created_at']

    def get_queryset(self):
        if not self.request.user.can_view_stock_alerts():
            return StockAlert.objects.none()

        return StockAlert.objects.select_related('product', 'product__category')

    def get_serializer_class(self):
        if self.request.method == 'POST':
            return StockAlertCreateSerializer
        return StockAlertSerializer

    def perform_create(self, serializer):
        if not self.request.user.can_manage_inventory():
            raise permissions.PermissionDenied("Permission insuffisante pour créer des alertes.")
        serializer.save()

class StockAlertDetailView(generics.RetrieveUpdateDestroyAPIView):
    """
    Vue pour récupérer, modifier ou supprimer une alerte de stock
    """
    serializer_class = StockAlertSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        if not self.request.user.can_view_stock_alerts():
            return StockAlert.objects.none()

        return StockAlert.objects.select_related('product', 'product__category')

    def perform_update(self, serializer):
        if not self.request.user.can_manage_inventory():
            raise permissions.PermissionDenied("Permission insuffisante pour modifier des alertes.")
        serializer.save()

    def perform_destroy(self, instance):
        if not self.request.user.can_delete_records():
            raise permissions.PermissionDenied("Permission insuffisante pour supprimer des alertes.")
        instance.delete()

@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def resolve_stock_alert(request, pk):
    """
    Vue pour résoudre une alerte de stock
    """
    if not request.user.can_manage_inventory():
        return Response(
            {'error': 'Permission insuffisante pour résoudre des alertes.'},
            status=status.HTTP_403_FORBIDDEN
        )

    try:
        alert = StockAlert.objects.get(pk=pk)
    except StockAlert.DoesNotExist:
        return Response(
            {'error': 'Alerte introuvable.'},
            status=status.HTTP_404_NOT_FOUND
        )

    if alert.status == 'resolved':
        return Response(
            {'error': 'Cette alerte est déjà résolue.'},
            status=status.HTTP_400_BAD_REQUEST
        )

    alert.status = 'resolved'
    alert.save()

    return Response({
        'message': 'Alerte résolue avec succès.',
        'alert': StockAlertSerializer(alert).data
    })

@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def generate_stock_alerts(request):
    """
    Vue pour générer automatiquement les alertes de stock
    """
    if not request.user.can_manage_inventory():
        return Response(
            {'error': 'Permission insuffisante pour générer des alertes.'},
            status=status.HTTP_403_FORBIDDEN
        )

    alerts_created = 0

    # Produits en rupture de stock
    out_of_stock_products = Product.objects.filter(
        current_stock=0,
        is_active=True
    )

    for product in out_of_stock_products:
        # Vérifier qu'il n'y a pas déjà une alerte
        if not StockAlert.objects.filter(
            product=product,
            alert_type='out_of_stock',
            status='active'
        ).exists():
            StockAlert.objects.create(
                product=product,
                alert_type='out_of_stock',
                message=f"Le produit {product.name} est en rupture de stock."
            )
            alerts_created += 1

    # Produits avec stock faible
    from django.db import models
    low_stock_products = Product.objects.filter(
        current_stock__lte=models.F('minimum_stock'),
        current_stock__gt=0,
        is_active=True
    )

    for product in low_stock_products:
        # Vérifier qu'il n'y a pas déjà une alerte
        if not StockAlert.objects.filter(
            product=product,
            alert_type='low_stock',
            status='active'
        ).exists():
            StockAlert.objects.create(
                product=product,
                alert_type='low_stock',
                message=f"Le stock du produit {product.name} est faible: {product.current_stock}/{product.minimum_stock}."
            )
            alerts_created += 1

    return Response({
        'message': f'{alerts_created} nouvelles alertes créées.',
        'alerts_created': alerts_created
    })


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def reports_summary(request):
    """
    Vue pour récupérer un résumé des rapports
    """
    if not request.user.can_generate_reports():
        return Response(
            {'error': 'Permission insuffisante pour voir les résumés de rapports.'},
            status=status.HTTP_403_FORBIDDEN
        )

    # Période par défaut: 30 derniers jours
    end_date = timezone.now().date()
    start_date = end_date - timedelta(days=30)

    # Paramètres de période
    period_start = request.query_params.get('start_date', start_date)
    period_end = request.query_params.get('end_date', end_date)

    if isinstance(period_start, str):
        try:
            period_start = datetime.strptime(period_start, '%Y-%m-%d').date()
        except ValueError:
            period_start = start_date

    if isinstance(period_end, str):
        try:
            period_end = datetime.strptime(period_end, '%Y-%m-%d').date()
        except ValueError:
            period_end = end_date

    # Requête des rapports
    reports = DailyReport.objects.filter(
        date__gte=period_start,
        date__lte=period_end
    )

    # Calculs
    total_reports = reports.count()

    aggregates = reports.aggregate(
        total_sales=Sum('total_sales'),
        total_revenue=Sum('total_sales'),
        avg_daily_sales=Avg('total_sales'),
        avg_daily_revenue=Avg('total_sales'),
        max_revenue=Max('total_sales')
    )

    # Meilleur jour
    best_day_report = reports.filter(
        total_sales=aggregates['max_revenue']
    ).first()

    best_day = best_day_report.date if best_day_report else None
    best_day_revenue = aggregates['max_revenue'] or 0

    # Alertes
    alerts = StockAlert.objects.filter(
        created_at__date__gte=period_start,
        created_at__date__lte=period_end
    )

    total_alerts = alerts.count()
    unresolved_alerts = alerts.filter(status='active').count()

    # Préparer les données
    summary_data = {
        'period_start': period_start,
        'period_end': period_end,
        'total_reports': total_reports,
        'total_sales': aggregates['total_sales'] or 0,
        'total_revenue': aggregates['total_revenue'] or 0,
        'average_daily_sales': round(aggregates['avg_daily_sales'] or 0, 2),
        'average_daily_revenue': round(aggregates['avg_daily_revenue'] or 0, 2),
        'best_day': best_day,
        'best_day_revenue': best_day_revenue,
        'total_alerts': total_alerts,
        'unresolved_alerts': unresolved_alerts
    }

    serializer = ReportSummarySerializer(summary_data)
    return Response(serializer.data)


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def dashboard_stats(request):
    """
    Vue pour les statistiques du tableau de bord
    """
    if not request.user.can_view_sales_history():
        return Response(
            {'error': 'Permission insuffisante pour voir les statistiques.'},
            status=status.HTTP_403_FORBIDDEN
        )

    today = timezone.now().date()

    # Rapport du jour
    today_report = DailyReport.objects.filter(date=today).first()

    # Alertes non résolues
    unresolved_alerts = StockAlert.objects.filter(status='active').count()

    # Produits en rupture
    out_of_stock_count = Product.objects.filter(
        current_stock=0,
        is_active=True
    ).count()

    # Produits avec stock faible
    from django.db import models
    low_stock_count = Product.objects.filter(
        current_stock__lte=models.F('minimum_stock'),
        current_stock__gt=0,
        is_active=True
    ).count()

    # Ventes en attente (si module sales disponible)
    try:
        from sales.models import Sale
        pending_sales = Sale.objects.filter(status='pending').count()
    except ImportError:
        pending_sales = 0

    return Response({
        'today': {
            'date': today,
            'sales': today_report.total_sales if today_report else 0,
            'revenue': today_report.total_sales if today_report else 0,
            'pending_sales': pending_sales
        },
        'alerts': {
            'total_unresolved': unresolved_alerts,
            'out_of_stock': out_of_stock_count,
            'low_stock': low_stock_count
        },
        'quick_stats': {
            'active_products': Product.objects.filter(is_active=True).count(),
            'total_categories': Product.objects.values('category').distinct().count()
        }
    })

# Vues d'export PDF et Excel
class ExportDailyReportPDFView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request, date_str):
        """Exporter un rapport quotidien en PDF"""
        try:
            report_date = datetime.strptime(date_str, '%Y-%m-%d').date()
        except ValueError:
            return Response(
                {'error': 'Format de date invalide. Utilisez YYYY-MM-DD.'},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            daily_report = DailyReport.objects.get(date=report_date)
        except DailyReport.DoesNotExist:
            return Response(
                {'error': 'Rapport quotidien non trouvé pour cette date.'},
                status=status.HTTP_404_NOT_FOUND
            )

        # Récupérer les données de ventes
        sales_data = SaleItem.objects.filter(
            sale__created_at__date=report_date
        ).values(
            'product__name', 'quantity', 'unit_price', 'total_price'
        ).annotate(
            product_name=F('product__name'),
            total_amount=F('total_price')
        )

        # Récupérer les alertes de stock
        stock_alerts = StockAlert.objects.filter(
            status='active',
            created_at__date=report_date
        ).select_related('product', 'product__category')

        # Générer le PDF
        pdf_generator = PDFReportGenerator()
        pdf_buffer = pdf_generator.generate_daily_report_pdf(
            daily_report, sales_data, stock_alerts
        )

        # Créer la réponse HTTP
        response = HttpResponse(pdf_buffer.getvalue(), content_type='application/pdf')
        response['Content-Disposition'] = f'attachment; filename="rapport_quotidien_{date_str}.pdf"'

        return response

class ExportDailyReportExcelView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request, date_str):
        """Exporter un rapport quotidien en Excel"""
        try:
            report_date = datetime.strptime(date_str, '%Y-%m-%d').date()
        except ValueError:
            return Response(
                {'error': 'Format de date invalide. Utilisez YYYY-MM-DD.'},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            daily_report = DailyReport.objects.get(date=report_date)
        except DailyReport.DoesNotExist:
            return Response(
                {'error': 'Rapport quotidien non trouvé pour cette date.'},
                status=status.HTTP_404_NOT_FOUND
            )

        # Récupérer les données de ventes
        sales_data = SaleItem.objects.filter(
            sale__created_at__date=report_date
        ).values(
            'product__name', 'product__category__name', 'quantity', 'unit_price', 'total_price'
        ).annotate(
            product_name=F('product__name'),
            category_name=F('product__category__name'),
            total_amount=F('total_price')
        )

        # Récupérer les alertes de stock
        stock_alerts = StockAlert.objects.filter(
            status='active',
            created_at__date=report_date
        ).select_related('product', 'product__category')

        # Générer l'Excel
        excel_generator = ExcelReportGenerator()
        excel_buffer = excel_generator.generate_daily_report_excel(
            daily_report, sales_data, stock_alerts
        )

        # Créer la réponse HTTP
        response = HttpResponse(
            excel_buffer.getvalue(),
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        response['Content-Disposition'] = f'attachment; filename="rapport_quotidien_{date_str}.xlsx"'

        return response

class ExportStockReportPDFView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        """Exporter un rapport de stock en PDF"""
        products = Product.objects.filter(is_active=True).select_related('category')

        # Générer le PDF
        pdf_generator = PDFReportGenerator()
        pdf_buffer = pdf_generator.generate_stock_report_pdf(products)

        # Créer la réponse HTTP
        response = HttpResponse(pdf_buffer.getvalue(), content_type='application/pdf')
        today_str = timezone.now().strftime('%Y-%m-%d')
        response['Content-Disposition'] = f'attachment; filename="rapport_stock_{today_str}.pdf"'

        return response

class ExportStockReportExcelView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        """Exporter un rapport de stock en Excel"""
        products = Product.objects.filter(is_active=True).select_related('category')

        # Générer l'Excel
        excel_generator = ExcelReportGenerator()
        excel_buffer = excel_generator.generate_stock_report_excel(products)

        # Créer la réponse HTTP
        response = HttpResponse(
            excel_buffer.getvalue(),
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        today_str = timezone.now().strftime('%Y-%m-%d')
        response['Content-Disposition'] = f'attachment; filename="rapport_stock_{today_str}.xlsx"'

        return response

class ExportSalesReportPDFView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        """Exporter un rapport de ventes en PDF"""
        start_date_str = request.query_params.get('start_date')
        end_date_str = request.query_params.get('end_date')

        if not start_date_str or not end_date_str:
            return Response(
                {'error': 'Les paramètres start_date et end_date sont requis.'},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            start_date = datetime.strptime(start_date_str, '%Y-%m-%d').date()
            end_date = datetime.strptime(end_date_str, '%Y-%m-%d').date()
        except ValueError:
            return Response(
                {'error': 'Format de date invalide. Utilisez YYYY-MM-DD.'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Récupérer les ventes
        sales = Sale.objects.filter(
            created_at__date__range=[start_date, end_date]
        ).select_related('table', 'server').prefetch_related('items__product')

        # Générer l'Excel (réutiliser la fonction Excel pour les ventes)
        excel_generator = ExcelReportGenerator()
        excel_buffer = excel_generator.generate_sales_report_excel(sales, start_date, end_date)

        # Créer la réponse HTTP
        response = HttpResponse(
            excel_buffer.getvalue(),
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        response['Content-Disposition'] = f'attachment; filename="rapport_ventes_{start_date_str}_{end_date_str}.xlsx"'

        return response

# APIs pour les notifications en temps réel
class NotificationStatusView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        """Obtenir le statut des notifications pour l'utilisateur"""
        user = request.user

        # Compter les alertes non lues
        unread_alerts = StockAlert.objects.filter(
            status='active'
        ).count() if user.role in ['admin', 'gerant'] else 0

        # Ventes en attente (pour les gérants et admins)
        pending_sales = 0
        if user.role in ['admin', 'gerant']:
            from sales.models import Sale
            pending_sales = Sale.objects.filter(status='pending').count()

        return Response({
            'user_id': user.id,
            'role': user.role,
            'unread_alerts': unread_alerts,
            'pending_sales': pending_sales,
            'websocket_url': f'ws://localhost:8000/ws/notifications/{user.id}/',
            'can_receive_stock_alerts': user.role in ['admin', 'gerant'],
            'can_receive_sale_notifications': user.role in ['admin', 'gerant']
        })

class TriggerStockCheckView(APIView):
    permission_classes = [IsAdminOrGerant]

    def post(self, request):
        """Déclencher manuellement une vérification des stocks"""
        from .notifications import NotificationService

        try:
            NotificationService.check_and_send_stock_alerts()
            NotificationService.update_dashboard_stats()

            return Response({
                'success': True,
                'message': 'Vérification des stocks effectuée avec succès'
            })
        except Exception as e:
            return Response({
                'success': False,
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

class SendTestNotificationView(APIView):
    permission_classes = [IsAdminOrGerant]

    def post(self, request):
        """Envoyer une notification de test"""
        from .notifications import NotificationService

        message = request.data.get('message', 'Notification de test')
        level = request.data.get('level', 'info')
        target_roles = request.data.get('target_roles', ['admin', 'gerant'])

        try:
            NotificationService.send_system_notification(
                message=message,
                level=level,
                target_roles=target_roles
            )

            return Response({
                'success': True,
                'message': 'Notification de test envoyée'
            })
        except Exception as e:
            return Response({
                'success': False,
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
