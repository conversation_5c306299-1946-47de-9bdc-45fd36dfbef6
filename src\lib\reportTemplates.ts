import { DailyStockItem } from '@/types/dailyReport';

// Templates de produits prédéfinis pour faciliter la saisie
export interface ProductTemplate {
  id: string;
  produit: string;
  category: 'Bières Brarudi' | 'Liqueurs' | 'Autres Boissons';
  prixCasier: number;
  prixAchatUnitaire: number;
  prixVenteUnitaire: number;
  uniteParCasier: number;
  seuilStockFaible: number;
  isActive: boolean;
}

// Templates basés sur vos données réelles
export const PRODUCT_TEMPLATES: ProductTemplate[] = [
  // Bières Brarudi
  {
    id: 'fanta',
    produit: 'FANTA',
    category: 'Bières Brarudi',
    prixCasier: 32400,
    prixAchatUnitaire: 1350,
    prixVenteUnitaire: 3000,
    uniteParCasier: 24,
    seuilStockFaible: 10,
    isActive: true
  },
  {
    id: 'primus',
    produit: 'PRIMUS',
    category: 'Bières Brarudi',
    prixCasier: 26600,
    prixAchatUnitaire: 2217,
    prixVenteUnitaire: 5000,
    uniteParCasier: 12,
    seuilStockFaible: 8,
    isActive: true
  },
  {
    id: 'amstel',
    produit: 'AMSTEL',
    category: 'Bières Brarudi',
    prixCasier: 36550,
    prixAchatUnitaire: 3046,
    prixVenteUnitaire: 6000,
    uniteParCasier: 12,
    seuilStockFaible: 5,
    isActive: true
  },
  {
    id: 'mutzig',
    produit: 'MUTZIG',
    category: 'Bières Brarudi',
    prixCasier: 26600,
    prixAchatUnitaire: 2217,
    prixVenteUnitaire: 5000,
    uniteParCasier: 12,
    seuilStockFaible: 8,
    isActive: true
  },
  {
    id: 'stella',
    produit: 'STELLA ARTOIS',
    category: 'Bières Brarudi',
    prixCasier: 36550,
    prixAchatUnitaire: 3046,
    prixVenteUnitaire: 6000,
    uniteParCasier: 12,
    seuilStockFaible: 5,
    isActive: true
  },
  
  // Liqueurs
  {
    id: 'chivas',
    produit: 'CHIVAS (shot/gode)',
    category: 'Liqueurs',
    prixCasier: 240000,
    prixAchatUnitaire: 13333,
    prixVenteUnitaire: 20000,
    uniteParCasier: 18,
    seuilStockFaible: 3,
    isActive: true
  },
  {
    id: 'johnnie_walker',
    produit: 'JOHNNIE WALKER (shot)',
    category: 'Liqueurs',
    prixCasier: 180000,
    prixAchatUnitaire: 10000,
    prixVenteUnitaire: 15000,
    uniteParCasier: 18,
    seuilStockFaible: 3,
    isActive: true
  },
  {
    id: 'baileys',
    produit: 'BAILEYS (shot)',
    category: 'Liqueurs',
    prixCasier: 200000,
    prixAchatUnitaire: 11111,
    prixVenteUnitaire: 18000,
    uniteParCasier: 18,
    seuilStockFaible: 3,
    isActive: true
  },
  {
    id: 'vodka',
    produit: 'VODKA (shot)',
    category: 'Liqueurs',
    prixCasier: 120000,
    prixAchatUnitaire: 6667,
    prixVenteUnitaire: 12000,
    uniteParCasier: 18,
    seuilStockFaible: 3,
    isActive: true
  },

  // Autres boissons
  {
    id: 'coca_cola',
    produit: 'COCA-COLA',
    category: 'Autres Boissons',
    prixCasier: 32400,
    prixAchatUnitaire: 1350,
    prixVenteUnitaire: 2500,
    uniteParCasier: 24,
    seuilStockFaible: 12,
    isActive: true
  },
  {
    id: 'sprite',
    produit: 'SPRITE',
    category: 'Autres Boissons',
    prixCasier: 32400,
    prixAchatUnitaire: 1350,
    prixVenteUnitaire: 2500,
    uniteParCasier: 24,
    seuilStockFaible: 12,
    isActive: true
  },
  {
    id: 'eau_minerale',
    produit: 'EAU MINÉRALE',
    category: 'Autres Boissons',
    prixCasier: 18000,
    prixAchatUnitaire: 750,
    prixVenteUnitaire: 1500,
    uniteParCasier: 24,
    seuilStockFaible: 20,
    isActive: true
  }
];

// Fonction pour créer un item de rapport à partir d'un template
export const createItemFromTemplate = (template: ProductTemplate, stockInitial: number = 0, stockEntrant: number = 0): DailyStockItem => {
  const stockTotal = stockInitial + stockEntrant;
  const marge = template.prixVenteUnitaire - template.prixAchatUnitaire;
  
  return {
    id: `item-${template.id}-${Date.now()}`,
    produit: template.produit,
    category: template.category,
    prixCasier: template.prixCasier,
    stockInitial,
    stockEntrant,
    stockTotal,
    consommation: 0,
    perte: 0,
    stockRestant: stockTotal,
    prixAchatUnitaire: template.prixAchatUnitaire,
    prixVenteUnitaire: template.prixVenteUnitaire,
    stockConsomme: 0,
    stockVendu: 0,
    marge,
    benefice: 0
  };
};

// Fonction pour obtenir les templates par catégorie
export const getTemplatesByCategory = (category: string): ProductTemplate[] => {
  return PRODUCT_TEMPLATES.filter(template => 
    template.category === category && template.isActive
  );
};

// Fonction pour rechercher un template par nom
export const findTemplateByName = (productName: string): ProductTemplate | undefined => {
  return PRODUCT_TEMPLATES.find(template => 
    template.produit.toLowerCase().includes(productName.toLowerCase()) ||
    productName.toLowerCase().includes(template.produit.toLowerCase())
  );
};

// Configuration des seuils par défaut selon la catégorie
export const DEFAULT_THRESHOLDS_BY_CATEGORY = {
  'Bières Brarudi': {
    stockFaibleSeuil: 15, // 15% du stock initial
    perteMaximale: 3, // 3% de perte acceptable
    margeMinimale: 1000 // 1000 BIF de marge minimale
  },
  'Liqueurs': {
    stockFaibleSeuil: 25, // 25% du stock initial (plus critique)
    perteMaximale: 2, // 2% de perte acceptable (plus strict)
    margeMinimale: 5000 // 5000 BIF de marge minimale
  },
  'Autres Boissons': {
    stockFaibleSeuil: 20, // 20% du stock initial
    perteMaximale: 5, // 5% de perte acceptable
    margeMinimale: 500 // 500 BIF de marge minimale
  }
};

// Fonction pour obtenir les seuils recommandés pour une catégorie
export const getRecommendedThresholds = (category: string) => {
  return DEFAULT_THRESHOLDS_BY_CATEGORY[category as keyof typeof DEFAULT_THRESHOLDS_BY_CATEGORY] || 
         DEFAULT_THRESHOLDS_BY_CATEGORY['Autres Boissons'];
};

// Template de rapport vide pour une nouvelle journée
export const createEmptyDailyReport = (date: string, selectedProducts: string[] = []): DailyStockItem[] => {
  const productsToInclude = selectedProducts.length > 0 
    ? PRODUCT_TEMPLATES.filter(template => selectedProducts.includes(template.id))
    : PRODUCT_TEMPLATES.filter(template => template.isActive);

  return productsToInclude.map(template => createItemFromTemplate(template));
};

// Validation des données d'import Excel
export const validateImportData = (data: any[]): { valid: boolean; errors: string[]; warnings: string[] } => {
  const errors: string[] = [];
  const warnings: string[] = [];
  
  if (!Array.isArray(data) || data.length === 0) {
    errors.push('Aucune donnée trouvée dans le fichier');
    return { valid: false, errors, warnings };
  }

  const requiredFields = ['produit', 'stockInitial', 'prixVenteUnitaire'];
  const firstRow = data[0];
  
  for (const field of requiredFields) {
    if (!(field in firstRow)) {
      errors.push(`Champ obligatoire manquant: ${field}`);
    }
  }

  // Vérifications sur chaque ligne
  data.forEach((row, index) => {
    const lineNumber = index + 1;
    
    if (!row.produit || row.produit.trim() === '') {
      errors.push(`Ligne ${lineNumber}: Nom du produit manquant`);
    }
    
    if (typeof row.stockInitial !== 'number' || row.stockInitial < 0) {
      errors.push(`Ligne ${lineNumber}: Stock initial invalide`);
    }
    
    if (typeof row.prixVenteUnitaire !== 'number' || row.prixVenteUnitaire <= 0) {
      errors.push(`Ligne ${lineNumber}: Prix de vente invalide`);
    }

    // Avertissements
    const template = findTemplateByName(row.produit);
    if (!template) {
      warnings.push(`Ligne ${lineNumber}: Produit "${row.produit}" non reconnu dans les templates`);
    }
  });

  return {
    valid: errors.length === 0,
    errors,
    warnings
  };
};
