from django.contrib import admin
from django.utils.html import format_html
from django.db.models import Su<PERSON>, Count, Avg
from django.urls import reverse
from .models import Recipe, RecipeIngredient, RecipeProduction


class RecipeIngredientInline(admin.TabularInline):
    """Inline pour les ingrédients de recette"""
    model = RecipeIngredient
    extra = 1
    fields = ('ingredient', 'quantity_needed', 'unit', 'waste_factor', 'is_optional', 'notes', 'order')
    readonly_fields = ('cost_display',)
    
    def cost_display(self, obj):
        """Affiche le coût de l'ingrédient"""
        if obj.id:
            return f"{obj.cost_per_recipe} BIF"
        return "-"
    cost_display.short_description = 'Coût'


@admin.register(Recipe)
class RecipeAdmin(admin.ModelAdmin):
    """Administration des recettes"""
    
    list_display = (
        'name', 'dish', 'portions_per_recipe', 'available_portions_display',
        'cost_per_portion_display', 'difficulty_badge', 'total_time_display', 'is_active'
    )
    list_filter = ('difficulty', 'is_active', 'dish__category', 'created_at')
    search_fields = ('name', 'dish__name', 'description')
    ordering = ('name',)
    
    fieldsets = (
        ('Informations générales', {
            'fields': ('dish', 'name', 'description', 'portions_per_recipe')
        }),
        ('Temps de préparation', {
            'fields': ('preparation_time', 'cooking_time', 'difficulty')
        }),
        ('Gestion des pertes', {
            'fields': ('waste_factor',)
        }),
        ('Statut', {
            'fields': ('is_active',)
        }),
        ('Informations système', {
            'fields': ('created_by', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    readonly_fields = ('created_at', 'updated_at')
    inlines = [RecipeIngredientInline]
    
    def available_portions_display(self, obj):
        """Affiche les portions disponibles avec couleur"""
        available = obj.available_portions()
        if available == 0:
            return format_html(
                '<span style="background-color: #dc3545; color: white; padding: 3px 8px; '
                'border-radius: 3px; font-size: 11px; font-weight: bold;">RUPTURE</span>'
            )
        elif available <= 5:
            return format_html(
                '<span style="background-color: #fd7e14; color: white; padding: 3px 8px; '
                'border-radius: 3px; font-size: 11px; font-weight: bold;">{} portions</span>',
                available
            )
        else:
            return format_html(
                '<span style="background-color: #198754; color: white; padding: 3px 8px; '
                'border-radius: 3px; font-size: 11px; font-weight: bold;">{} portions</span>',
                available
            )
    available_portions_display.short_description = 'Portions Disponibles'
    
    def cost_per_portion_display(self, obj):
        """Affiche le coût par portion"""
        cost = obj.cost_per_portion()
        suggested_price = obj.suggested_selling_price()
        return format_html(
            '{} BIF<br><small>Prix suggéré: {} BIF</small>',
            cost, suggested_price
        )
    cost_per_portion_display.short_description = 'Coût/Portion'
    
    def difficulty_badge(self, obj):
        """Affiche la difficulté avec un badge coloré"""
        colors = {
            'easy': '#198754',      # Vert
            'medium': '#fd7e14',    # Orange
            'hard': '#dc3545',      # Rouge
        }
        color = colors.get(obj.difficulty, '#6c757d')
        return format_html(
            '<span style="background-color: {}; color: white; padding: 3px 8px; '
            'border-radius: 3px; font-size: 11px; font-weight: bold;">{}</span>',
            color, obj.get_difficulty_display()
        )
    difficulty_badge.short_description = 'Difficulté'
    
    def total_time_display(self, obj):
        """Affiche le temps total"""
        return f"{obj.total_preparation_time} min"
    total_time_display.short_description = 'Temps Total'
    
    actions = ['calculate_costs', 'check_availability', 'mark_active', 'mark_inactive']
    
    def calculate_costs(self, request, queryset):
        """Recalcule les coûts des recettes"""
        for recipe in queryset:
            cost = recipe.cost_per_portion()
            self.message_user(request, f'{recipe.name}: {cost} BIF par portion')
    calculate_costs.short_description = "Calculer les coûts"
    
    def check_availability(self, request, queryset):
        """Vérifie la disponibilité des recettes"""
        for recipe in queryset:
            available = recipe.available_portions()
            self.message_user(request, f'{recipe.name}: {available} portions disponibles')
    check_availability.short_description = "Vérifier la disponibilité"
    
    def mark_active(self, request, queryset):
        """Active les recettes sélectionnées"""
        updated = queryset.update(is_active=True)
        self.message_user(request, f'{updated} recette(s) activée(s).')
    mark_active.short_description = "Activer les recettes"
    
    def mark_inactive(self, request, queryset):
        """Désactive les recettes sélectionnées"""
        updated = queryset.update(is_active=False)
        self.message_user(request, f'{updated} recette(s) désactivée(s).')
    mark_inactive.short_description = "Désactiver les recettes"
    
    def save_model(self, request, obj, form, change):
        """Définit automatiquement l'utilisateur créateur"""
        if not change:  # Nouveau objet
            obj.created_by = request.user
        super().save_model(request, obj, form, change)
    
    def get_queryset(self, request):
        """Optimise les requêtes"""
        return super().get_queryset(request).select_related('dish', 'created_by').prefetch_related('ingredients')


@admin.register(RecipeIngredient)
class RecipeIngredientAdmin(admin.ModelAdmin):
    """Administration des ingrédients de recette"""
    
    list_display = (
        'recipe', 'ingredient', 'quantity_needed', 'unit', 
        'waste_factor', 'cost_display', 'is_optional'
    )
    list_filter = ('is_optional', 'recipe__difficulty', 'ingredient__category')
    search_fields = ('recipe__name', 'ingredient__name')
    ordering = ('recipe', 'order', 'ingredient__name')
    
    def cost_display(self, obj):
        """Affiche le coût de l'ingrédient"""
        return f"{obj.cost_per_recipe} BIF"
    cost_display.short_description = 'Coût'
    
    def get_queryset(self, request):
        """Optimise les requêtes"""
        return super().get_queryset(request).select_related('recipe', 'ingredient')


@admin.register(RecipeProduction)
class RecipeProductionAdmin(admin.ModelAdmin):
    """Administration des productions de recettes"""
    
    list_display = (
        'recipe', 'portions_produced', 'actual_waste', 'production_cost',
        'produced_by', 'production_date'
    )
    list_filter = ('production_date', 'recipe', 'produced_by')
    search_fields = ('recipe__name', 'notes')
    ordering = ('-production_date',)
    
    fieldsets = (
        ('Production', {
            'fields': ('recipe', 'portions_produced', 'actual_waste')
        }),
        ('Coûts', {
            'fields': ('production_cost',)
        }),
        ('Informations', {
            'fields': ('produced_by', 'production_date', 'notes')
        }),
    )
    
    readonly_fields = ('production_date',)
    
    def save_model(self, request, obj, form, change):
        """Définit automatiquement l'utilisateur producteur"""
        if not change:  # Nouveau objet
            obj.produced_by = request.user
        super().save_model(request, obj, form, change)
    
    def get_queryset(self, request):
        """Optimise les requêtes"""
        return super().get_queryset(request).select_related('recipe', 'produced_by')
